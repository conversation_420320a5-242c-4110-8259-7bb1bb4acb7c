.class public Leu/chainfire/libsuperuser/Shell$Pool;
.super Ljava/lang/Object;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Pool"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
    }
.end annotation


# static fields
.field public static final SH:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

.field public static final SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

.field public static final defaultOnNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;

.field private static onNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private static pool:Ljava/util/Map;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map",
            "<",
            "Ljava/lang/String;",
            "Ljava/util/ArrayList",
            "<",
            "Leu/chainfire/libsuperuser/Shell$Threaded;",
            ">;>;"
        }
    .end annotation
.end field

.field private static poolSize:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .prologue
    .line 3196
    new-instance v0, Leu/chainfire/libsuperuser/Shell$Pool$1;

    move-object v2, v0

    move-object v0, v2

    move-object v1, v2

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Pool$1;-><init>()V

    sput-object v0, Leu/chainfire/libsuperuser/Shell$Pool;->defaultOnNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;

    .line 3209
    const/4 v0, 0x0

    sput-object v0, Leu/chainfire/libsuperuser/Shell$Pool;->onNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;

    .line 3211
    new-instance v0, Ljava/util/HashMap;

    move-object v2, v0

    move-object v0, v2

    move-object v1, v2

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    .line 3212
    const/4 v0, 0x4

    sput v0, Leu/chainfire/libsuperuser/Shell$Pool;->poolSize:I

    .line 3535
    const-string v0, "sh"

    invoke-static {v0}, Leu/chainfire/libsuperuser/Shell$Pool;->getWrapper(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-result-object v0

    sput-object v0, Leu/chainfire/libsuperuser/Shell$Pool;->SH:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    .line 3540
    const-string v0, "su"

    invoke-static {v0}, Leu/chainfire/libsuperuser/Shell$Pool;->getWrapper(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-result-object v0

    sput-object v0, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .prologue
    .line 3177
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$4600(Leu/chainfire/libsuperuser/Shell$Threaded;)V
    .locals 2

    .prologue
    .line 3177
    move-object v0, p0

    move-object v1, v0

    invoke-static {v1}, Leu/chainfire/libsuperuser/Shell$Pool;->releaseReservation(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    return-void
.end method

.method static synthetic access$4700(Leu/chainfire/libsuperuser/Shell$Threaded;)V
    .locals 2

    .prologue
    .line 3177
    move-object v0, p0

    move-object v1, v0

    invoke-static {v1}, Leu/chainfire/libsuperuser/Shell$Pool;->removeShell(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    return-void
.end method

.method private static cleanup(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V
    .locals 20
    .param p0    # Leu/chainfire/libsuperuser/Shell$Threaded;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .prologue
    .line 3326
    move-object/from16 v0, p0

    move/from16 v1, p1

    sget-object v13, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    invoke-interface {v13}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v13

    const/4 v14, 0x0

    new-array v14, v14, [Ljava/lang/String;

    invoke-interface {v13, v14}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v13

    check-cast v13, [Ljava/lang/String;

    move-object v2, v13

    move-object v13, v2

    array-length v13, v13

    move v3, v13

    const/4 v13, 0x0

    move v4, v13

    :goto_0
    move v13, v4

    move v14, v3

    if-ge v13, v14, :cond_a

    move-object v13, v2

    move v14, v4

    aget-object v13, v13, v14

    move-object v5, v13

    .line 3327
    sget-object v13, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v14, v5

    invoke-interface {v13, v14}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Ljava/util/ArrayList;

    move-object v6, v13

    .line 3328
    move-object v13, v6

    if-nez v13, :cond_1

    .line 3326
    :cond_0
    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 3330
    :cond_1
    move-object v13, v5

    invoke-static {v13}, Leu/chainfire/libsuperuser/Shell$SU;->isSU(Ljava/lang/String;)Z

    move-result v13

    if-eqz v13, :cond_5

    sget v13, Leu/chainfire/libsuperuser/Shell$Pool;->poolSize:I

    :goto_2
    move v7, v13

    .line 3331
    const/4 v13, 0x0

    move v8, v13

    .line 3332
    const/4 v13, 0x0

    move v9, v13

    .line 3334
    move-object v13, v6

    invoke-virtual {v13}, Ljava/util/ArrayList;->size()I

    move-result v13

    const/4 v14, 0x1

    add-int/lit8 v13, v13, -0x1

    move v10, v13

    :goto_3
    move v13, v10

    if-ltz v13, :cond_7

    .line 3335
    move-object v13, v6

    move v14, v10

    invoke-virtual {v13, v14}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v11, v13

    .line 3336
    move-object v13, v11

    invoke-virtual {v13}, Leu/chainfire/libsuperuser/Shell$Threaded;->isRunning()Z

    move-result v13

    if-eqz v13, :cond_2

    move-object v13, v11

    move-object v14, v0

    if-eq v13, v14, :cond_2

    move v13, v1

    if-eqz v13, :cond_6

    .line 3337
    :cond_2
    move v13, v1

    if-eqz v13, :cond_3

    move-object v13, v11

    invoke-virtual {v13}, Leu/chainfire/libsuperuser/Shell$Threaded;->closeWhenIdle()V

    .line 3338
    :cond_3
    const-string v13, "shell removed"

    invoke-static {v13}, Leu/chainfire/libsuperuser/Debug;->logPool(Ljava/lang/String;)V

    .line 3339
    move-object v13, v6

    move v14, v10

    invoke-virtual {v13, v14}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    move-result-object v13

    .line 3334
    :cond_4
    :goto_4
    add-int/lit8 v10, v10, -0x1

    goto :goto_3

    .line 3330
    :cond_5
    const/4 v13, 0x1

    goto :goto_2

    .line 3341
    :cond_6
    add-int/lit8 v8, v8, 0x1

    .line 3342
    move-object v13, v11

    invoke-static {v13}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5000(Leu/chainfire/libsuperuser/Shell$Threaded;)Z

    move-result v13

    if-nez v13, :cond_4

    .line 3343
    add-int/lit8 v9, v9, 0x1

    goto :goto_4

    .line 3348
    :cond_7
    move v13, v8

    move v14, v7

    if-le v13, v14, :cond_8

    move v13, v9

    const/4 v14, 0x1

    if-le v13, v14, :cond_8

    .line 3349
    move v13, v9

    const/4 v14, 0x1

    add-int/lit8 v13, v13, -0x1

    move v14, v8

    move v15, v7

    sub-int/2addr v14, v15

    invoke-static {v13, v14}, Ljava/lang/Math;->min(II)I

    move-result v13

    move v10, v13

    .line 3350
    move-object v13, v6

    invoke-virtual {v13}, Ljava/util/ArrayList;->size()I

    move-result v13

    const/4 v14, 0x1

    add-int/lit8 v13, v13, -0x1

    move v11, v13

    :goto_5
    move v13, v11

    if-ltz v13, :cond_8

    .line 3351
    move-object v13, v6

    move v14, v11

    invoke-virtual {v13, v14}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v12, v13

    .line 3352
    move-object v13, v12

    invoke-static {v13}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5000(Leu/chainfire/libsuperuser/Shell$Threaded;)Z

    move-result v13

    if-nez v13, :cond_9

    move-object v13, v12

    invoke-virtual {v13}, Leu/chainfire/libsuperuser/Shell$Threaded;->isIdle()Z

    move-result v13

    if-eqz v13, :cond_9

    .line 3353
    move-object v13, v6

    move v14, v11

    invoke-virtual {v13, v14}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    move-result-object v13

    .line 3354
    const-string v13, "shell killed"

    invoke-static {v13}, Leu/chainfire/libsuperuser/Debug;->logPool(Ljava/lang/String;)V

    .line 3356
    move-object v13, v12

    const/4 v14, 0x1

    invoke-static {v13, v14}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5100(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V

    .line 3357
    add-int/lit8 v10, v10, -0x1

    .line 3358
    move v13, v10

    if-nez v13, :cond_9

    .line 3364
    :cond_8
    move-object v13, v6

    invoke-virtual {v13}, Ljava/util/ArrayList;->size()I

    move-result v13

    if-nez v13, :cond_0

    .line 3365
    sget-object v13, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v14, v5

    invoke-interface {v13, v14}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    goto/16 :goto_1

    .line 3350
    :cond_9
    add-int/lit8 v11, v11, -0x1

    goto :goto_5

    .line 3369
    :cond_a
    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getDebug()Z

    move-result v13

    if-eqz v13, :cond_e

    .line 3370
    sget-object v13, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    invoke-interface {v13}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v13

    invoke-interface {v13}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v13

    move-object v2, v13

    :goto_6
    move-object v13, v2

    invoke-interface {v13}, Ljava/util/Iterator;->hasNext()Z

    move-result v13

    if-eqz v13, :cond_e

    move-object v13, v2

    invoke-interface {v13}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Ljava/lang/String;

    move-object v3, v13

    .line 3371
    const/4 v13, 0x0

    move v4, v13

    .line 3372
    sget-object v13, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v14, v3

    invoke-interface {v13, v14}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Ljava/util/ArrayList;

    move-object v5, v13

    .line 3373
    move-object v13, v5

    if-nez v13, :cond_b

    goto :goto_6

    .line 3374
    :cond_b
    const/4 v13, 0x0

    move v6, v13

    :goto_7
    move v13, v6

    move-object v14, v5

    invoke-virtual {v14}, Ljava/util/ArrayList;->size()I

    move-result v14

    if-ge v13, v14, :cond_d

    .line 3375
    move-object v13, v5

    move v14, v6

    invoke-virtual {v13, v14}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Leu/chainfire/libsuperuser/Shell$Threaded;

    invoke-static {v13}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5000(Leu/chainfire/libsuperuser/Shell$Threaded;)Z

    move-result v13

    if-eqz v13, :cond_c

    add-int/lit8 v4, v4, 0x1

    .line 3374
    :cond_c
    add-int/lit8 v6, v6, 0x1

    goto :goto_7

    .line 3377
    :cond_d
    sget-object v13, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v14, "cleanup: shell:%s count:%d reserved:%d"

    const/4 v15, 0x3

    new-array v15, v15, [Ljava/lang/Object;

    move-object/from16 v19, v15

    move-object/from16 v15, v19

    move-object/from16 v16, v19

    const/16 v17, 0x0

    move-object/from16 v18, v3

    aput-object v18, v16, v17

    move-object/from16 v19, v15

    move-object/from16 v15, v19

    move-object/from16 v16, v19

    const/16 v17, 0x1

    move-object/from16 v18, v5

    invoke-virtual/range {v18 .. v18}, Ljava/util/ArrayList;->size()I

    move-result v18

    invoke-static/range {v18 .. v18}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v18

    aput-object v18, v16, v17

    move-object/from16 v19, v15

    move-object/from16 v15, v19

    move-object/from16 v16, v19

    const/16 v17, 0x2

    move/from16 v18, v4

    invoke-static/range {v18 .. v18}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v18

    aput-object v18, v16, v17

    invoke-static {v13, v14, v15}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Leu/chainfire/libsuperuser/Debug;->logPool(Ljava/lang/String;)V

    .line 3378
    goto :goto_6

    .line 3380
    :cond_e
    return-void
.end method

.method public static declared-synchronized closeAll()V
    .locals 3
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 3510
    const-class v2, Leu/chainfire/libsuperuser/Shell$Pool;

    monitor-enter v2

    const/4 v0, 0x0

    const/4 v1, 0x1

    :try_start_0
    invoke-static {v0, v1}, Leu/chainfire/libsuperuser/Shell$Pool;->cleanup(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3511
    monitor-exit v2

    return-void

    .line 3510
    :catchall_0
    move-exception v0

    monitor-exit v2

    throw v0
.end method

.method public static get(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 3
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 3401
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x0

    invoke-static {v1, v2}, Leu/chainfire/libsuperuser/Shell$Pool;->get(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method public static get(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 16
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "WrongThread"
        }
    .end annotation

    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 3425
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    const/4 v10, 0x0

    move-object v2, v10

    .line 3426
    move-object v10, v0

    sget-object v11, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v10, v11}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v10

    move-object v3, v10

    .line 3428
    const-class v10, Leu/chainfire/libsuperuser/Shell$Pool;

    move-object v15, v10

    move-object v10, v15

    move-object v11, v15

    move-object v4, v11

    monitor-enter v10

    .line 3429
    const/4 v10, 0x0

    const/4 v11, 0x0

    :try_start_0
    invoke-static {v10, v11}, Leu/chainfire/libsuperuser/Shell$Pool;->cleanup(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V

    .line 3432
    sget-object v10, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v11, v3

    invoke-interface {v10, v11}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Ljava/util/ArrayList;

    move-object v5, v10

    .line 3433
    move-object v10, v5

    if-eqz v10, :cond_0

    .line 3434
    move-object v10, v5

    invoke-virtual {v10}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v10

    move-object v6, v10

    :goto_0
    move-object v10, v6

    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    move-result v10

    if-eqz v10, :cond_0

    move-object v10, v6

    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v7, v10

    .line 3435
    move-object v10, v7

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5000(Leu/chainfire/libsuperuser/Shell$Threaded;)Z

    move-result v10

    if-nez v10, :cond_1

    .line 3436
    move-object v10, v7

    move-object v2, v10

    .line 3437
    move-object v10, v2

    const/4 v11, 0x1

    invoke-static {v10, v11}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5200(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V

    .line 3442
    :cond_0
    move-object v10, v4

    monitor-exit v10
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3444
    move-object v10, v2

    if-nez v10, :cond_8

    .line 3446
    move-object v10, v0

    move-object v11, v1

    const/4 v12, 0x1

    invoke-static {v10, v11, v12}, Leu/chainfire/libsuperuser/Shell$Pool;->newInstance(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v10

    move-object v2, v10

    .line 3447
    move-object v10, v2

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/Shell$Threaded;->isRunning()Z

    move-result v10

    if-nez v10, :cond_2

    .line 3448
    new-instance v10, Leu/chainfire/libsuperuser/Shell$ShellDiedException;

    move-object v15, v10

    move-object v10, v15

    move-object v11, v15

    invoke-direct {v11}, Leu/chainfire/libsuperuser/Shell$ShellDiedException;-><init>()V

    throw v10

    .line 3440
    :cond_1
    goto :goto_0

    .line 3442
    :catchall_0
    move-exception v10

    move-object v8, v10

    move-object v10, v4

    :try_start_1
    monitor-exit v10
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    move-object v10, v8

    throw v10

    .line 3450
    :cond_2
    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabledEffective()Z

    move-result v10

    if-eqz v10, :cond_3

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->onMainThread()Z

    move-result v10

    if-nez v10, :cond_4

    .line 3451
    :cond_3
    move-object v10, v2

    const/4 v11, 0x0

    invoke-virtual {v10, v11}, Leu/chainfire/libsuperuser/Shell$Threaded;->waitForOpened(Ljava/lang/Boolean;)Z

    move-result v10

    if-nez v10, :cond_4

    .line 3452
    new-instance v10, Leu/chainfire/libsuperuser/Shell$ShellDiedException;

    move-object v15, v10

    move-object v10, v15

    move-object v11, v15

    invoke-direct {v11}, Leu/chainfire/libsuperuser/Shell$ShellDiedException;-><init>()V

    throw v10

    .line 3457
    :cond_4
    const-class v10, Leu/chainfire/libsuperuser/Shell$Pool;

    move-object v15, v10

    move-object v10, v15

    move-object v11, v15

    move-object v4, v11

    monitor-enter v10

    .line 3458
    move-object v10, v2

    :try_start_2
    invoke-virtual {v10}, Leu/chainfire/libsuperuser/Shell$Threaded;->wasPoolRemoveCalled()Z

    move-result v10

    if-nez v10, :cond_6

    .line 3459
    sget-object v10, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v11, v3

    invoke-interface {v10, v11}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    if-nez v10, :cond_5

    .line 3460
    sget-object v10, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v11, v3

    new-instance v12, Ljava/util/ArrayList;

    move-object v15, v12

    move-object v12, v15

    move-object v13, v15

    invoke-direct {v13}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v10, v11, v12}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    .line 3463
    :cond_5
    sget-object v10, Leu/chainfire/libsuperuser/Shell$Pool;->pool:Ljava/util/Map;

    move-object v11, v3

    invoke-interface {v10, v11}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Ljava/util/ArrayList;

    move-object v11, v2

    invoke-virtual {v10, v11}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    move-result v10

    .line 3465
    :cond_6
    move-object v10, v4

    monitor-exit v10

    .line 3485
    :cond_7
    :goto_1
    move-object v10, v2

    move-object v0, v10

    return-object v0

    .line 3465
    :catchall_1
    move-exception v10

    move-object v9, v10

    move-object v10, v4

    monitor-exit v10
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    move-object v10, v9

    throw v10

    .line 3468
    :cond_8
    move-object v10, v1

    if-eqz v10, :cond_7

    .line 3469
    move-object v10, v2

    move-object v4, v10

    .line 3470
    move-object v10, v2

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/Shell$Threaded;->startCallback()V

    .line 3472
    move-object v10, v2

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Threaded;->handler:Landroid/os/Handler;

    new-instance v11, Leu/chainfire/libsuperuser/Shell$Pool$2;

    move-object v15, v11

    move-object v11, v15

    move-object v12, v15

    move-object v13, v1

    move-object v14, v4

    invoke-direct {v12, v13, v14}, Leu/chainfire/libsuperuser/Shell$Pool$2;-><init>(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Leu/chainfire/libsuperuser/Shell$Threaded;)V

    invoke-virtual {v10, v11}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v10

    goto :goto_1
.end method

.method public static declared-synchronized getOnNewBuilderListener()Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .prologue
    .line 3223
    const-class v1, Leu/chainfire/libsuperuser/Shell$Pool;

    monitor-enter v1

    :try_start_0
    sget-object v0, Leu/chainfire/libsuperuser/Shell$Pool;->onNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v1

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit v1

    throw v0
.end method

.method public static declared-synchronized getPoolSize()I
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 3250
    const-class v1, Leu/chainfire/libsuperuser/Shell$Pool;

    monitor-enter v1

    :try_start_0
    sget v0, Leu/chainfire/libsuperuser/Shell$Pool;->poolSize:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v1

    return v0

    :catchall_0
    move-exception v0

    monitor-exit v1

    throw v0
.end method

.method public static getUnpooled(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 3
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 3295
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x0

    invoke-static {v1, v2}, Leu/chainfire/libsuperuser/Shell$Pool;->getUnpooled(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method public static getUnpooled(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 5
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 3311
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    invoke-static {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$Pool;->newInstance(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v2

    move-object v0, v2

    return-object v0
.end method

.method public static getWrapper(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$PoolWrapper;
    .locals 5
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 3523
    move-object v0, p0

    move-object v1, v0

    sget-object v2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v1, v2}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "SH"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    sget-object v1, Leu/chainfire/libsuperuser/Shell$Pool;->SH:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    if-eqz v1, :cond_0

    .line 3524
    sget-object v1, Leu/chainfire/libsuperuser/Shell$Pool;->SH:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-object v0, v1

    .line 3528
    :goto_0
    return-object v0

    .line 3525
    :cond_0
    move-object v1, v0

    sget-object v2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v1, v2}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "SU"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    sget-object v1, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    if-eqz v1, :cond_1

    .line 3526
    sget-object v1, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-object v0, v1

    goto :goto_0

    .line 3528
    :cond_1
    new-instance v1, Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-object v4, v1

    move-object v1, v4

    move-object v2, v4

    move-object v3, v0

    invoke-direct {v2, v3}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;-><init>(Ljava/lang/String;)V

    move-object v0, v1

    goto :goto_0
.end method

.method private static newBuilder()Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 5
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 3277
    const-class v2, Leu/chainfire/libsuperuser/Shell$Pool;

    move-object v4, v2

    move-object v2, v4

    move-object v3, v4

    move-object v0, v3

    monitor-enter v2

    .line 3278
    :try_start_0
    sget-object v2, Leu/chainfire/libsuperuser/Shell$Pool;->onNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;

    if-eqz v2, :cond_0

    .line 3279
    sget-object v2, Leu/chainfire/libsuperuser/Shell$Pool;->onNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;

    invoke-interface {v2}, Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;->newBuilder()Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v2

    move-object v3, v0

    monitor-exit v3

    move-object v0, v2

    .line 3281
    :goto_0
    return-object v0

    :cond_0
    sget-object v2, Leu/chainfire/libsuperuser/Shell$Pool;->defaultOnNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;

    invoke-interface {v2}, Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;->newBuilder()Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v2

    move-object v3, v0

    monitor-exit v3

    move-object v0, v2

    goto :goto_0

    .line 3283
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v2, v1

    throw v2
.end method

.method private static newInstance(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 10
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .prologue
    .line 3315
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    sget-object v3, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v4, "newInstance(shell:%s, pooled:%d)"

    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    const/4 v7, 0x0

    move-object v8, v0

    aput-object v8, v6, v7

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    const/4 v7, 0x1

    move v8, v2

    if-eqz v8, :cond_0

    const/4 v8, 0x1

    :goto_0
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    aput-object v8, v6, v7

    invoke-static {v3, v4, v5}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Leu/chainfire/libsuperuser/Debug;->logPool(Ljava/lang/String;)V

    .line 3316
    invoke-static {}, Leu/chainfire/libsuperuser/Shell$Pool;->newBuilder()Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v3

    move-object v4, v0

    invoke-virtual {v3, v4}, Leu/chainfire/libsuperuser/Shell$Builder;->setShell(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v3

    move-object v4, v1

    move v5, v2

    invoke-static {v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$Builder;->access$4900(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v3

    move-object v0, v3

    return-object v0

    .line 3315
    :cond_0
    const/4 v8, 0x0

    goto :goto_0
.end method

.method private static releaseReservation(Leu/chainfire/libsuperuser/Shell$Threaded;)V
    .locals 3
    .param p0    # Leu/chainfire/libsuperuser/Shell$Threaded;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 3492
    move-object v0, p0

    const-string v1, "releaseReservation"

    invoke-static {v1}, Leu/chainfire/libsuperuser/Debug;->logPool(Ljava/lang/String;)V

    .line 3493
    move-object v1, v0

    const/4 v2, 0x0

    invoke-static {v1, v2}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$5200(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V

    .line 3494
    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-static {v1, v2}, Leu/chainfire/libsuperuser/Shell$Pool;->cleanup(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V

    .line 3495
    return-void
.end method

.method private static declared-synchronized removeShell(Leu/chainfire/libsuperuser/Shell$Threaded;)V
    .locals 4
    .param p0    # Leu/chainfire/libsuperuser/Shell$Threaded;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 3501
    move-object v0, p0

    const-class v3, Leu/chainfire/libsuperuser/Shell$Pool;

    monitor-enter v3

    :try_start_0
    const-string v1, "removeShell"

    invoke-static {v1}, Leu/chainfire/libsuperuser/Debug;->logPool(Ljava/lang/String;)V

    .line 3502
    move-object v1, v0

    const/4 v2, 0x0

    invoke-static {v1, v2}, Leu/chainfire/libsuperuser/Shell$Pool;->cleanup(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3503
    monitor-exit v3

    return-void

    .line 3501
    :catchall_0
    move-exception v0

    monitor-exit v3

    throw v0
.end method

.method public static declared-synchronized setOnNewBuilderListener(Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;)V
    .locals 3
    .param p0    # Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 3233
    move-object v0, p0

    const-class v2, Leu/chainfire/libsuperuser/Shell$Pool;

    monitor-enter v2

    move-object v1, v0

    :try_start_0
    sput-object v1, Leu/chainfire/libsuperuser/Shell$Pool;->onNewBuilderListener:Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3234
    monitor-exit v2

    return-void

    .line 3233
    :catchall_0
    move-exception v0

    monitor-exit v2

    throw v0
.end method

.method public static declared-synchronized setPoolSize(I)V
    .locals 4
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 3267
    move v0, p0

    const-class v3, Leu/chainfire/libsuperuser/Shell$Pool;

    monitor-enter v3

    move v1, v0

    const/4 v2, 0x1

    :try_start_0
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v1

    move v0, v1

    .line 3268
    move v1, v0

    sget v2, Leu/chainfire/libsuperuser/Shell$Pool;->poolSize:I

    if-eq v1, v2, :cond_0

    .line 3269
    move v1, v0

    sput v1, Leu/chainfire/libsuperuser/Shell$Pool;->poolSize:I

    .line 3270
    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-static {v1, v2}, Leu/chainfire/libsuperuser/Shell$Pool;->cleanup(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3272
    :cond_0
    monitor-exit v3

    return-void

    .line 3267
    :catchall_0
    move-exception v0

    monitor-exit v3

    throw v0
.end method
