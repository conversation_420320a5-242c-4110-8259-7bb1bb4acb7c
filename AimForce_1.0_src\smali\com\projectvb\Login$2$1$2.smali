.class Lcom/projectvb/Login$2$1$2;
.super Ljava/lang/Object;
.source "Login.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Login$2$1;->enableLoginUI()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$2:Lcom/projectvb/Login$2$1;


# direct methods
.method constructor <init>(Lcom/projectvb/Login$2$1;)V
    .locals 0
    .param p1, "this$2"    # Lcom/projectvb/Login$2$1;

    .line 200
    iput-object p1, p0, Lcom/projectvb/Login$2$1$2;->this$2:Lcom/projectvb/Login$2$1;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 203
    iget-object v0, p0, Lcom/projectvb/Login$2$1$2;->this$2:Lcom/projectvb/Login$2$1;

    iget-object v0, v0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v0, v0, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v0}, Lcom/projectvb/Login;->access$500(Lcom/projectvb/Login;)Landroid/widget/ProgressBar;

    move-result-object v0

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 204
    iget-object v0, p0, Lcom/projectvb/Login$2$1$2;->this$2:Lcom/projectvb/Login$2$1;

    iget-object v0, v0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v0, v0, Lcom/projectvb/Login$2;->val$loginButton:Landroid/widget/Button;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setEnabled(Z)V

    .line 205
    return-void
.end method
