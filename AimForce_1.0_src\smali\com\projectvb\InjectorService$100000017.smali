.class Lcom/projectvb/InjectorService$100000017;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/content/DialogInterface$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000017"
.end annotation


# instance fields
.field private final this$0:Lcom/projectvb/InjectorService;


# direct methods
.method constructor <init>(Lcom/projectvb/InjectorService;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/projectvb/InjectorService$100000017;->this$0:Lcom/projectvb/InjectorService;

    return-void
.end method

.method static access$0(Lcom/projectvb/InjectorService$100000017;)Lcom/projectvb/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000017;->this$0:Lcom/projectvb/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/content/DialogInterface;I)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/DialogInterface;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 1080
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lcom/projectvb/InjectorService$100000017;->this$0:Lcom/projectvb/InjectorService;

    invoke-virtual {v4}, Lcom/projectvb/InjectorService;->stopSelf()V

    .line 1081
    move-object v4, v0

    iget-object v4, v4, Lcom/projectvb/InjectorService$100000017;->this$0:Lcom/projectvb/InjectorService;

    iget-object v4, v4, Lcom/projectvb/InjectorService;->mWindowManager:Landroid/view/WindowManager;

    move-object v5, v0

    iget-object v5, v5, Lcom/projectvb/InjectorService$100000017;->this$0:Lcom/projectvb/InjectorService;

    iget-object v5, v5, Lcom/projectvb/InjectorService;->frameLayout:Landroid/widget/FrameLayout;

    invoke-interface {v4, v5}, Landroid/view/WindowManager;->removeView(Landroid/view/View;)V

    return-void
.end method
