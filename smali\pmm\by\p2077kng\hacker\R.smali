.class public final Lpmm/by/p2077kng/hacker/R;
.super Ljava/lang/Object;
.source "R.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lpmm/by/p2077kng/hacker/R$attr;,
        Lpmm/by/p2077kng/hacker/R$color;,
        Lpmm/by/p2077kng/hacker/R$drawable;,
        Lpmm/by/p2077kng/hacker/R$id;,
        Lpmm/by/p2077kng/hacker/R$layout;,
        Lpmm/by/p2077kng/hacker/R$string;,
        Lpmm/by/p2077kng/hacker/R$style;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 32
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method
