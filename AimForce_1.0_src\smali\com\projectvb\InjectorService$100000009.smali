.class Lcom/projectvb/InjectorService$100000009;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Lcom/projectvb/InjectorService$InterfaceBtn;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000009"
.end annotation


# instance fields
.field private final this$0:Lcom/projectvb/InjectorService;

.field private final val$feature:I


# direct methods
.method constructor <init>(Lcom/projectvb/InjectorService;I)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lcom/projectvb/InjectorService$100000009;->this$0:Lcom/projectvb/InjectorService;

    move-object v4, v0

    move v5, v2

    iput v5, v4, Lcom/projectvb/InjectorService$100000009;->val$feature:I

    return-void
.end method

.method static access$0(Lcom/projectvb/InjectorService$100000009;)Lcom/projectvb/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000009;->this$0:Lcom/projectvb/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public OnWrite()V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 675
    move-object v0, p0

    move-object v2, v0

    iget-object v2, v2, Lcom/projectvb/InjectorService$100000009;->this$0:Lcom/projectvb/InjectorService;

    move-object v3, v0

    iget v3, v3, Lcom/projectvb/InjectorService$100000009;->val$feature:I

    const/4 v4, 0x0

    invoke-virtual {v2, v3, v4}, Lcom/projectvb/InjectorService;->Changes(II)V

    return-void
.end method
