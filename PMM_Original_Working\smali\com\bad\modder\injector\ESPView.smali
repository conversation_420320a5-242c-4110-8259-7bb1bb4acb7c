.class public Lcom/bad/modder/injector/ESPView;
.super Landroid/view/View;
.source "ESPView.java"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field FPS:I

.field mFilledPaint:Landroid/graphics/Paint;

.field mStrokePaint:Landroid/graphics/Paint;

.field mTextPaint:Landroid/graphics/Paint;

.field mThread:Ljava/lang/Thread;

.field screenHeight:I

.field screenWidth:I

.field sleepTime:J

.field time:Ljava/util/Date;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 8

    .prologue
    .line 29
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    move-object v4, v1

    const/4 v5, 0x0

    check-cast v5, Landroid/util/AttributeSet;

    const/4 v6, 0x0

    invoke-direct {v3, v4, v5, v6}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    move-object v3, v0

    const/16 v4, 0x3c

    iput v4, v3, Lcom/bad/modder/injector/ESPView;->FPS:I

    .line 30
    move-object v3, v0

    invoke-virtual {v3}, Lcom/bad/modder/injector/ESPView;->InitializePaints()V

    .line 31
    move-object v3, v0

    const/4 v4, 0x0

    invoke-virtual {v3, v4}, Lcom/bad/modder/injector/ESPView;->setFocusableInTouchMode(Z)V

    .line 32
    move-object v3, v0

    const/4 v4, 0x0

    invoke-virtual {v3, v4}, Lcom/bad/modder/injector/ESPView;->setBackgroundColor(I)V

    .line 33
    move-object v3, v0

    new-instance v4, Ljava/util/Date;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    invoke-direct {v5}, Ljava/util/Date;-><init>()V

    iput-object v4, v3, Lcom/bad/modder/injector/ESPView;->time:Ljava/util/Date;

    .line 34
    move-object v3, v0

    const/16 v4, 0x3e8

    move-object v5, v0

    iget v5, v5, Lcom/bad/modder/injector/ESPView;->FPS:I

    div-int/2addr v4, v5

    int-to-long v4, v4

    iput-wide v4, v3, Lcom/bad/modder/injector/ESPView;->sleepTime:J

    .line 35
    move-object v3, v0

    new-instance v4, Ljava/lang/Thread;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v6, v0

    invoke-direct {v5, v6}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    iput-object v4, v3, Lcom/bad/modder/injector/ESPView;->mThread:Ljava/lang/Thread;

    .line 36
    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/ESPView;->mThread:Ljava/lang/Thread;

    invoke-virtual {v3}, Ljava/lang/Thread;->start()V

    return-void
.end method


# virtual methods
.method public ClearCanvas(Landroid/graphics/Canvas;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            ")V"
        }
    .end annotation

    .prologue
    .line 80
    move-object v0, p0

    move-object v1, p1

    move-object v3, v1

    const/4 v4, 0x0

    sget-object v5, Landroid/graphics/PorterDuff$Mode;->CLEAR:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v3, v4, v5}, Landroid/graphics/Canvas;->drawColor(ILandroid/graphics/PorterDuff$Mode;)V

    return-void
.end method

.method public DrawCircle(Landroid/graphics/Canvas;IIIIFFFF)V
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIFFFF)V"
        }
    .end annotation

    .prologue
    .line 105
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move/from16 v4, p4

    move/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    move/from16 v9, p9

    move-object v11, v0

    iget-object v11, v11, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v12, v3

    move v13, v4

    move v14, v5

    invoke-static {v12, v13, v14}, Landroid/graphics/Color;->rgb(III)I

    move-result v12

    invoke-virtual {v11, v12}, Landroid/graphics/Paint;->setColor(I)V

    .line 106
    move-object v11, v0

    iget-object v11, v11, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v12, v2

    invoke-virtual {v11, v12}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 107
    move-object v11, v0

    iget-object v11, v11, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v12, v6

    invoke-virtual {v11, v12}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 108
    move-object v11, v1

    move v12, v7

    move v13, v8

    move v14, v9

    move-object v15, v0

    iget-object v15, v15, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-virtual {v11, v12, v13, v14, v15}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    return-void
.end method

.method public DrawEnemyCount(Landroid/graphics/Canvas;IIIIIIII)V
    .locals 22
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIIIII)V"
        }
    .end annotation

    .prologue
    .line 125
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move/from16 v4, p4

    move/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    move/from16 v9, p9

    const/4 v15, 0x3

    new-array v15, v15, [I

    move-object/from16 v21, v15

    move-object/from16 v15, v21

    move-object/from16 v16, v21

    const/16 v17, 0x0

    const/16 v18, 0x0

    aput v18, v16, v17

    move-object/from16 v21, v15

    move-object/from16 v15, v21

    move-object/from16 v16, v21

    const/16 v17, 0x1

    move/from16 v18, v3

    move/from16 v19, v4

    move/from16 v20, v5

    invoke-static/range {v18 .. v20}, Landroid/graphics/Color;->rgb(III)I

    move-result v18

    aput v18, v16, v17

    move-object/from16 v21, v15

    move-object/from16 v15, v21

    move-object/from16 v16, v21

    const/16 v17, 0x2

    const/16 v18, 0x0

    aput v18, v16, v17

    move-object v11, v15

    .line 126
    new-instance v15, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v21, v15

    move-object/from16 v15, v21

    move-object/from16 v16, v21

    sget-object v17, Landroid/graphics/drawable/GradientDrawable$Orientation;->RIGHT_LEFT:Landroid/graphics/drawable/GradientDrawable$Orientation;

    move-object/from16 v18, v11

    invoke-direct/range {v16 .. v18}, Landroid/graphics/drawable/GradientDrawable;-><init>(Landroid/graphics/drawable/GradientDrawable$Orientation;[I)V

    move-object v12, v15

    .line 127
    move-object v15, v12

    const/16 v16, 0x0

    invoke-virtual/range {v15 .. v16}, Landroid/graphics/drawable/GradientDrawable;->setShape(I)V

    .line 128
    move-object v15, v12

    const/high16 v16, 0x42f00000    # 120.0f

    invoke-virtual/range {v15 .. v16}, Landroid/graphics/drawable/GradientDrawable;->setGradientRadius(F)V

    .line 129
    new-instance v15, Landroid/graphics/Rect;

    move-object/from16 v21, v15

    move-object/from16 v15, v21

    move-object/from16 v16, v21

    move/from16 v17, v6

    move/from16 v18, v7

    move/from16 v19, v8

    move/from16 v20, v9

    invoke-direct/range {v16 .. v20}, Landroid/graphics/Rect;-><init>(IIII)V

    move-object v13, v15

    .line 130
    move-object v15, v12

    move-object/from16 v16, v13

    invoke-virtual/range {v15 .. v16}, Landroid/graphics/drawable/GradientDrawable;->setBounds(Landroid/graphics/Rect;)V

    .line 131
    move-object v15, v1

    invoke-virtual {v15}, Landroid/graphics/Canvas;->save()I

    move-result v15

    .line 132
    move-object v15, v12

    const/16 v16, 0x0

    invoke-virtual/range {v15 .. v16}, Landroid/graphics/drawable/GradientDrawable;->setGradientType(I)V

    .line 133
    move-object v15, v12

    move-object/from16 v16, v1

    invoke-virtual/range {v15 .. v16}, Landroid/graphics/drawable/GradientDrawable;->draw(Landroid/graphics/Canvas;)V

    .line 134
    move-object v15, v1

    invoke-virtual {v15}, Landroid/graphics/Canvas;->restore()V

    return-void
.end method

.method public DrawFilledCircle(Landroid/graphics/Canvas;IIIIFFF)V
    .locals 15
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIFFF)V"
        }
    .end annotation

    .prologue
    .line 112
    move-object v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move/from16 v4, p4

    move/from16 v5, p5

    move/from16 v6, p6

    move/from16 v7, p7

    move/from16 v8, p8

    move-object v10, v0

    iget-object v10, v10, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    move v11, v3

    move v12, v4

    move v13, v5

    invoke-static {v11, v12, v13}, Landroid/graphics/Color;->rgb(III)I

    move-result v11

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setColor(I)V

    .line 113
    move-object v10, v0

    iget-object v10, v10, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    move v11, v2

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 114
    move-object v10, v1

    move v11, v6

    move v12, v7

    move v13, v8

    move-object v14, v0

    iget-object v14, v14, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    return-void
.end method

.method public DrawFilledRect(Landroid/graphics/Canvas;IIIIFFFF)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIFFFF)V"
        }
    .end annotation

    .prologue
    .line 138
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    move/from16 v10, p9

    move-object v12, v1

    iget-object v12, v12, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    move v13, v4

    move v14, v5

    move v15, v6

    invoke-static {v13, v14, v15}, Landroid/graphics/Color;->rgb(III)I

    move-result v13

    invoke-virtual {v12, v13}, Landroid/graphics/Paint;->setColor(I)V

    .line 139
    move-object v12, v1

    iget-object v12, v12, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    move v13, v3

    invoke-virtual {v12, v13}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 140
    move-object v12, v2

    move v13, v7

    move v14, v8

    move v15, v7

    move/from16 v16, v9

    add-float v15, v15, v16

    move/from16 v16, v8

    move/from16 v17, v10

    add-float v16, v16, v17

    move-object/from16 v17, v1

    move-object/from16 v0, v17

    iget-object v0, v0, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    move-object/from16 v17, v0

    invoke-virtual/range {v12 .. v17}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    return-void
.end method

.method public DrawLine(Landroid/graphics/Canvas;IIIIFFFFF)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIFFFFF)V"
        }
    .end annotation

    .prologue
    .line 84
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    move/from16 v10, p9

    move/from16 v11, p10

    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v14, v4

    move v15, v5

    move/from16 v16, v6

    invoke-static/range {v14 .. v16}, Landroid/graphics/Color;->rgb(III)I

    move-result v14

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setColor(I)V

    .line 85
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v14, v3

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 86
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v14, v7

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 87
    move-object v13, v2

    move v14, v8

    move v15, v9

    move/from16 v16, v10

    move/from16 v17, v11

    move-object/from16 v18, v1

    move-object/from16 v0, v18

    iget-object v0, v0, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move-object/from16 v18, v0

    invoke-virtual/range {v13 .. v18}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    return-void
.end method

.method public DrawRect(Landroid/graphics/Canvas;IIIIIFFFF)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIIFFFF)V"
        }
    .end annotation

    .prologue
    .line 118
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    move/from16 v10, p9

    move/from16 v11, p10

    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v14, v7

    int-to-float v14, v14

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 119
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v14, v4

    move v15, v5

    move/from16 v16, v6

    invoke-static/range {v14 .. v16}, Landroid/graphics/Color;->rgb(III)I

    move-result v14

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setColor(I)V

    .line 120
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move v14, v3

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 121
    move-object v13, v2

    move v14, v8

    move v15, v9

    move/from16 v16, v8

    move/from16 v17, v10

    add-float v16, v16, v17

    move/from16 v17, v9

    move/from16 v18, v11

    add-float v17, v17, v18

    move-object/from16 v18, v1

    move-object/from16 v0, v18

    iget-object v0, v0, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    move-object/from16 v18, v0

    invoke-virtual/range {v13 .. v18}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    return-void
.end method

.method public DrawText(Landroid/graphics/Canvas;IIIIFLjava/lang/String;FFF)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            "IIIIF",
            "Ljava/lang/String;",
            "FFF)V"
        }
    .end annotation

    .prologue
    .line 91
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move-object/from16 v8, p7

    move/from16 v9, p8

    move/from16 v10, p9

    move/from16 v11, p10

    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    move v14, v4

    move v15, v5

    move/from16 v16, v6

    invoke-static/range {v14 .. v16}, Landroid/graphics/Color;->rgb(III)I

    move-result v14

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setColor(I)V

    .line 92
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    move v14, v3

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 93
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    move v14, v7

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 94
    move-object v13, v1

    invoke-virtual {v13}, Lcom/bad/modder/injector/ESPView;->getRight()I

    move-result v13

    const/16 v14, 0x780

    if-gt v13, v14, :cond_0

    move-object v13, v1

    invoke-virtual {v13}, Lcom/bad/modder/injector/ESPView;->getBottom()I

    move-result v13

    const/16 v14, 0x780

    if-le v13, v14, :cond_1

    .line 95
    :cond_0
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    const/high16 v14, 0x40800000    # 4.0f

    move v15, v11

    add-float/2addr v14, v15

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 101
    :goto_0
    move-object v13, v2

    move-object v14, v8

    move v15, v9

    move/from16 v16, v10

    move-object/from16 v17, v1

    move-object/from16 v0, v17

    iget-object v0, v0, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    move-object/from16 v17, v0

    invoke-virtual/range {v13 .. v17}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    return-void

    .line 96
    :cond_1
    move-object v13, v1

    invoke-virtual {v13}, Lcom/bad/modder/injector/ESPView;->getRight()I

    move-result v13

    const/16 v14, 0x780

    if-eq v13, v14, :cond_2

    move-object v13, v1

    invoke-virtual {v13}, Lcom/bad/modder/injector/ESPView;->getBottom()I

    move-result v13

    const/16 v14, 0x780

    if-ne v13, v14, :cond_3

    .line 97
    :cond_2
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    move v14, v11

    const/high16 v15, 0x40000000    # 2.0f

    add-float/2addr v14, v15

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setTextSize(F)V

    goto :goto_0

    .line 99
    :cond_3
    move-object v13, v1

    iget-object v13, v13, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    move v14, v11

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setTextSize(F)V

    goto :goto_0
.end method

.method public InitializePaints()V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 65
    move-object v0, p0

    move-object v2, v0

    new-instance v3, Landroid/graphics/Paint;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    invoke-direct {v4}, Landroid/graphics/Paint;-><init>()V

    iput-object v3, v2, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    .line 66
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    sget-object v3, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 67
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    const/4 v3, 0x1

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 68
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mStrokePaint:Landroid/graphics/Paint;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    invoke-static {v3, v4, v5}, Landroid/graphics/Color;->rgb(III)I

    move-result v3

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 70
    move-object v2, v0

    new-instance v3, Landroid/graphics/Paint;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    invoke-direct {v4}, Landroid/graphics/Paint;-><init>()V

    iput-object v3, v2, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    .line 71
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    sget-object v3, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 72
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mFilledPaint:Landroid/graphics/Paint;

    const/4 v3, 0x1

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 74
    move-object v2, v0

    new-instance v3, Landroid/graphics/Paint;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    invoke-direct {v4}, Landroid/graphics/Paint;-><init>()V

    iput-object v3, v2, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    .line 75
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    const/4 v3, 0x1

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 76
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/ESPView;->mTextPaint:Landroid/graphics/Paint;

    sget-object v3, Landroid/graphics/Paint$Align;->CENTER:Landroid/graphics/Paint$Align;

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setTextAlign(Landroid/graphics/Paint$Align;)V

    return-void
.end method

.method protected onDraw(Landroid/graphics/Canvas;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Canvas;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 41
    move-object v0, p0

    move-object v1, p1

    move-object v3, v1

    if-eqz v3, :cond_0

    move-object v3, v0

    invoke-virtual {v3}, Lcom/bad/modder/injector/ESPView;->getVisibility()I

    move-result v3

    const/4 v4, 0x0

    if-ne v3, v4, :cond_0

    .line 42
    move-object v3, v0

    move-object v4, v1

    invoke-virtual {v3, v4}, Lcom/bad/modder/injector/ESPView;->ClearCanvas(Landroid/graphics/Canvas;)V

    .line 43
    move-object v3, v0

    move-object v4, v1

    invoke-virtual {v4}, Landroid/graphics/Canvas;->getWidth()I

    move-result v4

    const/4 v5, 0x2

    div-int/lit8 v4, v4, 0x2

    iput v4, v3, Lcom/bad/modder/injector/ESPView;->screenWidth:I

    .line 44
    move-object v3, v0

    move-object v4, v1

    invoke-virtual {v4}, Landroid/graphics/Canvas;->getHeight()I

    move-result v4

    iput v4, v3, Lcom/bad/modder/injector/ESPView;->screenHeight:I

    .line 45
    move-object v3, v0

    move-object v4, v1

    invoke-static {v3, v4}, Lcom/bad/modder/injector/InjectorService;->DrawOn(Lcom/bad/modder/injector/ESPView;Landroid/graphics/Canvas;)V

    :cond_0
    return-void
.end method

.method public run()V
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 51
    move-object v1, p0

    const/16 v6, 0xa

    invoke-static {v6}, Landroid/os/Process;->setThreadPriority(I)V

    .line 52
    :goto_0
    move-object v6, v1

    iget-object v6, v6, Lcom/bad/modder/injector/ESPView;->mThread:Ljava/lang/Thread;

    invoke-virtual {v6}, Ljava/lang/Thread;->isAlive()Z

    move-result v6

    if-eqz v6, :cond_0

    move-object v6, v1

    iget-object v6, v6, Lcom/bad/modder/injector/ESPView;->mThread:Ljava/lang/Thread;

    invoke-virtual {v6}, Ljava/lang/Thread;->isInterrupted()Z

    move-result v6

    if-eqz v6, :cond_1

    :cond_0
    return-void

    .line 54
    :cond_1
    :try_start_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v6

    move-wide v3, v6

    .line 55
    move-object v6, v1

    invoke-virtual {v6}, Lcom/bad/modder/injector/ESPView;->postInvalidate()V

    .line 56
    const/4 v6, 0x0

    int-to-long v6, v6

    move-object v8, v1

    iget-wide v8, v8, Lcom/bad/modder/injector/ESPView;->sleepTime:J

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v10

    move-wide v12, v3

    sub-long/2addr v10, v12

    sub-long/2addr v8, v10

    invoke-static {v6, v7, v8, v9}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v6

    move-object v8, v1

    iget-wide v8, v8, Lcom/bad/modder/injector/ESPView;->sleepTime:J

    invoke-static {v6, v7, v8, v9}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v6

    invoke-static {v6, v7}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v6

    move-object v3, v6

    .line 58
    const-string v6, "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"

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    move-object v7, v3

    invoke-virtual {v7}, Ljava/lang/InterruptedException;->getMessage()Ljava/lang/String;

    move-result-object v7

    invoke-static {v6, v7}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    move-result v6

    goto/16 :goto_0
.end method
