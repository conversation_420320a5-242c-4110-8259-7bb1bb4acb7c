.class Lcom/bad/modder/injector/InjectorService$100000013;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/widget/SeekBar$OnSeekBarChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000013"
.end annotation


# instance fields
.field l:I

.field private final this$0:Lcom/bad/modder/injector/InjectorService;

.field private final val$feature:Ljava/lang/String;

.field private final val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

.field private final val$seekBar4:Landroid/widget/SeekBar;

.field private final val$textView4:Landroid/widget/TextView;


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/InjectorService;Landroid/widget/SeekBar;Lcom/bad/modder/injector/InjectorService$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;)V
    .locals 9

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v7, v0

    invoke-direct {v7}, Ljava/lang/Object;-><init>()V

    move-object v7, v0

    move-object v8, v1

    iput-object v8, v7, Lcom/bad/modder/injector/InjectorService$100000013;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v7, v0

    move-object v8, v2

    iput-object v8, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move-object v7, v0

    move-object v8, v3

    iput-object v8, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move-object v7, v0

    move-object v8, v4

    iput-object v8, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v7, v0

    move-object v8, v5

    iput-object v8, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/InjectorService$100000013;)Lcom/bad/modder/injector/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000013;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onProgressChanged(Landroid/widget/SeekBar;IZ)V
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/SeekBar;",
            "IZ)V"
        }
    .end annotation

    .prologue
    .line 872
    move-object v0, p0

    move-object v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move v7, v2

    const/4 v8, 0x0

    if-ne v7, v8, :cond_1

    .line 873
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 874
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 875
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 876
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBejgdAmtqMScnQHkGHh4YDRgWAB5pAh0XaHAfEiQfaWEfAWcPDBBydVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrRWoXEA9DXTtkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE+FSRnQTF7EFd9MxYOFFwQLAdVXQM/Ik96MQQjVQA8EXVdSQUsA0h+DRETYXAPIiRKcWIcK0UNNxYHSF0AI21veRMOFX95JhoTY1kPORdrWWRsHkhAEwInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4TIxdheTECHnx1eh0tFA4YBRdHbgQVfmhgAzAVfG0mFXZFExEGA3lhDRUWZFEHHyV6cTYfdnh2GywxHGo4PxVlb2QmJ1R+Mh0rFAEcBj4fWWRkFmJqMSYRemkbMAFjeQwXD0NeOz92aGkXFhN/fSE3LlVdGhYAWVlnJw5iUDFhIEBxBxUDRnoSFzFLYTtgEmRAMTwjeX0WHChVCTA8dl9vAD8iYWpsECdXcQcYK3tpEC4TR2oDbTJve2EBHm9PYBJ2RVARADVGYRMRDmNrA2IlfGEHERAYDRAWJQZwZztyfUADOCQfaTged38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9/XTg/JGB5Ez4jfE8NEHZjXRoufx9ZEj8ea1AbeyB+fWYYKntcEjwcVG8DOChnT2A9E3l9FhErGFAYFjFZWR07FWtPIWUneUwyFwFFSRgAMUddDWQRZXs9AgV5eW0wKkETExMHS1pnOz5lewcfJ3x1LB91WWgTLXJ5amc7AmNAAyYfbHUCER5aeRcWKX9ZZGQUb1AbHx9FdQcEK2NKGAYxQV47EQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 948
    :cond_0
    :goto_0
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    return-void

    .line 877
    :cond_1
    move v7, v2

    const/4 v8, 0x1

    if-ne v7, v8, :cond_2

    .line 878
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 879
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 880
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 881
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "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"

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 882
    :cond_2
    move v7, v2

    const/4 v8, 0x2

    if-ne v7, v8, :cond_3

    .line 883
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 884
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 885
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 886
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95XwcdK2d5HS0XeVk7PwFoa2EOIn9fAhUEGE8dBi17WR0VJWhBBwweV30DH3ZrcREWJXVdAz9xfU8TOCdXCBYSERReFDwTaHATFRZ9cA9gH0V5BxUrWWoSLDF5cGZkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfaQA7HmRCEx0kV1M7BCt4dBcscm5eIjMiYHs9Yx5vaSAyd3tdFBMxfmE7OyRnaxd7FVp1MxYOFFwQLAdVXQM/InR6Hz8XbAA4ESp/ARcsA0h+DRETYXAPIiRKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4TIxdheTECHnx1eh0tFA4YBRdHbgQVfmhgAzAVfG0mFXZFExEGA3lhDRUWZFEHHyV6cTYfdnh2GywxHGo4PxVlb2QmJ1R+Mh0rFF4QPD4fWWU/FG9fGyEfSnUDHgFJaRQtJWVaOz81anphAR5vdQIXd3tdGhYAWVkSFQ5gQBt7HmpxBxUDRnoSFzFLYTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK0VIEiwlR2pmODJve2ACEkVXJhV3e1AQFhMGWWURAmRRMRwlfAgiFQNrcxAWC0tZAj8CfU8fJRN+fWIdd38BHDwTWV1kJydrUWRhHkBuPhwrRQ0cBgNLXThkDmNPEz4jf2kmEHZJXBoufm5hHSwhSEATEiB+eQ8cLWtcEjwfbG8DPxVhexM6F3l9ZBEQSVAUPgdsXWc/EmFsE20feUwyEnRZSRQtH2tuBBURZV8TFiJvaSAeE2ddHDkXS1pnOz5lewcfJ3x1LB91WWgTLXJ5amc7AmNAAyYfbHUCER5aeRcWKX9ZZGQUb1AbHx9FdQcEK2NKGAYxQV47EQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 887
    :cond_3
    move v7, v2

    const/4 v8, 0x3

    if-ne v7, v8, :cond_4

    .line 888
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 889
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 890
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 891
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "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"

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 892
    :cond_4
    move v7, v2

    const/4 v8, 0x4

    if-ne v7, v8, :cond_5

    .line 893
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 894
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 895
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 896
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3o3K0kPDBBydVoiFRFoUAQOIn9pIBZ2Z0oULAN+WWQRAmtQbGETHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXEDFgB/WxQuNVtZZzsSYnobJSN/YQYRFH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfaQA7HmRCE2ElaVMiH3R4dBEsC0NaZmQFa18XexFVaSAaEFliDy41fmE7Jz50URt7H3xhIhYOVQ4QFzFLbDsVcnR5MQQjVQA8EQNCehoGA3laZDsxYEAxMid8eWIcK397ECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFxYkEHOidVXGISEF0JGCwfYn4TIxdheTECHnx1eh0ofw4dLn51WhQVcmhrJXsHf1smFXZFExEGA3lhDRUWZFEHHyV6cTYfdnh2GywxHGo4PxVlb2QmJ1R+Mh0rFF4QPD4fWWU/FG9fGyEfSnUDHgFJaRQtJWVaOz81anphAR5vdQIXd3tdGhYAWVkSFQ5gQBt7HmpxBxUDRnoSFzFLYTtgEmRAMTwjeX0WHChVCTA8dll+FGAfb2oxHCRKaQMyK0VfFC4He14SZCtrYA8SA1V9EhV1QVARBhNKaxARPmVPAyUleWEzMBR8aBAWC0tZAj8CfQsfOCQfaTwddRQJEjwPSH4UZCB9ezlhH3ppehIrZ0kYBx9/XTg7K2V7ZBIjfE82Gh5VThpmF25hHSwhSEATEiB+eQ8cLWtcEjwfbG8DPxVhexM6F3l9ZBEQSVAUPgdsXWc/EmFsE20feUwyEnRZSRQtH2tuBBURZV8TFiJvaSAeE2ddHDkXS1pnOz5lewcfJ3x1LB91WWgTLXJ5amc7AmNAAyYfbHUCER5aeRcWKX9ZZGQUb1AbHx9FdQcEK2NKGAYxQV47EQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 897
    :cond_5
    move v7, v2

    const/4 v8, 0x5

    if-ne v7, v8, :cond_6

    .line 898
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 899
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 900
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 901
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTgRImJAHyQTankGES4ZdRgWAB5wHRkVaHAcEyRXXzEVK0FIDBcXeVk7PwFlaz0CFW9fAhUEHFAPPHZ4YhARAmtQbGETHlcMMANBVxIVBFRcAzsva2oTOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDkwK2ULDxMnb30CFgB/XRoTB2lZZxUOYkExEhFUcQIwFHx6HS4xeVkdYBJkQA8mElR+PxwtXUAFLANfWmY7HmRCEx0kQGI+FSt4dBEtH0FeZmQFZVBgYx5vaSAyd3tdFBMxfmE7Jz5peQMiH3xhBxIOFFwQLAdVXQM/ImtqHyYnSgEhHhFaehg8Ph9dImQeYXobMiRacXowEUVoEmYDQVkDGitrCwciAn9xJhx1VU0RACEcYR0gLWdQAycHeV8QHy1BahEjH3leOBFxYkEHOidVTGIcdVVQFywPYn4SHRdhbBMYJGlLAzcofw0YAAt1WmUjLmVQEzUjRVcSFXZFExEGA3lhDRUWZFExbCN6cTYfdRQPEBMfaWkDPwJqUSEmBVpbPBUoFEAfPD4cbgBkFGx7EDcffwgyGBFJaRQtJWVdA2wXZUFkFidFfT8cdUFdGhZyWVlnJw59QBt7EmpxBhEAHHkdEDF9YTtgFWVqMTwkb3UMHgFBdBQuJUJeImQfbHkhZRFAbT8YK3tpEC4TR15kJytrYAMWEXwABBoRVV0RByJXaxARPmNrMREfeWEzMBR8aBAWC0tZAj8CfU8fJRN+fWIdd38BHDwTWV1kJydrUWRhHkBuPhwrRQ0cBgNLXThkDmNPEz4jf2kmEHZJXBoufm5hHSwhSEATEiB+eQ8cLWtcEjwfbG8DPxVhexM6F3l9ZBEQSVAUPgdsXWc/EmFsE20feUwyEnRZSRQtH2tuBBURZV8TFiJvaSAeE2ddHDkXS1pnOz5lewcfJ3x1LB91WWgTLXJ5amc7AmNAAyYfbHUCER5aeRcWKX9ZZGQUb1AbHx9FdQcEK2NKGAYxQV47EQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 902
    :cond_6
    move v7, v2

    const/4 v8, 0x6

    if-ne v7, v8, :cond_7

    .line 903
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 904
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 905
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 906
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3o3K0kPDBBydVoiFRFlaWACF299FBZ3VU8dBnZ1Wmc/PmlrBwweV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDkwK2ULDxMnb30CFgB/WxQuNVtZZzsSYnsxYiN/YQYwBHx6ECwfTnA4YAdPex8iEEoBORETb1owMxdfaQA7HmRCEx0kQGI+FSt4dBEtH0FeZmQFa1Y2Nx5vaSAaEFliDy41fmE+FSRnQTF7EFd9MxYOFFwQLAdVXQM/Ik96MQQjVQA8EXVdSQUsA0h+DRETYXAPIiRKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcofw0YAAt1WmUjLmVQEzUjRVcSFXZFExEGA3lhDRUWZFExbCN6cTYfdRQPEBMfaWkDPwJqUSEmBVpbPBUoFEAfPD4cbgBkFGx7EDcffwgyGBFJaRQtJWVdA2wCYHxgexFFfSEwK3tOFDwAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfbHkhZRFAbT8YK3tpEC4TR15kJytrYAMWEXwABBoRVV0RByJXaxARPmNrMREfeWEzMBR8aBAWJQZwZztyfUADOCQfaTgRKnsJFzwTXGACMxNheQQwIFV2Ph4eHF4cBgNLXThkDmNPEz4jf2kmEHZJXBoufmthHT8kZ1AbeyN8CA8cLlVoEBAxbG4CEW1oaWA9E3l9FhwrXUAXPANscA0dFWtCExgfeUwyESt/eRgAMUddDWQRZXs9AgV5eW0wLWddHDkHRGEDDiF3ahMSJ3x1Zh92e04TFwt9WWcncnd7ISUnRVsCEhEUYB88EB5dZBUXa1AYEx9/dQcEK3t6FGYPQV0CETxgez0SBUVxIRkTWVEUPSV+YT4RIGdAG38geV8CEQAcWRcGH3lpO2AXa2sXbSBqfWYeA10OBTwPb24EbBdoewMYHkRxLwR0XQ4QLHJDXmc/LmVQE2Meb2o4GR57URQWD1lZZBE+aFEXYQdpYSIRdFlbED4xS1oSYAVob2Q7J3p5bBF1QQETPANfYAAVJH1fMWweelt6EStrSxBmdktdACM/aGsheyN/XxQZE0lcEQMXf2INJ39sXwNhI3oIIh8uf3cTFgQGahIRBWd8AzsjV30YHh5VWhEWfxxvADMXawsfYSdAW2IVLRRLHS0PR3BmZB5leyYBJ2xPAhAEFEkULBN4Wh0jAmhAbHsifFsEH3RjcxMTH3leEmAiaGlgOx9sdRocdBQNFxYheW8CHRN9VmAhHkp6Jh0BSUkSZ35KXQNsBWhpFDcRf1sSHHVBSBpmB3VZZyceT2sXJSB6YTAVdx13FwAxfVpnYAh9QGwiF2pPDB53SXQTLB9/XWU/C2V8AxweRXomFwFFTxEuckheZDchYFFgAhNvCBIRdBhiFCwfRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 907
    :cond_7
    move v7, v2

    const/4 v8, 0x7

    if-ne v7, v8, :cond_8

    .line 908
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 909
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 910
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 911
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTgRImJAHyQTankGES4ZdRgWAB5wHRkVaHAcEyRXXzEVK0FIDBcXeVk7PwFlaz0CFW9fAhUEHFAPPHZ4YhARAmtQbGETHlcMMANBVxIVBFRcAzsva2oTOBNqeRYSERReFDwTaH8QbCJvUWRjEX8IAx0BVWoXEA9/bmZkAmULDyYlf30CFnccXBQuNVtZZzsSYkAbJyd/VzEfBH9dExAxeVkdbBdPex8iE3pqPxEeFXEULHZvbgI7Hmh5bHskQGI+FSt4dBgGH0FqDRECZVAbJhVsACAyLX9dFBMxfmE7OyRnaxd7FVp1MxYOFFwQLAdVXQM/InR6Hz8XbAA4ESp/ARcsA0h+DRETYXAPIiRKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcoHGgdLn51WhQVcmhrJXsHf1smFXZFExEGA3lhDRUWZFEHHyV6cTYfdnh2GywfZWsTZAdkCmQiI391GhIeFA0fPCl5YWQ/FmJqMSYRemomHC1FeRAjD0NeOz92aGkXFhN/fSE3LlVdGhYAWVlnJw5iUDFhIEBxBxUDRnoSFzFLYTtgEmRAMTwjeX0WHChVCTA8dl9vAD8iYWpsECdXcQcYK3tpEC4TR2oDbTJve2EBHm9PYBJ2RVARADVGYRMRDmNrA2UQVHEHEXVZYBAQLUtZAj8CfQsfOCQfaTwddRQJFzwTXGACMxNheQQwIFV2Ph4eHF4cBgNLXiIzcWBWA3sjfE8NEHZjSQ8TMWthHTsOaFExEiB5ADYwE2tcEjwfbG4CEW1oaWA9E3l9FhErXUAbPgd8XWc/EmFsE20feUwyEhF7SRgAMUZaZgELZV8TFiJvaSAeE0VdHDkHRGECJDVoezEMJXxbDB92e14TFwt9WWcncnd7ISUnRVsCHCp7SBQ8AB5dZGQUb0EDfx9/dQcEK2NKGAYxQV47EQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 912
    :cond_8
    move v7, v2

    const/16 v8, 0x8

    if-ne v7, v8, :cond_9

    .line 913
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 914
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 915
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 916
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWHQ5VQBEAB1xwHR0Xb2kxDSRXXzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHldmH3ZGaBEWBFRcAzsva2oTOxNsaRodKBRIHBYcH2JlOxNheRt/EXppBxUrWWk3EwN5cDkwK2ULDxMnb30CFgB/WxQuNVtZZzsSYnsxYiN/YQYwBHx6ECwfTnA4YAdPex8iEEoBORETb1owMxdfaQA7HmRCEx0kQGI+FSt4dBgGH0FqDRECZVAbJhVsACAyLX9dFBMxfmE7OyRnaxd7FVp1MxYOFFwQLAdVXQM/Ik96MTMXb0whHhFaehoGA0h+DRETYXAPIid8eWIcK397EGYDQVkDGitrCwciAn9xJhx1VU0RACEcYR0gLWdQAycHeV8DMD5/WhEtdmVvAhFtaGxgJydaWzYWdFVQFywPYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhgAzAVfG0mFXZFExEGA3lhDRUgSEExHwVqcTYfdRQPEBMfaWkDPwJqUSEmBVpbPBUoFEAfPD4cbgBkFG9fGyERemomHC1FeRAjD0NeOz92aGkXFhN/fSE3LlVdGhYAVWE+EQ5iUTECEmpxNjAQe2kTLTF9YTtgEmRAMTwjeX0WHChVCTA8dl9vAD8iYWpsECdXcQcYK3tpEC4TR2oDbTJve2EBHm9PYBJ2RVARADVGYRMRDmNrA2UQVHEHEXVZYBAQLUtZAjtyYkEfOyRXUzwddRQJBRYTQn4UZCB9ezlhH3ppehIrZ0kYBx9/XTg7K2V7ZBIjfE82Gh5VThpmF2thHT8kZ1AbeyN8CA8cLlVoEBAxbG4CEW1oaWA9E3l9FhwrXUAXPANscA0dFWtCExgfeUwyESt/eRgAMUddDWQRZXs9AgV5eW0wKkETExMHS1odPy53ahMVJHkADB91WUgQF3J5amc7AmNAAyYfbAAGFXd7ARA8EBxpAxUUb1AbHydVeQIaHn9pNxMPQV4SEXRqehsmHm95BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 917
    :cond_9
    move v7, v2

    const/16 v8, 0x9

    if-ne v7, v8, :cond_a

    .line 918
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 919
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 920
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 921
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3o3K0kPDBBydVoiFRFoUAQOIn9pIBZ2Z0oULAN+WWQRAmtQbGETHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrRWoXEA9DXTtkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9dExAxeVkdFRJrag9tJWwBMhx2ew0XLBNfbwI/dWhPGx0kQGI+FSt4dBEtH0FeZmQFZVBgYx5vajgWdRhRDwYTfmE7OyRnaxt7H3xhBxIOFGIQLTFLbDsVcnR5MTMXb0whHhFaehoGA0h+DRETYXAPIid8eWIcK397EGYDQVkDGitrCwciAn9xJhx1VU0RACEcYR0gLWdQAycHeV8DMD5/WhEtdmVvAhFtaGxgJydaWzYWdFVQFywPYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidEYTYwBH9xGywfS2sTYBN9CmQmJ1R+Mh0rFF4QPD4fWWU/FG9fGyEfSnUDHgFJaRQtJWVaOz81anphAR5vdQIXd3tdGhYAVWE+EQ5iUTECHmpxBhEAHXISFzFLYTtgEmRAMTwjeX0WHChVCTA8D0heImQfbHAPZRFAbTIZK3tfFBUDRF0NODJrYA8SHlVpBBd2RVARADVGYRMRDmNrA2IlfGEHERAYDRAWJQZwZztyfUADOCQfaTged38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9/XTg/JGB5Ez4jfE8NEHZjXRoufx9ZEj8ea1AbeyB+fWYYKntcEjwfbG8DOChnT2A9E2l+PR0Db0AXLBNpajs7FWtCExgfeUwyEnRZSh0tD3lZPmwLZV8TFiJvaSAeE2ddHDkXS1odPy53ahMVJHkADB91WUgQF3J5amRhLWdBISYfbHUCER5aeRcWKXxdZBUXa1AYEx9/dQcELRQPEmYPQV0CETxgegdsJ295Ah4Xd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 922
    :cond_a
    move v7, v2

    const/16 v8, 0xa

    if-ne v7, v8, :cond_b

    .line 923
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 924
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 925
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 926
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3oYdEloEi0lQ285JxFlUGACFW9fAhUEVUoRBi17Xg0/PmhRBDInen0zFRFOaBEWBFRcAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDkwK2ULDxMnb30CFgB/XRoTB2lZZxUOYkExEhFUcQIwFHx6HS4xeVkdYBJkQA8mElR+PxwtXUAFLANfWmY7HmRCEx0kQGI+FSt4dBEtH0FeZmQFZVBgYx5vaSAyd3tdFBMxfmE7Jz5peQMiH3xhBxIOFFwQLAdVXQM/ImtqHyYnSgEhHhFaehg8Ph9dImQeYXobMiRacXowEUVoEmYDQVkDGitrCwciAn9xJhx1VU0RAxdrWWQNdGNAAxIjenkCMANrURAXMX1wEhFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 927
    :cond_b
    move v7, v2

    const/16 v8, 0xb

    if-ne v7, v8, :cond_c

    .line 928
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 929
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 930
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 931
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3oYdEloEi0lQ285JxFlUGACFW9fAhUEVUoRBi17Xg0/PmhRBDInen0zFRFOaBEWBFRcAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDkwK2ULDxMnb30CFgB/XRoTB2lZZxUOYkExEhFUcQIwFHx6HS4xeVkdYBJkQA8mElR+PxwtXUAFLANfWmY7HmRCEx0kQGI+FSt4dBEtH0FeZmQFZVBgYx5vaSAyd3tdFBMxfmE7Jz5peQMiH3xhBxIOFFwQLAdVXQM/ImtqHyYnSgEhHhFaehg8Ph9dImQeYXobMiRacXowEUVoEmYDQVkDGitrCwciAn9xJhx1VU0RAxdrWWQNdGNAAxIjenkCMANrURAXMX1wEhFtaGAHMxN/XGIRAEkBGCwfYn4TIxdheTECHnx1eh0tFA4YBRdHbgQVfmhgAzAVfG0mFXZFExEGA3lhDRUgSEExHwVqcTYfdRQPEBMfaWkDPwJqa2w4BVpcMh0rFAEfPCIfYWQ/FmJqMSYRemomHC1FeQwcNXxvO2QFYHkbJhN/eQ0ZHhxPEDwAVWE+EQ5iUTECHmpxBhEAHXISFzFLYTtgEmRAMTwjeX0WHChVCTA8dkJeImAiYVZgZRFFaQM3K0VvFCMPa2s4ZCtqahcWFW9XJhV0VV0RByJXaxARPmNrMRwlfAgiFQNrcxAWJQZwZztydGtkJR9vdTwdd38TGDx2X34TOyB9UQMyIFV2Ph4eHF4cBgNLXiIzcWBWA3sjfE8NEHZjSQ8TMW9hHTgubGoTEgVvdTYwKkFoEBAxbG8DPxVhexM9F29LPB0oFFoYFjFcXWY/Imh8E20feVAyFXV/Dx0tD2tuBBURZXs9AgV5eW0wLWddHDkHRGEDDiF3ahMSJ3xbDB91WV4TFhNpahNhLWNAAyYfbAAGFXd7ARA8EBxpAxUUb0ITDR9/CDsEK3toFC4hfG4dEQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 932
    :cond_c
    move v7, v2

    const/16 v8, 0xc

    if-ne v7, v8, :cond_d

    .line 933
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 934
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 935
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 936
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3oYdEloEi0lQ285JxFlUGACFW9fAhUEVUoRBi17Xg0/PmhRBDInen0zFRFOaBEWBFRcAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDkwK2ULDxMnb30CFgB/XRoTB2lZZxUOYkExEhFUcQIwFHx6HS4xeVkdYBJkQA8mElR+PxwtXUAFLANfWmY7HmRCEx0kQGI+FSt4dBEtH0FeZmQFZVBgYx5vaSAyd3tdFBMxfmE7Jz5peQMiH3xhBxIOFFwQLAdVXQM/ImtqHyYnSgEhHhFaehg8Ph9dImQeYXobMiRacXowEUVoEmYDQVkDGitrCwciAn9xJhx1VU0RAxdrWWQNdGNAAxIjenkCMANrURAXMX1wEhFtaGAHMxN/XGIRAEkBGCwfYn4TIxdheTECHnx1eh0tFA4YBRdHbgQVfmhgAzAVfG0mFXZFExEGA3lhDRUgSEExHwVqcTYfdRQPEBMfaWkDPwJqa2w4BVpcMh0rFAEfPCIfYWQ/FmJqMSYRemomHC1FeQwcNXxvO2QFYHkbJhN/eQ0ZHhxPEDwAVWE+EQ5iUTECHmpxBhEAHXISFzFLYTtgEmRAMTwjeX0WHChVCTA8dkJeImAiYVZgZRFFaQM3K0VvFCMPa2s4ZCtqahcWFW9XJhV0VV0RByJXaxARPmNrMRwlfAgiFQNrcxAWC0tZAj8CfU8fJRN+fTgWEF1aBRYTQn4QFSB9fGE3JEp6PhIBVXkYBx9/XTg/JGB5Ez4jf2kmEHZJXBoufmthHT8kZ1AbeyB+fQ8cLWtpEjwfbG8DPxVhexM9F29LPB0oFFoYFjFcXWY/Imh8E20feVAyFXV/Dx0tD2tuBBURZXs9AgV5eW0wLWddHDkHRGEDDiF3ahMSJ3xbDB91WV4TFhNpahNhLWNAAyYfbAAGFXd7ARA8EBxpAxUUb0ITDR9/CDsEK3toFC4hfG4dEQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 937
    :cond_d
    move v7, v2

    const/16 v8, 0xd

    if-ne v7, v8, :cond_e

    .line 938
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 939
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 940
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 941
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTgRImJAHyQTankGES4ZdRgWAB5wHRkVaHAcEyRXXzEVK0FIDBcXeV0NZS1oayVjFW9pfhZ3VU8dBnZ+WR0VIGlrBwweV30DH3ZrcREWJXVdAz9xfU8TOCdXCBYSERReFDwTaHATFRZ9cA9gH0V5BxUrWWoSLDF5cGZkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9dExAxeVkdbBdPex8iE3pqPxEeFXEULHZvbgI7Hmh5bHskQGI+FSt4dBgGH0FqDRECZVAbJhVsACAyLX9dFBMxfmE7OyRnaxd7FVp1MxYOFFwQLAdVXQM/InR6Hz8XbAA4ESp/ARcsA0h+DRETYXAfeydKCGIcK397ECx2Rm5kGXFveyFgIm9bJjcrGF0cPgtEYR0nf3RAYCInfnksMHVZXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4SHRdhbBMYJGlLAxIRZ2gdZiVHazIRLmVgAAEif1cSFXZFFxAWE0NhDTg1YFEHNSV6cTYfdRQPEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 942
    :cond_e
    move v7, v2

    const/16 v8, 0xe

    if-ne v7, v8, :cond_0

    .line 943
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$seekBar4:Landroid/widget/SeekBar;

    move v8, v2

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 944
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$interInt:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 945
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000013;->val$textView4:Landroid/widget/TextView;

    move-object v5, v7

    .line 946
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, " "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000013;->val$feature:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWHQ5VQBEAB1xwHR0Xb2kxDSRXXzEVK1lJGAclQ2kdPwFoa2EOIn9pfhZ2Z0oRBi17Xg0/PmhRBDInen0zFRFOaBEWBFRcAzsva2oTOxNsaRodKBRaFxYcHm4AMxN9cA9lEX8IAx0BQUoXIzF5cDk3C2B5GxYQVXEDFgB/WxQuNVtZZzsSYnobJSN/YQYRFH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfaQA7HmRCE2ElaVMiH3R4dBEsC0NaZmQFa18XexFVaSAaEFliDy41fmE+FSRnQTF7EFd9MxYOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacXowEUVoEmYDQVkDGitrCwciAn9xJhx1VU0RACEcYR0gLWdQAycHeV8QHy1BahEjH3leOBFxYkEHOidVTGIcdVVQFywPYn4TIxdheTECHnx1ehIefw0YAHZ1WhMnfmhgAzAVfG0mGRBBSBosA1VhEBU+YFExbCN6cTYfdRQPEBMfaWkDPwJqUSEmBVpbPBUoFEAfPD4cbgBkFG9fGyERemomHC1FeQwcNXxvO2QFYHkbJhN/eQ0ZHhxPEDwAVWJlHiFnextsInlPNjAQe2kTLTF9YTtgEmRAMTwjeX0WHChVCTA8dll+FGAfb2oxHCRKaQMyK3tPFy4TR2pmODJrYA8SA1V9EhV1QVARBzFYYR0nPmVPAzgQVHEHEXVZYBAQLUtZAj8CfU8fJRN+fWIdd38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9LazsBBWVqYHsjfE8NEHZjSQ8TMWthHTsOaFExEiB5ADYwE2tcEjwfbG8DOChnT2A9E2l+PR0Db0AXLBNpajs7FWtCExgfeUwyEhF7SRgAMUZaZgELZV8TFiJvaSAeE0VdHDkHRGECJDVoezEMJXxbDB92e14TFwt9WWcncnd7ISUnRVsCHCp7SBQ8AB5dZGQUb0EDfx9/dQcEK2NKGAYxQV47EQ5oYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8HMAR8cxcHMXlgEhUFYEAbPCN8aSIdAHsKBTwPQnAUZBVhaRsYJ1dLFxUre14QLHJDXmc/LmVQE2MRf1cgMi5VYhotF1haEiQhZFEfbCV5YSIRdFlbED4xTm4SbAJiez06I1V1bB12SRMFBnZpWmUVHmF6GyQnR0tiHStFABBmdktdACM/aFATYxV/CCAyKHtdHQUDf2INFQdsUQN7J355MBF2e1wdEC1la2Q7F2tsYDgjWlsDHHZJQA88A0hgODMiZE8xOB56aWIRdFlIHS4LRlk7P3FrX2A1IEdpAzE+bAU="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0
.end method

.method public onStartTrackingTouch(Landroid/widget/SeekBar;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/SeekBar;",
            ")V"
        }
    .end annotation

    return-void
.end method

.method public onStopTrackingTouch(Landroid/widget/SeekBar;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/SeekBar;",
            ")V"
        }
    .end annotation

    return-void
.end method
