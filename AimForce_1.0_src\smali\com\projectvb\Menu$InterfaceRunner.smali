.class Lcom/projectvb/Menu$InterfaceRunner;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Ljava/lang/Runnable;

# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;

# direct methods
.method constructor <init>(Lcom/projectvb/Menu;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Menu;

    .prologue
    .line 600
    iput-object p1, p0, Lcom/projectvb/Menu$InterfaceRunner;->this$0:Lcom/projectvb/Menu;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# virtual methods
.method public run()V
    .locals 1

    .prologue
    .line 603
    :try_start_0
    iget-object v0, p0, Lcom/projectvb/Menu$InterfaceRunner;->this$0:Lcom/projectvb/Menu;
    invoke-virtual {v0}, Lcom/projectvb/Menu;->createPMMStyleInterface()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    # If failed, try simple interface
    iget-object v0, p0, Lcom/projectvb/Menu$InterfaceRunner;->this$0:Lcom/projectvb/Menu;
    invoke-virtual {v0}, Lcom/projectvb/Menu;->createSimpleInterface()V

    :goto_end
    return-void
.end method
