.class Lpmm/by/p2077kng/hacker/Loader$100000021;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000021"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;

.field private final val$sw:Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Loader$100000021;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Loader$100000021;->val$sw:Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000021;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000021;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/CompoundButton;",
            "Z)V"
        }
    .end annotation

    .prologue
    .line 1079
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000021;->val$sw:Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;

    move v5, v2

    invoke-interface {v4, v5}, Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;->OnWrite(Z)V

    .line 1080
    move v4, v2

    if-eqz v4, :cond_0

    .line 1082
    :goto_0
    return-void

    :cond_0
    goto :goto_0
.end method
