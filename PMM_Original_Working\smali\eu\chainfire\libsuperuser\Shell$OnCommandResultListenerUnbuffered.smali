.class interface abstract Leu/chainfire/libsuperuser/Shell$OnCommandResultListenerUnbuffered;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnResult;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x60a
    name = "OnCommandResultListenerUnbuffered"
.end annotation


# virtual methods
.method public abstract onCommandResult(II)V
.end method
