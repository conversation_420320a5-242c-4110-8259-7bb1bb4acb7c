.class Lcom/projectvb/Login$1$1;
.super Ljava/lang/Object;
.source "Login.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Login$1;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lcom/projectvb/Login$1;


# direct methods
.method constructor <init>(Lcom/projectvb/Login$1;)V
    .locals 0
    .param p1, "this$1"    # Lcom/projectvb/Login$1;

    .line 70
    iput-object p1, p0, Lcom/projectvb/Login$1$1;->this$1:Lcom/projectvb/Login$1;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 73
    iget-object v0, p0, Lcom/projectvb/Login$1$1;->this$1:Lcom/projectvb/Login$1;

    iget-object v0, v0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    invoke-static {v0}, Lcom/projectvb/Login;->access$300(Lcom/projectvb/Login;)V

    .line 74
    return-void
.end method
