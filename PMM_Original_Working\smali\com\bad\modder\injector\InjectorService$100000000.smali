.class Lcom/bad/modder/injector/InjectorService$100000000;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000000"
.end annotation


# instance fields
.field private final this$0:Lcom/bad/modder/injector/InjectorService;

.field private final val$handler:Landroid/os/Handler;


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/InjectorService;Landroid/os/Handler;)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lcom/bad/modder/injector/InjectorService$100000000;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Lcom/bad/modder/injector/InjectorService$100000000;->val$handler:Landroid/os/Handler;

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/InjectorService$100000000;)Lcom/bad/modder/injector/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000000;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public run()V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 237
    move-object v0, p0

    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/InjectorService$100000000;->this$0:Lcom/bad/modder/injector/InjectorService;

    invoke-static {v2}, Lcom/bad/modder/injector/InjectorService;->access$1000028(Lcom/bad/modder/injector/InjectorService;)V

    .line 238
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/InjectorService$100000000;->this$0:Lcom/bad/modder/injector/InjectorService;

    invoke-static {v2}, Lcom/bad/modder/injector/InjectorService;->access$1000075(Lcom/bad/modder/injector/InjectorService;)V

    .line 239
    move-object v2, v0

    iget-object v2, v2, Lcom/bad/modder/injector/InjectorService$100000000;->val$handler:Landroid/os/Handler;

    move-object v3, v0

    const/16 v4, 0x3e8

    int-to-long v4, v4

    invoke-virtual {v2, v3, v4, v5}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    move-result v2

    return-void
.end method
