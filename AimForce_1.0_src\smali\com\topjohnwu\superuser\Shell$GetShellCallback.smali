.class public interface abstract Lcom/topjohnwu/superuser/Shell$GetShellCallback;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/topjohnwu/superuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "GetShellCallback"
.end annotation


# virtual methods
.method public abstract onShell(Lcom/topjohnwu/superuser/Shell;)V
.end method
