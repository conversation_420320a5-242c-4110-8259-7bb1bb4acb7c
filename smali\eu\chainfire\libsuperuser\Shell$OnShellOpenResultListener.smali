.class public interface abstract Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnResult;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnShellOpenResultListener"
.end annotation


# virtual methods
.method public abstract onOpenResult(ZI)V
.end method
