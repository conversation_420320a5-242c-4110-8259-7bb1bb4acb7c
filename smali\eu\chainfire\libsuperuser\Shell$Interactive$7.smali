.class Leu/chainfire/libsuperuser/Shell$Interactive$7;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->open()Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V
    .locals 4

    .prologue
    .line 2127
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onLine(Ljava/lang/String;)V
    .locals 14
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 2131
    move-object v0, p0

    move-object v1, p1

    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v9

    move-object v2, v9

    .line 2132
    move-object v9, v2

    if-eqz v9, :cond_1

    move-object v9, v2

    invoke-static {v9}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v9

    if-eqz v9, :cond_1

    .line 2134
    move-object v9, v1

    const-string v10, "inputstream"

    invoke-virtual {v9, v10}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_1

    .line 2135
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v9

    if-eqz v9, :cond_0

    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v9

    invoke-virtual {v9}, Leu/chainfire/libsuperuser/StreamGobbler;->suspendGobbling()V

    .line 2175
    :cond_0
    :goto_0
    return-void

    .line 2140
    :cond_1
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    move-object v3, v10

    monitor-enter v9

    .line 2141
    move-object v9, v0

    :try_start_0
    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v9

    if-nez v9, :cond_2

    .line 2142
    move-object v9, v3

    monitor-exit v9

    goto :goto_0

    .line 2145
    :cond_2
    move-object v9, v1

    move-object v4, v9

    .line 2146
    const/4 v9, 0x0

    move-object v5, v9

    .line 2148
    move-object v9, v1

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v10

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v9

    move v6, v9

    .line 2149
    move v9, v6

    if-nez v9, :cond_6

    .line 2150
    const/4 v9, 0x0

    move-object v4, v9

    .line 2151
    move-object v9, v1

    move-object v5, v9

    .line 2157
    :cond_3
    :goto_1
    move-object v9, v4

    if-eqz v9, :cond_4

    .line 2158
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v10, v4

    const/4 v11, 0x0

    invoke-static {v9, v10, v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3800(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Z)V

    .line 2159
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v10, v4

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3900(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static {v9, v10, v11, v12}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4000(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Ljava/lang/Object;Z)V

    .line 2160
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v10, v4

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v11

    invoke-static {v11}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static {v9, v10, v11, v12}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4000(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Ljava/lang/Object;Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2163
    :cond_4
    move-object v9, v5

    if-eqz v9, :cond_5

    .line 2165
    move-object v9, v0

    :try_start_1
    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v10, v5

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    .line 2166
    invoke-static {v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v11

    invoke-static {v11}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/String;->length()I

    move-result v11

    const/4 v12, 0x1

    add-int/lit8 v11, v11, 0x1

    invoke-virtual {v10, v11}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v10

    const/16 v11, 0xa

    .line 2165
    invoke-static {v10, v11}, Ljava/lang/Integer;->valueOf(Ljava/lang/String;I)Ljava/lang/Integer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/Integer;->intValue()I

    move-result v10

    invoke-static {v9, v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4102(Leu/chainfire/libsuperuser/Shell$Interactive;I)I
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    move-result v9

    .line 2171
    :goto_2
    move-object v9, v0

    :try_start_2
    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v10

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v9, v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4202(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    .line 2172
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive$7;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4300(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    .line 2174
    :cond_5
    move-object v9, v3

    monitor-exit v9

    .line 2175
    goto/16 :goto_0

    .line 2152
    :cond_6
    move v9, v6

    if-lez v9, :cond_3

    .line 2153
    move-object v9, v1

    const/4 v10, 0x0

    move v11, v6

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v9

    move-object v4, v9

    .line 2154
    move-object v9, v1

    move v10, v6

    invoke-virtual {v9, v10}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v9

    move-object v5, v9

    goto/16 :goto_1

    .line 2167
    :catch_0
    move-exception v9

    move-object v7, v9

    .line 2169
    move-object v9, v7

    invoke-virtual {v9}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_2

    .line 2174
    :catchall_0
    move-exception v9

    move-object v8, v9

    move-object v9, v3

    monitor-exit v9
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-object v9, v8

    throw v9
.end method
