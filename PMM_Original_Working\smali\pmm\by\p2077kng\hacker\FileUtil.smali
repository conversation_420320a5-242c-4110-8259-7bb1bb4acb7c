.class public Lpmm/by/p2077kng/hacker/FileUtil;
.super Ljava/lang/Object;
.source "FileUtil.java"


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 603
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static calculateInSampleSize(Landroid/graphics/BitmapFactory$Options;II)I
    .locals 12

    .prologue
    .line 366
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v10, v0

    iget v10, v10, Landroid/graphics/BitmapFactory$Options;->outWidth:I

    move v4, v10

    .line 367
    move-object v10, v0

    iget v10, v10, Landroid/graphics/BitmapFactory$Options;->outHeight:I

    move v5, v10

    .line 368
    const/4 v10, 0x1

    move v6, v10

    .line 370
    move v10, v5

    move v11, v2

    if-gt v10, v11, :cond_0

    move v10, v4

    move v11, v1

    if-le v10, v11, :cond_1

    .line 371
    :cond_0
    move v10, v5

    const/4 v11, 0x2

    div-int/lit8 v10, v10, 0x2

    move v7, v10

    .line 372
    move v10, v4

    const/4 v11, 0x2

    div-int/lit8 v10, v10, 0x2

    move v8, v10

    .line 374
    :goto_0
    move v10, v7

    move v11, v6

    div-int/2addr v10, v11

    move v11, v2

    if-lt v10, v11, :cond_1

    move v10, v8

    move v11, v6

    div-int/2addr v10, v11

    move v11, v1

    if-ge v10, v11, :cond_2

    .line 379
    :cond_1
    move v10, v6

    move v0, v10

    return v0

    .line 375
    :cond_2
    move v10, v6

    const/4 v11, 0x2

    mul-int/lit8 v10, v10, 0x2

    move v6, v10

    goto :goto_0
.end method

.method public static convertUriToFilePath(Landroid/content/Context;Landroid/net/Uri;)Ljava/lang/String;
    .locals 17

    .prologue
    .line 229
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    const/4 v11, 0x0

    check-cast v11, Ljava/lang/String;

    move-object v3, v11

    .line 230
    move-object v11, v0

    move-object v12, v1

    invoke-static {v11, v12}, Landroid/provider/DocumentsContract;->isDocumentUri(Landroid/content/Context;Landroid/net/Uri;)Z

    move-result v11

    if-eqz v11, :cond_7

    .line 231
    move-object v11, v1

    invoke-static {v11}, Lpmm/by/p2077kng/hacker/FileUtil;->isExternalStorageDocument(Landroid/net/Uri;)Z

    move-result v11

    if-eqz v11, :cond_1

    .line 232
    move-object v11, v1

    invoke-static {v11}, Landroid/provider/DocumentsContract;->getDocumentId(Landroid/net/Uri;)Ljava/lang/String;

    move-result-object v11

    move-object v4, v11

    .line 233
    move-object v11, v4

    const-string v12, ":"

    invoke-virtual {v11, v12}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v11

    move-object v5, v11

    .line 234
    move-object v11, v5

    const/4 v12, 0x0

    aget-object v11, v11, v12

    move-object v6, v11

    .line 236
    const-string v11, "primary"

    move-object v12, v6

    invoke-virtual {v11, v12}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_0

    .line 237
    new-instance v11, Ljava/lang/StringBuffer;

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v12, Ljava/lang/StringBuffer;

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    invoke-direct {v13}, Ljava/lang/StringBuffer;-><init>()V

    invoke-static {}, Landroid/os/Environment;->getExternalStorageDirectory()Ljava/io/File;

    move-result-object v13

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v12

    const-string v13, "/"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v12

    invoke-virtual {v12}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v5

    const/4 v13, 0x1

    aget-object v12, v12, v13

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    move-object v3, v11

    .line 279
    :cond_0
    :goto_0
    move-object v11, v3

    if-eqz v11, :cond_9

    .line 281
    move-object v11, v3

    :try_start_0
    const-string v12, "UTF-8"

    invoke-static {v11, v12}, Ljava/net/URLDecoder;->decode(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v11

    move-object v0, v11

    .line 286
    :goto_1
    return-object v0

    .line 239
    :cond_1
    move-object v11, v1

    invoke-static {v11}, Lpmm/by/p2077kng/hacker/FileUtil;->isDownloadsDocument(Landroid/net/Uri;)Z

    move-result v11

    if-eqz v11, :cond_3

    .line 240
    move-object v11, v1

    invoke-static {v11}, Landroid/provider/DocumentsContract;->getDocumentId(Landroid/net/Uri;)Ljava/lang/String;

    move-result-object v11

    move-object v4, v11

    .line 242
    move-object v11, v4

    invoke-static {v11}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v11

    if-nez v11, :cond_2

    .line 243
    move-object v11, v4

    const-string v12, "raw:"

    invoke-virtual {v11, v12}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_2

    .line 244
    move-object v11, v4

    const-string v12, "raw:"

    const-string v13, ""

    invoke-virtual {v11, v12, v13}, Ljava/lang/String;->replaceFirst(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    move-object v0, v11

    goto :goto_1

    .line 248
    :cond_2
    const-string v11, "content://downloads/public_downloads"

    invoke-static {v11}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v11

    move-object v12, v4

    invoke-static {v12}, Ljava/lang/Long;->valueOf(Ljava/lang/String;)Ljava/lang/Long;

    move-result-object v12

    check-cast v12, Ljava/lang/Long;

    invoke-virtual {v12}, Ljava/lang/Long;->longValue()J

    move-result-wide v12

    invoke-static {v11, v12, v13}, Landroid/content/ContentUris;->withAppendedId(Landroid/net/Uri;J)Landroid/net/Uri;

    move-result-object v11

    move-object v5, v11

    .line 251
    move-object v11, v0

    move-object v12, v5

    const/4 v13, 0x0

    check-cast v13, Ljava/lang/String;

    const/4 v14, 0x0

    check-cast v14, [Ljava/lang/String;

    invoke-static {v11, v12, v13, v14}, Lpmm/by/p2077kng/hacker/FileUtil;->getDataColumn(Landroid/content/Context;Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    move-object v3, v11

    goto :goto_0

    .line 252
    :cond_3
    move-object v11, v1

    invoke-static {v11}, Lpmm/by/p2077kng/hacker/FileUtil;->isMediaDocument(Landroid/net/Uri;)Z

    move-result v11

    if-eqz v11, :cond_0

    .line 253
    move-object v11, v1

    invoke-static {v11}, Landroid/provider/DocumentsContract;->getDocumentId(Landroid/net/Uri;)Ljava/lang/String;

    move-result-object v11

    move-object v4, v11

    .line 254
    move-object v11, v4

    const-string v12, ":"

    invoke-virtual {v11, v12}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v11

    move-object v5, v11

    .line 255
    move-object v11, v5

    const/4 v12, 0x0

    aget-object v11, v11, v12

    move-object v6, v11

    .line 257
    const/4 v11, 0x0

    check-cast v11, Landroid/net/Uri;

    move-object v7, v11

    .line 258
    const-string v11, "image"

    move-object v12, v6

    invoke-virtual {v11, v12}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_5

    .line 259
    sget-object v11, Landroid/provider/MediaStore$Images$Media;->EXTERNAL_CONTENT_URI:Landroid/net/Uri;

    move-object v7, v11

    .line 266
    :cond_4
    :goto_2
    new-instance v11, Ljava/lang/StringBuffer;

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, "_id"

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    const-string v12, "=?"

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    move-object v8, v11

    .line 267
    const/4 v11, 0x1

    new-array v11, v11, [Ljava/lang/String;

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    const/4 v13, 0x0

    move-object v14, v5

    const/4 v15, 0x1

    aget-object v14, v14, v15

    aput-object v14, v12, v13

    move-object v9, v11

    .line 271
    move-object v11, v0

    move-object v12, v7

    move-object v13, v8

    move-object v14, v9

    invoke-static {v11, v12, v13, v14}, Lpmm/by/p2077kng/hacker/FileUtil;->getDataColumn(Landroid/content/Context;Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    move-object v3, v11

    goto/16 :goto_0

    .line 260
    :cond_5
    const-string v11, "video"

    move-object v12, v6

    invoke-virtual {v11, v12}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_6

    .line 261
    sget-object v11, Landroid/provider/MediaStore$Video$Media;->EXTERNAL_CONTENT_URI:Landroid/net/Uri;

    move-object v7, v11

    goto :goto_2

    .line 262
    :cond_6
    const-string v11, "audio"

    move-object v12, v6

    invoke-virtual {v11, v12}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_4

    .line 263
    sget-object v11, Landroid/provider/MediaStore$Audio$Media;->EXTERNAL_CONTENT_URI:Landroid/net/Uri;

    move-object v7, v11

    goto :goto_2

    .line 273
    :cond_7
    sget-object v11, Landroid/content/ContentResolver;->SCHEME_CONTENT:Ljava/lang/String;

    move-object v12, v1

    invoke-virtual {v12}, Landroid/net/Uri;->getScheme()Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_8

    .line 274
    move-object v11, v0

    move-object v12, v1

    const/4 v13, 0x0

    check-cast v13, Ljava/lang/String;

    const/4 v14, 0x0

    check-cast v14, [Ljava/lang/String;

    invoke-static {v11, v12, v13, v14}, Lpmm/by/p2077kng/hacker/FileUtil;->getDataColumn(Landroid/content/Context;Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    move-object v3, v11

    goto/16 :goto_0

    .line 275
    :cond_8
    sget-object v11, Landroid/content/ContentResolver;->SCHEME_FILE:Ljava/lang/String;

    move-object v12, v1

    invoke-virtual {v12}, Landroid/net/Uri;->getScheme()Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_0

    .line 276
    move-object v11, v1

    invoke-virtual {v11}, Landroid/net/Uri;->getPath()Ljava/lang/String;

    move-result-object v11

    move-object v3, v11

    goto/16 :goto_0

    .line 281
    :catch_0
    move-exception v11

    move-object v4, v11

    .line 283
    const/4 v11, 0x0

    check-cast v11, Ljava/lang/String;

    move-object v0, v11

    goto/16 :goto_1

    .line 286
    :cond_9
    const/4 v11, 0x0

    check-cast v11, Ljava/lang/String;

    move-object v0, v11

    goto/16 :goto_1
.end method

.method public static copyFile(Ljava/lang/String;Ljava/lang/String;)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 107
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object v14, v0

    invoke-static {v14}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v14

    if-nez v14, :cond_0

    .line 137
    :goto_0
    return-void

    .line 108
    :cond_0
    move-object v14, v1

    invoke-static {v14}, Lpmm/by/p2077kng/hacker/FileUtil;->createNewFile(Ljava/lang/String;)V

    .line 110
    const/4 v14, 0x0

    check-cast v14, Ljava/io/FileInputStream;

    move-object v3, v14

    .line 111
    const/4 v14, 0x0

    check-cast v14, Ljava/io/FileOutputStream;

    move-object v4, v14

    .line 114
    :try_start_0
    new-instance v14, Ljava/io/FileInputStream;

    move-object/from16 v18, v14

    move-object/from16 v14, v18

    move-object/from16 v15, v18

    move-object/from16 v16, v0

    invoke-direct/range {v15 .. v16}, Ljava/io/FileInputStream;-><init>(Ljava/lang/String;)V

    move-object v3, v14

    .line 115
    new-instance v14, Ljava/io/FileOutputStream;

    move-object/from16 v18, v14

    move-object/from16 v14, v18

    move-object/from16 v15, v18

    move-object/from16 v16, v1

    const/16 v17, 0x0

    invoke-direct/range {v15 .. v17}, Ljava/io/FileOutputStream;-><init>(Ljava/lang/String;Z)V

    move-object v4, v14

    .line 117
    const/16 v14, 0x400

    new-array v14, v14, [B

    move-object v9, v14

    .line 118
    const/4 v14, 0x0

    move v10, v14

    .line 120
    :goto_1
    move-object v14, v3

    move-object v15, v9

    invoke-virtual {v14, v15}, Ljava/io/FileInputStream;->read([B)I
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v14

    move/from16 v18, v14

    move/from16 v14, v18

    move/from16 v15, v18

    move v10, v15

    const/4 v15, 0x0

    if-gt v14, v15, :cond_3

    .line 126
    :goto_2
    move-object v14, v3

    if-eqz v14, :cond_1

    .line 128
    move-object v14, v3

    :try_start_1
    invoke-virtual {v14}, Ljava/io/FileInputStream;->close()V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_3

    .line 133
    :cond_1
    :goto_3
    move-object v14, v4

    if-eqz v14, :cond_2

    .line 135
    move-object v14, v4

    :try_start_2
    invoke-virtual {v14}, Ljava/io/FileOutputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_4

    .line 137
    :cond_2
    :goto_4
    goto :goto_0

    .line 121
    :cond_3
    move-object v14, v4

    move-object v15, v9

    const/16 v16, 0x0

    move/from16 v17, v10

    :try_start_3
    invoke-virtual/range {v14 .. v17}, Ljava/io/FileOutputStream;->write([BII)V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    goto :goto_1

    .line 120
    :catch_0
    move-exception v14

    move-object v9, v14

    .line 124
    move-object v14, v9

    :try_start_4
    invoke-virtual {v14}, Ljava/io/IOException;->printStackTrace()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    goto :goto_2

    :catchall_0
    move-exception v14

    move-object v5, v14

    .line 126
    move-object v14, v3

    if-eqz v14, :cond_4

    .line 128
    move-object v14, v3

    :try_start_5
    invoke-virtual {v14}, Ljava/io/FileInputStream;->close()V
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_1

    .line 133
    :cond_4
    :goto_5
    move-object v14, v4

    if-eqz v14, :cond_5

    .line 135
    move-object v14, v4

    :try_start_6
    invoke-virtual {v14}, Ljava/io/FileOutputStream;->close()V
    :try_end_6
    .catch Ljava/io/IOException; {:try_start_6 .. :try_end_6} :catch_2

    .line 137
    :cond_5
    :goto_6
    move-object v14, v5

    throw v14

    .line 128
    :catch_1
    move-exception v14

    move-object v12, v14

    .line 130
    move-object v14, v12

    invoke-virtual {v14}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_5

    .line 135
    :catch_2
    move-exception v14

    move-object v12, v14

    .line 137
    move-object v14, v12

    invoke-virtual {v14}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_6

    .line 128
    :catch_3
    move-exception v14

    move-object v12, v14

    .line 130
    move-object v14, v12

    invoke-virtual {v14}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_3

    .line 135
    :catch_4
    move-exception v14

    move-object v12, v14

    .line 137
    move-object v14, v12

    invoke-virtual {v14}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_4
.end method

.method private static createNewFile(Ljava/lang/String;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 41
    move-object v0, p0

    move-object v6, v0

    sget-object v7, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v6, v7}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v6

    move v2, v6

    .line 42
    move v6, v2

    const/4 v7, 0x0

    if-le v6, v7, :cond_0

    .line 43
    move-object v6, v0

    const/4 v7, 0x0

    move v8, v2

    invoke-virtual {v6, v7, v8}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v6

    move-object v3, v6

    .line 44
    move-object v6, v3

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/FileUtil;->makeDir(Ljava/lang/String;)V

    .line 47
    :cond_0
    new-instance v6, Ljava/io/File;

    move-object v9, v6

    move-object v6, v9

    move-object v7, v9

    move-object v8, v0

    invoke-direct {v7, v8}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v3, v6

    .line 50
    move-object v6, v3

    :try_start_0
    invoke-virtual {v6}, Ljava/io/File;->exists()Z

    move-result v6

    if-nez v6, :cond_1

    .line 51
    move-object v6, v3

    invoke-virtual {v6}, Ljava/io/File;->createNewFile()Z
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    move-result v6

    .line 53
    :cond_1
    :goto_0
    return-void

    .line 51
    :catch_0
    move-exception v6

    move-object v4, v6

    .line 53
    move-object v6, v4

    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_0
.end method

.method public static createNewPictureFile(Landroid/content/Context;)Ljava/io/File;
    .locals 13

    .prologue
    .line 599
    move-object v0, p0

    new-instance v6, Ljava/text/SimpleDateFormat;

    move-object v12, v6

    move-object v6, v12

    move-object v7, v12

    const-string v8, "yyyyMMdd_HHmmss"

    invoke-direct {v7, v8}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;)V

    move-object v2, v6

    .line 600
    new-instance v6, Ljava/lang/StringBuffer;

    move-object v12, v6

    move-object v6, v12

    move-object v7, v12

    invoke-direct {v7}, Ljava/lang/StringBuffer;-><init>()V

    move-object v7, v2

    new-instance v8, Ljava/util/Date;

    move-object v12, v8

    move-object v8, v12

    move-object v9, v12

    invoke-direct {v9}, Ljava/util/Date;-><init>()V

    invoke-virtual {v7, v8}, Ljava/text/SimpleDateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v7, ".jpg"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    move-object v3, v6

    .line 601
    new-instance v6, Ljava/io/File;

    move-object v12, v6

    move-object v6, v12

    move-object v7, v12

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v12, v8

    move-object v8, v12

    move-object v9, v12

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    move-object v10, v0

    sget-object v11, Landroid/os/Environment;->DIRECTORY_DCIM:Ljava/lang/String;

    invoke-virtual {v10, v11}, Landroid/content/Context;->getExternalFilesDir(Ljava/lang/String;)Ljava/io/File;

    move-result-object v10

    invoke-virtual {v10}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    sget-object v10, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    move-object v9, v3

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-direct {v7, v8}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v4, v6

    .line 602
    move-object v6, v4

    move-object v0, v6

    return-object v0
.end method

.method public static cropBitmapFileFromCenter(Ljava/lang/String;Ljava/lang/String;II)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "II)V"
        }
    .end annotation

    .prologue
    .line 453
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object v14, v0

    invoke-static {v14}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v14

    if-nez v14, :cond_0

    .line 481
    :goto_0
    return-void

    .line 454
    :cond_0
    move-object v14, v0

    invoke-static {v14}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v14

    move-object v5, v14

    .line 456
    move-object v14, v5

    invoke-virtual {v14}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v14

    move v6, v14

    .line 457
    move-object v14, v5

    invoke-virtual {v14}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v14

    move v7, v14

    .line 459
    move v14, v6

    move v15, v2

    if-ge v14, v15, :cond_1

    move v14, v7

    move v15, v3

    if-ge v14, v15, :cond_1

    .line 460
    goto :goto_0

    .line 462
    :cond_1
    const/4 v14, 0x0

    move v8, v14

    .line 463
    const/4 v14, 0x0

    move v9, v14

    .line 465
    move v14, v6

    move v15, v2

    if-le v14, v15, :cond_2

    .line 466
    move v14, v6

    move v15, v2

    sub-int/2addr v14, v15

    const/4 v15, 0x2

    div-int/lit8 v14, v14, 0x2

    move v8, v14

    .line 468
    :cond_2
    move v14, v7

    move v15, v3

    if-le v14, v15, :cond_3

    .line 469
    move v14, v7

    move v15, v3

    sub-int/2addr v14, v15

    const/4 v15, 0x2

    div-int/lit8 v14, v14, 0x2

    move v9, v14

    .line 471
    :cond_3
    move v14, v2

    move v10, v14

    .line 472
    move v14, v3

    move v11, v14

    .line 474
    move v14, v2

    move v15, v6

    if-le v14, v15, :cond_4

    .line 475
    move v14, v6

    move v10, v14

    .line 477
    :cond_4
    move v14, v3

    move v15, v7

    if-le v14, v15, :cond_5

    .line 478
    move v14, v7

    move v11, v14

    .line 480
    :cond_5
    move-object v14, v5

    move v15, v8

    move/from16 v16, v9

    move/from16 v17, v10

    move/from16 v18, v11

    invoke-static/range {v14 .. v18}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIII)Landroid/graphics/Bitmap;

    move-result-object v14

    move-object v12, v14

    .line 481
    move-object v14, v12

    move-object v15, v1

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static decodeSampleBitmapFromPath(Ljava/lang/String;II)Landroid/graphics/Bitmap;
    .locals 11

    .prologue
    .line 383
    move-object v0, p0

    move v1, p1

    move v2, p2

    new-instance v6, Landroid/graphics/BitmapFactory$Options;

    move-object v10, v6

    move-object v6, v10

    move-object v7, v10

    invoke-direct {v7}, Landroid/graphics/BitmapFactory$Options;-><init>()V

    move-object v4, v6

    .line 384
    move-object v6, v4

    const/4 v7, 0x1

    iput-boolean v7, v6, Landroid/graphics/BitmapFactory$Options;->inJustDecodeBounds:Z

    .line 385
    move-object v6, v0

    move-object v7, v4

    invoke-static {v6, v7}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;Landroid/graphics/BitmapFactory$Options;)Landroid/graphics/Bitmap;

    move-result-object v6

    .line 387
    move-object v6, v4

    move-object v7, v4

    move v8, v1

    move v9, v2

    invoke-static {v7, v8, v9}, Lpmm/by/p2077kng/hacker/FileUtil;->calculateInSampleSize(Landroid/graphics/BitmapFactory$Options;II)I

    move-result v7

    iput v7, v6, Landroid/graphics/BitmapFactory$Options;->inSampleSize:I

    .line 389
    move-object v6, v4

    const/4 v7, 0x0

    iput-boolean v7, v6, Landroid/graphics/BitmapFactory$Options;->inJustDecodeBounds:Z

    .line 390
    move-object v6, v0

    move-object v7, v4

    invoke-static {v6, v7}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;Landroid/graphics/BitmapFactory$Options;)Landroid/graphics/Bitmap;

    move-result-object v6

    move-object v0, v6

    return-object v0
.end method

.method public static deleteFile(Ljava/lang/String;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 149
    move-object v0, p0

    new-instance v8, Ljava/io/File;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    move-object v10, v0

    invoke-direct {v9, v10}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v2, v8

    .line 151
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->exists()Z

    move-result v8

    if-nez v8, :cond_0

    .line 172
    :goto_0
    return-void

    .line 153
    :cond_0
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->isFile()Z

    move-result v8

    if-eqz v8, :cond_1

    .line 154
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->delete()Z

    move-result v8

    .line 155
    goto :goto_0

    .line 158
    :cond_1
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object v8

    move-object v3, v8

    .line 160
    move-object v8, v3

    if-eqz v8, :cond_2

    .line 161
    move-object v8, v3

    move-object v4, v8

    const/4 v8, 0x0

    move v5, v8

    .line 167
    :goto_1
    move v8, v5

    move-object v9, v4

    array-length v9, v9

    if-lt v8, v9, :cond_3

    .line 172
    :cond_2
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->delete()Z

    move-result v8

    goto :goto_0

    .line 161
    :cond_3
    move-object v8, v4

    move v9, v5

    aget-object v8, v8, v9

    move-object v6, v8

    .line 162
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->isDirectory()Z

    move-result v8

    if-eqz v8, :cond_4

    .line 163
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lpmm/by/p2077kng/hacker/FileUtil;->deleteFile(Ljava/lang/String;)V

    .line 166
    :cond_4
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->isFile()Z

    move-result v8

    if-eqz v8, :cond_5

    .line 167
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->delete()Z

    move-result v8

    :cond_5
    add-int/lit8 v5, v5, 0x1

    goto :goto_1
.end method

.method private static getDataColumn(Landroid/content/Context;Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;
    .locals 21

    .prologue
    .line 290
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    const/4 v14, 0x0

    check-cast v14, Landroid/database/Cursor;

    move-object v5, v14

    .line 292
    const-string v14, "_data"

    move-object v6, v14

    .line 293
    const/4 v14, 0x1

    new-array v14, v14, [Ljava/lang/String;

    move-object/from16 v20, v14

    move-object/from16 v14, v20

    move-object/from16 v15, v20

    const/16 v16, 0x0

    move-object/from16 v17, v6

    aput-object v17, v15, v16

    move-object v7, v14

    .line 298
    move-object v14, v0

    :try_start_0
    invoke-virtual {v14}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v14

    move-object v15, v1

    move-object/from16 v16, v7

    move-object/from16 v17, v2

    move-object/from16 v18, v3

    const/16 v19, 0x0

    check-cast v19, Ljava/lang/String;

    invoke-virtual/range {v14 .. v19}, Landroid/content/ContentResolver;->query(Landroid/net/Uri;[Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;)Landroid/database/Cursor;

    move-result-object v14

    move-object v5, v14

    .line 299
    move-object v14, v5

    if-eqz v14, :cond_1

    move-object v14, v5

    invoke-interface {v14}, Landroid/database/Cursor;->moveToFirst()Z

    move-result v14

    if-eqz v14, :cond_1

    .line 300
    move-object v14, v5

    move-object v15, v6

    invoke-interface {v14, v15}, Landroid/database/Cursor;->getColumnIndexOrThrow(Ljava/lang/String;)I

    move-result v14

    move v12, v14

    .line 301
    move-object v14, v5

    move v15, v12

    invoke-interface {v14, v15}, Landroid/database/Cursor;->getString(I)Ljava/lang/String;

    move-result-object v14

    move-object v10, v14

    .line 306
    move-object v14, v5

    if-eqz v14, :cond_0

    .line 307
    move-object v14, v5

    invoke-interface {v14}, Landroid/database/Cursor;->close()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    move-object v14, v10

    move-object v0, v14

    .line 310
    :goto_0
    return-object v0

    .line 306
    :cond_1
    :goto_1
    move-object v14, v5

    if-eqz v14, :cond_2

    .line 307
    move-object v14, v5

    invoke-interface {v14}, Landroid/database/Cursor;->close()V

    .line 310
    :cond_2
    const/4 v14, 0x0

    check-cast v14, Ljava/lang/String;

    move-object v0, v14

    goto :goto_0

    .line 307
    :catch_0
    move-exception v14

    move-object v12, v14

    goto :goto_1

    :catchall_0
    move-exception v14

    move-object v8, v14

    .line 306
    move-object v14, v5

    if-eqz v14, :cond_3

    .line 307
    move-object v14, v5

    invoke-interface {v14}, Landroid/database/Cursor;->close()V

    :cond_3
    move-object v14, v8

    throw v14
.end method

.method public static getExternalStorageDir()Ljava/lang/String;
    .locals 3

    .prologue
    .line 217
    invoke-static {}, Landroid/os/Environment;->getExternalStorageDirectory()Ljava/io/File;

    move-result-object v2

    invoke-virtual {v2}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v2

    move-object v0, v2

    return-object v0
.end method

.method public static getFileLength(Ljava/lang/String;)J
    .locals 8

    .prologue
    .line 212
    move-object v1, p0

    move-object v4, v1

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v4

    if-nez v4, :cond_0

    const/4 v4, 0x0

    int-to-long v4, v4

    move-wide v1, v4

    .line 213
    :goto_0
    return-wide v1

    :cond_0
    new-instance v4, Ljava/io/File;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v6, v1

    invoke-direct {v5, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v4}, Ljava/io/File;->length()J

    move-result-wide v4

    move-wide v1, v4

    goto :goto_0
.end method

.method public static getJpegRotate(Ljava/lang/String;)I
    .locals 10

    .prologue
    .line 572
    move-object v0, p0

    const/4 v6, 0x0

    move v2, v6

    .line 574
    :try_start_0
    new-instance v6, Landroid/media/ExifInterface;

    move-object v9, v6

    move-object v6, v9

    move-object v7, v9

    move-object v8, v0

    invoke-direct {v7, v8}, Landroid/media/ExifInterface;-><init>(Ljava/lang/String;)V

    move-object v3, v6

    .line 575
    move-object v6, v3

    const-string v7, "Orientation"

    const/4 v8, -0x1

    invoke-virtual {v6, v7, v8}, Landroid/media/ExifInterface;->getAttributeInt(Ljava/lang/String;I)I
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    move-result v6

    move v4, v6

    .line 577
    move v6, v4

    packed-switch v6, :pswitch_data_0

    .line 588
    :pswitch_0
    const/4 v6, 0x0

    move v2, v6

    .line 596
    :goto_0
    move v6, v2

    move v0, v6

    :goto_1
    return v0

    .line 579
    :pswitch_1
    const/16 v6, 0x5a

    move v2, v6

    .line 580
    goto :goto_0

    .line 582
    :pswitch_2
    const/16 v6, 0xb4

    move v2, v6

    .line 583
    goto :goto_0

    .line 585
    :pswitch_3
    const/16 v6, 0x10e

    move v2, v6

    .line 586
    goto :goto_0

    .line 589
    :catch_0
    move-exception v6

    move-object v3, v6

    .line 593
    const/4 v6, 0x0

    move v0, v6

    goto :goto_1

    .line 577
    :pswitch_data_0
    .packed-switch 0x3
        :pswitch_2
        :pswitch_0
        :pswitch_0
        :pswitch_1
        :pswitch_0
        :pswitch_3
    .end packed-switch
.end method

.method public static getPackageDataDir(Landroid/content/Context;)Ljava/lang/String;
    .locals 5

    .prologue
    .line 221
    move-object v0, p0

    move-object v3, v0

    const/4 v4, 0x0

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v3, v4}, Landroid/content/Context;->getExternalFilesDir(Ljava/lang/String;)Ljava/io/File;

    move-result-object v3

    invoke-virtual {v3}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method public static getPublicDir(Ljava/lang/String;)Ljava/lang/String;
    .locals 4

    .prologue
    .line 225
    move-object v0, p0

    move-object v3, v0

    invoke-static {v3}, Landroid/os/Environment;->getExternalStoragePublicDirectory(Ljava/lang/String;)Ljava/io/File;

    move-result-object v3

    invoke-virtual {v3}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method public static getScaledBitmap(Ljava/lang/String;I)Landroid/graphics/Bitmap;
    .locals 12

    .prologue
    .line 346
    move-object v0, p0

    move v1, p1

    move-object v8, v0

    invoke-static {v8}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v8

    move-object v3, v8

    .line 348
    move-object v8, v3

    invoke-virtual {v8}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v8

    move v4, v8

    .line 349
    move-object v8, v3

    invoke-virtual {v8}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v8

    move v5, v8

    .line 350
    const/4 v8, 0x0

    move v6, v8

    .line 352
    move v8, v4

    move v9, v5

    if-le v8, v9, :cond_0

    .line 353
    move v8, v1

    int-to-float v8, v8

    move v9, v4

    int-to-float v9, v9

    div-float/2addr v8, v9

    move v6, v8

    .line 354
    move v8, v5

    int-to-float v8, v8

    move v9, v6

    mul-float/2addr v8, v9

    float-to-int v8, v8

    move v5, v8

    .line 355
    move v8, v1

    move v4, v8

    .line 362
    :goto_0
    move-object v8, v3

    move v9, v4

    move v10, v5

    const/4 v11, 0x1

    invoke-static {v8, v9, v10, v11}, Landroid/graphics/Bitmap;->createScaledBitmap(Landroid/graphics/Bitmap;IIZ)Landroid/graphics/Bitmap;

    move-result-object v8

    move-object v0, v8

    return-object v0

    .line 357
    :cond_0
    move v8, v1

    int-to-float v8, v8

    move v9, v5

    int-to-float v9, v9

    div-float/2addr v8, v9

    move v6, v8

    .line 358
    move v8, v4

    int-to-float v8, v8

    move v9, v6

    mul-float/2addr v8, v9

    float-to-int v8, v8

    move v4, v8

    .line 359
    move v8, v1

    move v5, v8

    goto :goto_0
.end method

.method public static isDirectory(Ljava/lang/String;)Z
    .locals 7

    .prologue
    .line 202
    move-object v0, p0

    move-object v3, v0

    invoke-static {v3}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_0

    const/4 v3, 0x0

    move v0, v3

    .line 203
    :goto_0
    return v0

    :cond_0
    new-instance v3, Ljava/io/File;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v5, v0

    invoke-direct {v4, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v3}, Ljava/io/File;->isDirectory()Z

    move-result v3

    move v0, v3

    goto :goto_0
.end method

.method private static isDownloadsDocument(Landroid/net/Uri;)Z
    .locals 5

    .prologue
    .line 319
    move-object v0, p0

    const-string v3, "com.android.providers.downloads.documents"

    move-object v4, v0

    invoke-virtual {v4}, Landroid/net/Uri;->getAuthority()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    move v0, v3

    return v0
.end method

.method public static isExistFile(Ljava/lang/String;)Z
    .locals 8

    .prologue
    .line 176
    move-object v0, p0

    new-instance v4, Ljava/io/File;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v6, v0

    invoke-direct {v5, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v2, v4

    .line 177
    move-object v4, v2

    invoke-virtual {v4}, Ljava/io/File;->exists()Z

    move-result v4

    move v0, v4

    return v0
.end method

.method private static isExternalStorageDocument(Landroid/net/Uri;)Z
    .locals 5

    .prologue
    .line 315
    move-object v0, p0

    const-string v3, "com.android.externalstorage.documents"

    move-object v4, v0

    invoke-virtual {v4}, Landroid/net/Uri;->getAuthority()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    move v0, v3

    return v0
.end method

.method public static isFile(Ljava/lang/String;)Z
    .locals 7

    .prologue
    .line 207
    move-object v0, p0

    move-object v3, v0

    invoke-static {v3}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_0

    const/4 v3, 0x0

    move v0, v3

    .line 208
    :goto_0
    return v0

    :cond_0
    new-instance v3, Ljava/io/File;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v5, v0

    invoke-direct {v4, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v3}, Ljava/io/File;->isFile()Z

    move-result v3

    move v0, v3

    goto :goto_0
.end method

.method private static isMediaDocument(Landroid/net/Uri;)Z
    .locals 5

    .prologue
    .line 323
    move-object v0, p0

    const-string v3, "com.android.providers.media.documents"

    move-object v4, v0

    invoke-virtual {v4}, Landroid/net/Uri;->getAuthority()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    move v0, v3

    return v0
.end method

.method public static listDir(Ljava/lang/String;Ljava/util/ArrayList;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/ArrayList",
            "<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .prologue
    .line 188
    move-object v0, p0

    move-object v1, p1

    new-instance v9, Ljava/io/File;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    move-object v11, v0

    invoke-direct {v10, v11}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v3, v9

    .line 189
    move-object v9, v3

    invoke-virtual {v9}, Ljava/io/File;->exists()Z

    move-result v9

    if-eqz v9, :cond_0

    move-object v9, v3

    invoke-virtual {v9}, Ljava/io/File;->isFile()Z

    move-result v9

    if-eqz v9, :cond_1

    .line 197
    :cond_0
    :goto_0
    return-void

    .line 191
    :cond_1
    move-object v9, v3

    invoke-virtual {v9}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object v9

    move-object v4, v9

    .line 192
    move-object v9, v4

    if-eqz v9, :cond_2

    move-object v9, v4

    array-length v9, v9

    const/4 v10, 0x0

    if-gt v9, v10, :cond_3

    :cond_2
    goto :goto_0

    .line 194
    :cond_3
    move-object v9, v1

    if-nez v9, :cond_4

    goto :goto_0

    .line 195
    :cond_4
    move-object v9, v1

    invoke-virtual {v9}, Ljava/util/ArrayList;->clear()V

    .line 196
    move-object v9, v4

    move-object v5, v9

    const/4 v9, 0x0

    move v6, v9

    .line 197
    :goto_1
    move v9, v6

    move-object v10, v5

    array-length v10, v10

    if-lt v9, v10, :cond_5

    goto :goto_0

    .line 196
    :cond_5
    move-object v9, v5

    move v10, v6

    aget-object v9, v9, v10

    move-object v7, v9

    .line 197
    move-object v9, v1

    move-object v10, v7

    invoke-virtual {v10}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    move-result v9

    add-int/lit8 v6, v6, 0x1

    goto :goto_1
.end method

.method public static makeDir(Ljava/lang/String;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 181
    move-object v0, p0

    move-object v4, v0

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v4

    if-nez v4, :cond_0

    .line 182
    new-instance v4, Ljava/io/File;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v6, v0

    invoke-direct {v5, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v2, v4

    .line 183
    move-object v4, v2

    invoke-virtual {v4}, Ljava/io/File;->mkdirs()Z

    move-result v4

    :cond_0
    return-void
.end method

.method public static moveFile(Ljava/lang/String;Ljava/lang/String;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 144
    move-object v0, p0

    move-object v1, p1

    move-object v4, v0

    move-object v5, v1

    invoke-static {v4, v5}, Lpmm/by/p2077kng/hacker/FileUtil;->copyFile(Ljava/lang/String;Ljava/lang/String;)V

    .line 145
    move-object v4, v0

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/FileUtil;->deleteFile(Ljava/lang/String;)V

    return-void
.end method

.method public static readFile(Ljava/lang/String;)Ljava/lang/String;
    .locals 19

    .prologue
    .line 58
    move-object/from16 v0, p0

    move-object v12, v0

    invoke-static {v12}, Lpmm/by/p2077kng/hacker/FileUtil;->createNewFile(Ljava/lang/String;)V

    .line 60
    new-instance v12, Ljava/lang/StringBuilder;

    move-object/from16 v18, v12

    move-object/from16 v12, v18

    move-object/from16 v13, v18

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    move-object v2, v12

    .line 61
    const/4 v12, 0x0

    check-cast v12, Ljava/io/FileReader;

    move-object v3, v12

    .line 63
    :try_start_0
    new-instance v12, Ljava/io/FileReader;

    move-object/from16 v18, v12

    move-object/from16 v12, v18

    move-object/from16 v13, v18

    new-instance v14, Ljava/io/File;

    move-object/from16 v18, v14

    move-object/from16 v14, v18

    move-object/from16 v15, v18

    move-object/from16 v16, v0

    invoke-direct/range {v15 .. v16}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-direct {v13, v14}, Ljava/io/FileReader;-><init>(Ljava/io/File;)V

    move-object v3, v12

    .line 65
    const/16 v12, 0x400

    new-array v12, v12, [C

    move-object v8, v12

    .line 66
    const/4 v12, 0x0

    move v9, v12

    .line 68
    :goto_0
    move-object v12, v3

    move-object v13, v8

    invoke-virtual {v12, v13}, Ljava/io/FileReader;->read([C)I
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v12

    move/from16 v18, v12

    move/from16 v12, v18

    move/from16 v13, v18

    move v9, v13

    const/4 v13, 0x0

    if-gt v12, v13, :cond_1

    .line 74
    :goto_1
    move-object v12, v3

    if-eqz v12, :cond_0

    .line 76
    move-object v12, v3

    :try_start_1
    invoke-virtual {v12}, Ljava/io/FileReader;->close()V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_2

    .line 83
    :cond_0
    :goto_2
    move-object v12, v2

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    move-object v0, v12

    return-object v0

    .line 69
    :cond_1
    move-object v12, v2

    :try_start_2
    new-instance v13, Ljava/lang/String;

    move-object/from16 v18, v13

    move-object/from16 v13, v18

    move-object/from16 v14, v18

    move-object v15, v8

    const/16 v16, 0x0

    move/from16 v17, v9

    invoke-direct/range {v14 .. v17}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-result-object v12

    goto :goto_0

    .line 68
    :catch_0
    move-exception v12

    move-object v8, v12

    .line 72
    move-object v12, v8

    :try_start_3
    invoke-virtual {v12}, Ljava/io/IOException;->printStackTrace()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v12

    move-object v4, v12

    .line 74
    move-object v12, v3

    if-eqz v12, :cond_2

    .line 76
    move-object v12, v3

    :try_start_4
    invoke-virtual {v12}, Ljava/io/FileReader;->close()V
    :try_end_4
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_4} :catch_1

    .line 78
    :cond_2
    :goto_3
    move-object v12, v4

    throw v12

    .line 76
    :catch_1
    move-exception v12

    move-object v10, v12

    .line 78
    move-object v12, v10

    invoke-virtual {v12}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_3

    .line 76
    :catch_2
    move-exception v12

    move-object v10, v12

    .line 78
    move-object v12, v10

    invoke-virtual {v12}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_2
.end method

.method public static resizeBitmapFileRetainRatio(Ljava/lang/String;Ljava/lang/String;I)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 394
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v6

    if-nez v6, :cond_0

    .line 396
    :goto_0
    return-void

    .line 395
    :cond_0
    move-object v6, v0

    move v7, v2

    invoke-static {v6, v7}, Lpmm/by/p2077kng/hacker/FileUtil;->getScaledBitmap(Ljava/lang/String;I)Landroid/graphics/Bitmap;

    move-result-object v6

    move-object v4, v6

    .line 396
    move-object v6, v4

    move-object v7, v1

    invoke-static {v6, v7}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static resizeBitmapFileToCircle(Ljava/lang/String;Ljava/lang/String;)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 407
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object v10, v0

    invoke-static {v10}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v10

    if-nez v10, :cond_0

    .line 425
    :goto_0
    return-void

    .line 408
    :cond_0
    move-object v10, v0

    invoke-static {v10}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v3, v10

    .line 409
    move-object v10, v3

    invoke-virtual {v10}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v10

    move-object v11, v3

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v11

    sget-object v12, Landroid/graphics/Bitmap$Config;->ARGB_8888:Landroid/graphics/Bitmap$Config;

    invoke-static {v10, v11, v12}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v4, v10

    .line 411
    new-instance v10, Landroid/graphics/Canvas;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v4

    invoke-direct {v11, v12}, Landroid/graphics/Canvas;-><init>(Landroid/graphics/Bitmap;)V

    move-object v5, v10

    .line 413
    const v10, -0xbdbdbe

    move v6, v10

    .line 414
    new-instance v10, Landroid/graphics/Paint;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    invoke-direct {v11}, Landroid/graphics/Paint;-><init>()V

    move-object v7, v10

    .line 415
    new-instance v10, Landroid/graphics/Rect;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    const/4 v12, 0x0

    const/4 v13, 0x0

    move-object v14, v3

    invoke-virtual {v14}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v14

    move-object v15, v3

    invoke-virtual {v15}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v15

    invoke-direct {v11, v12, v13, v14, v15}, Landroid/graphics/Rect;-><init>(IIII)V

    move-object v8, v10

    .line 417
    move-object v10, v7

    const/4 v11, 0x1

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 418
    move-object v10, v5

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawARGB(IIII)V

    .line 419
    move-object v10, v7

    const v11, -0xbdbdbe

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setColor(I)V

    .line 420
    move-object v10, v5

    move-object v11, v3

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v11

    const/4 v12, 0x2

    div-int/lit8 v11, v11, 0x2

    int-to-float v11, v11

    move-object v12, v3

    invoke-virtual {v12}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v12

    const/4 v13, 0x2

    div-int/lit8 v12, v12, 0x2

    int-to-float v12, v12

    move-object v13, v3

    invoke-virtual {v13}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v13

    const/4 v14, 0x2

    div-int/lit8 v13, v13, 0x2

    int-to-float v13, v13

    move-object v14, v7

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 422
    move-object v10, v7

    new-instance v11, Landroid/graphics/PorterDuffXfermode;

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    sget-object v13, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-direct {v12, v13}, Landroid/graphics/PorterDuffXfermode;-><init>(Landroid/graphics/PorterDuff$Mode;)V

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setXfermode(Landroid/graphics/Xfermode;)Landroid/graphics/Xfermode;

    move-result-object v10

    .line 423
    move-object v10, v5

    move-object v11, v3

    move-object v12, v8

    move-object v13, v8

    move-object v14, v7

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawBitmap(Landroid/graphics/Bitmap;Landroid/graphics/Rect;Landroid/graphics/Rect;Landroid/graphics/Paint;)V

    .line 425
    move-object v10, v4

    move-object v11, v1

    invoke-static {v10, v11}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto/16 :goto_0
.end method

.method public static resizeBitmapFileToSquare(Ljava/lang/String;Ljava/lang/String;I)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 400
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v7, v0

    invoke-static {v7}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v7

    if-nez v7, :cond_0

    .line 403
    :goto_0
    return-void

    .line 401
    :cond_0
    move-object v7, v0

    invoke-static {v7}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v7

    move-object v4, v7

    .line 402
    move-object v7, v4

    move v8, v2

    move v9, v2

    const/4 v10, 0x1

    invoke-static {v7, v8, v9, v10}, Landroid/graphics/Bitmap;->createScaledBitmap(Landroid/graphics/Bitmap;IIZ)Landroid/graphics/Bitmap;

    move-result-object v7

    move-object v5, v7

    .line 403
    move-object v7, v5

    move-object v8, v1

    invoke-static {v7, v8}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static resizeBitmapFileWithRoundedBorder(Ljava/lang/String;Ljava/lang/String;I)V
    .locals 20
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 429
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object v13, v0

    invoke-static {v13}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v13

    if-nez v13, :cond_0

    .line 449
    :goto_0
    return-void

    .line 430
    :cond_0
    move-object v13, v0

    invoke-static {v13}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v13

    move-object v4, v13

    .line 431
    move-object v13, v4

    invoke-virtual {v13}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v13

    move-object v14, v4

    invoke-virtual {v14}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v14

    sget-object v15, Landroid/graphics/Bitmap$Config;->ARGB_8888:Landroid/graphics/Bitmap$Config;

    invoke-static {v13, v14, v15}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object v13

    move-object v5, v13

    .line 433
    new-instance v13, Landroid/graphics/Canvas;

    move-object/from16 v19, v13

    move-object/from16 v13, v19

    move-object/from16 v14, v19

    move-object v15, v5

    invoke-direct {v14, v15}, Landroid/graphics/Canvas;-><init>(Landroid/graphics/Bitmap;)V

    move-object v6, v13

    .line 435
    const v13, -0xbdbdbe

    move v7, v13

    .line 436
    new-instance v13, Landroid/graphics/Paint;

    move-object/from16 v19, v13

    move-object/from16 v13, v19

    move-object/from16 v14, v19

    invoke-direct {v14}, Landroid/graphics/Paint;-><init>()V

    move-object v8, v13

    .line 437
    new-instance v13, Landroid/graphics/Rect;

    move-object/from16 v19, v13

    move-object/from16 v13, v19

    move-object/from16 v14, v19

    const/4 v15, 0x0

    const/16 v16, 0x0

    move-object/from16 v17, v4

    invoke-virtual/range {v17 .. v17}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v17

    move-object/from16 v18, v4

    invoke-virtual/range {v18 .. v18}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v18

    invoke-direct/range {v14 .. v18}, Landroid/graphics/Rect;-><init>(IIII)V

    move-object v9, v13

    .line 438
    new-instance v13, Landroid/graphics/RectF;

    move-object/from16 v19, v13

    move-object/from16 v13, v19

    move-object/from16 v14, v19

    move-object v15, v9

    invoke-direct {v14, v15}, Landroid/graphics/RectF;-><init>(Landroid/graphics/Rect;)V

    move-object v10, v13

    .line 439
    move v13, v2

    int-to-float v13, v13

    move v11, v13

    .line 441
    move-object v13, v8

    const/4 v14, 0x1

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 442
    move-object v13, v6

    const/4 v14, 0x0

    const/4 v15, 0x0

    const/16 v16, 0x0

    const/16 v17, 0x0

    invoke-virtual/range {v13 .. v17}, Landroid/graphics/Canvas;->drawARGB(IIII)V

    .line 443
    move-object v13, v8

    const v14, -0xbdbdbe

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setColor(I)V

    .line 444
    move-object v13, v6

    move-object v14, v10

    move v15, v11

    move/from16 v16, v11

    move-object/from16 v17, v8

    invoke-virtual/range {v13 .. v17}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 446
    move-object v13, v8

    new-instance v14, Landroid/graphics/PorterDuffXfermode;

    move-object/from16 v19, v14

    move-object/from16 v14, v19

    move-object/from16 v15, v19

    sget-object v16, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-direct/range {v15 .. v16}, Landroid/graphics/PorterDuffXfermode;-><init>(Landroid/graphics/PorterDuff$Mode;)V

    invoke-virtual {v13, v14}, Landroid/graphics/Paint;->setXfermode(Landroid/graphics/Xfermode;)Landroid/graphics/Xfermode;

    move-result-object v13

    .line 447
    move-object v13, v6

    move-object v14, v4

    move-object v15, v9

    move-object/from16 v16, v9

    move-object/from16 v17, v8

    invoke-virtual/range {v13 .. v17}, Landroid/graphics/Canvas;->drawBitmap(Landroid/graphics/Bitmap;Landroid/graphics/Rect;Landroid/graphics/Rect;Landroid/graphics/Paint;)V

    .line 449
    move-object v13, v5

    move-object v14, v1

    invoke-static {v13, v14}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto/16 :goto_0
.end method

.method public static rotateBitmapFile(Ljava/lang/String;Ljava/lang/String;F)V
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "F)V"
        }
    .end annotation

    .prologue
    .line 485
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object v8, v0

    invoke-static {v8}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v8

    if-nez v8, :cond_0

    .line 490
    :goto_0
    return-void

    .line 486
    :cond_0
    move-object v8, v0

    invoke-static {v8}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v8

    move-object v4, v8

    .line 487
    new-instance v8, Landroid/graphics/Matrix;

    move-object v15, v8

    move-object v8, v15

    move-object v9, v15

    invoke-direct {v9}, Landroid/graphics/Matrix;-><init>()V

    move-object v5, v8

    .line 488
    move-object v8, v5

    move v9, v2

    invoke-virtual {v8, v9}, Landroid/graphics/Matrix;->postRotate(F)Z

    move-result v8

    .line 489
    move-object v8, v4

    const/4 v9, 0x0

    const/4 v10, 0x0

    move-object v11, v4

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v11

    move-object v12, v4

    invoke-virtual {v12}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v12

    move-object v13, v5

    const/4 v14, 0x1

    invoke-static/range {v8 .. v14}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIIILandroid/graphics/Matrix;Z)Landroid/graphics/Bitmap;

    move-result-object v8

    move-object v6, v8

    .line 490
    move-object v8, v6

    move-object v9, v1

    invoke-static {v8, v9}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method private static saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V
    .locals 21
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/Bitmap;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 327
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    const/4 v15, 0x0

    check-cast v15, Ljava/io/FileOutputStream;

    move-object v3, v15

    .line 328
    move-object v15, v1

    invoke-static {v15}, Lpmm/by/p2077kng/hacker/FileUtil;->createNewFile(Ljava/lang/String;)V

    .line 330
    :try_start_0
    new-instance v15, Ljava/io/FileOutputStream;

    move-object/from16 v20, v15

    move-object/from16 v15, v20

    move-object/from16 v16, v20

    new-instance v17, Ljava/io/File;

    move-object/from16 v20, v17

    move-object/from16 v17, v20

    move-object/from16 v18, v20

    move-object/from16 v19, v1

    invoke-direct/range {v18 .. v19}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-direct/range {v16 .. v17}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V

    move-object v3, v15

    .line 331
    move-object v15, v0

    sget-object v16, Landroid/graphics/Bitmap$CompressFormat;->PNG:Landroid/graphics/Bitmap$CompressFormat;

    const/16 v17, 0x64

    move-object/from16 v18, v3

    invoke-virtual/range {v15 .. v18}, Landroid/graphics/Bitmap;->compress(Landroid/graphics/Bitmap$CompressFormat;ILjava/io/OutputStream;)Z
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v15

    .line 336
    :goto_0
    move-object v15, v3

    if-eqz v15, :cond_0

    .line 337
    move-object v15, v3

    :try_start_1
    invoke-virtual {v15}, Ljava/io/FileOutputStream;->close()V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_2

    .line 340
    :cond_0
    :goto_1
    return-void

    .line 331
    :catch_0
    move-exception v15

    move-object v8, v15

    .line 333
    move-object v15, v8

    :try_start_2
    invoke-virtual {v15}, Ljava/lang/Exception;->printStackTrace()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v15

    move-object v4, v15

    .line 336
    move-object v15, v3

    if-eqz v15, :cond_1

    .line 337
    move-object v15, v3

    :try_start_3
    invoke-virtual {v15}, Ljava/io/FileOutputStream;->close()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_1

    .line 340
    :cond_1
    :goto_2
    move-object v15, v4

    throw v15

    .line 337
    :catch_1
    move-exception v15

    move-object v13, v15

    .line 340
    move-object v15, v13

    invoke-virtual {v15}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_2

    .line 337
    :catch_2
    move-exception v15

    move-object v13, v15

    .line 340
    move-object v15, v13

    invoke-virtual {v15}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_1
.end method

.method public static scaleBitmapFile(Ljava/lang/String;Ljava/lang/String;FF)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "FF)V"
        }
    .end annotation

    .prologue
    .line 494
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object v11, v0

    invoke-static {v11}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v11

    if-nez v11, :cond_0

    .line 503
    :goto_0
    return-void

    .line 495
    :cond_0
    move-object v11, v0

    invoke-static {v11}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v11

    move-object v5, v11

    .line 496
    new-instance v11, Landroid/graphics/Matrix;

    move-object/from16 v18, v11

    move-object/from16 v11, v18

    move-object/from16 v12, v18

    invoke-direct {v12}, Landroid/graphics/Matrix;-><init>()V

    move-object v6, v11

    .line 497
    move-object v11, v6

    move v12, v2

    move v13, v3

    invoke-virtual {v11, v12, v13}, Landroid/graphics/Matrix;->postScale(FF)Z

    move-result v11

    .line 499
    move-object v11, v5

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v11

    move v7, v11

    .line 500
    move-object v11, v5

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v11

    move v8, v11

    .line 502
    move-object v11, v5

    const/4 v12, 0x0

    const/4 v13, 0x0

    move v14, v7

    move v15, v8

    move-object/from16 v16, v6

    const/16 v17, 0x1

    invoke-static/range {v11 .. v17}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIIILandroid/graphics/Matrix;Z)Landroid/graphics/Bitmap;

    move-result-object v11

    move-object v9, v11

    .line 503
    move-object v11, v9

    move-object v12, v1

    invoke-static {v11, v12}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static setBitmapFileBrightness(Ljava/lang/String;Ljava/lang/String;F)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "F)V"
        }
    .end annotation

    .prologue
    .line 533
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object v10, v0

    invoke-static {v10}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v10

    if-nez v10, :cond_0

    .line 548
    :goto_0
    return-void

    .line 534
    :cond_0
    move-object v10, v0

    invoke-static {v10}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v4, v10

    .line 535
    new-instance v10, Landroid/graphics/ColorMatrix;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    const/16 v12, 0x14

    new-array v12, v12, [F

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x0

    const/4 v15, 0x1

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x1

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x2

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x3

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x4

    move v15, v2

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x5

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x6

    const/4 v15, 0x1

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x7

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x8

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x9

    move v15, v2

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xa

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xb

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xc

    const/4 v15, 0x1

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xd

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xe

    move v15, v2

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xf

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x10

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x11

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x12

    const/4 v15, 0x1

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x13

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    invoke-direct {v11, v12}, Landroid/graphics/ColorMatrix;-><init>([F)V

    move-object v5, v10

    .line 543
    move-object v10, v4

    invoke-virtual {v10}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v10

    move-object v11, v4

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v11

    move-object v12, v4

    invoke-virtual {v12}, Landroid/graphics/Bitmap;->getConfig()Landroid/graphics/Bitmap$Config;

    move-result-object v12

    invoke-static {v10, v11, v12}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v6, v10

    .line 544
    new-instance v10, Landroid/graphics/Canvas;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v6

    invoke-direct {v11, v12}, Landroid/graphics/Canvas;-><init>(Landroid/graphics/Bitmap;)V

    move-object v7, v10

    .line 545
    new-instance v10, Landroid/graphics/Paint;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    invoke-direct {v11}, Landroid/graphics/Paint;-><init>()V

    move-object v8, v10

    .line 546
    move-object v10, v8

    new-instance v11, Landroid/graphics/ColorMatrixColorFilter;

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    move-object v13, v5

    invoke-direct {v12, v13}, Landroid/graphics/ColorMatrixColorFilter;-><init>(Landroid/graphics/ColorMatrix;)V

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    move-result-object v10

    .line 547
    move-object v10, v7

    move-object v11, v4

    const/4 v12, 0x0

    int-to-float v12, v12

    const/4 v13, 0x0

    int-to-float v13, v13

    move-object v14, v8

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawBitmap(Landroid/graphics/Bitmap;FFLandroid/graphics/Paint;)V

    .line 548
    move-object v10, v6

    move-object v11, v1

    invoke-static {v10, v11}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto/16 :goto_0
.end method

.method public static setBitmapFileColorFilter(Ljava/lang/String;Ljava/lang/String;I)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 520
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object v10, v0

    invoke-static {v10}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v10

    if-nez v10, :cond_0

    .line 529
    :goto_0
    return-void

    .line 521
    :cond_0
    move-object v10, v0

    invoke-static {v10}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v4, v10

    .line 522
    move-object v10, v4

    const/4 v11, 0x0

    const/4 v12, 0x0

    move-object v13, v4

    invoke-virtual {v13}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v13

    const/4 v14, 0x1

    add-int/lit8 v13, v13, -0x1

    move-object v14, v4

    invoke-virtual {v14}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v14

    const/4 v15, 0x1

    add-int/lit8 v14, v14, -0x1

    invoke-static {v10, v11, v12, v13, v14}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIII)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v5, v10

    .line 524
    new-instance v10, Landroid/graphics/Paint;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    invoke-direct {v11}, Landroid/graphics/Paint;-><init>()V

    move-object v6, v10

    .line 525
    new-instance v10, Landroid/graphics/LightingColorFilter;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move v12, v2

    const/4 v13, 0x1

    invoke-direct {v11, v12, v13}, Landroid/graphics/LightingColorFilter;-><init>(II)V

    move-object v7, v10

    .line 526
    move-object v10, v6

    move-object v11, v7

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    move-result-object v10

    .line 527
    new-instance v10, Landroid/graphics/Canvas;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v5

    invoke-direct {v11, v12}, Landroid/graphics/Canvas;-><init>(Landroid/graphics/Bitmap;)V

    move-object v8, v10

    .line 528
    move-object v10, v8

    move-object v11, v5

    const/4 v12, 0x0

    int-to-float v12, v12

    const/4 v13, 0x0

    int-to-float v13, v13

    move-object v14, v6

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawBitmap(Landroid/graphics/Bitmap;FFLandroid/graphics/Paint;)V

    .line 529
    move-object v10, v5

    move-object v11, v1

    invoke-static {v10, v11}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static setBitmapFileContrast(Ljava/lang/String;Ljava/lang/String;F)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "F)V"
        }
    .end annotation

    .prologue
    .line 552
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object v10, v0

    invoke-static {v10}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v10

    if-nez v10, :cond_0

    .line 568
    :goto_0
    return-void

    .line 553
    :cond_0
    move-object v10, v0

    invoke-static {v10}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v4, v10

    .line 554
    new-instance v10, Landroid/graphics/ColorMatrix;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    const/16 v12, 0x14

    new-array v12, v12, [F

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x0

    move v15, v2

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x1

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x2

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x3

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x4

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x5

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x6

    move v15, v2

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x7

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x8

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x9

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xa

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xb

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xc

    move v15, v2

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xd

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xe

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0xf

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x10

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x11

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x12

    const/4 v15, 0x1

    int-to-float v15, v15

    aput v15, v13, v14

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/16 v14, 0x13

    const/4 v15, 0x0

    int-to-float v15, v15

    aput v15, v13, v14

    invoke-direct {v11, v12}, Landroid/graphics/ColorMatrix;-><init>([F)V

    move-object v5, v10

    .line 562
    move-object v10, v4

    invoke-virtual {v10}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v10

    move-object v11, v4

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v11

    move-object v12, v4

    invoke-virtual {v12}, Landroid/graphics/Bitmap;->getConfig()Landroid/graphics/Bitmap$Config;

    move-result-object v12

    invoke-static {v10, v11, v12}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object v10

    move-object v6, v10

    .line 563
    new-instance v10, Landroid/graphics/Canvas;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v6

    invoke-direct {v11, v12}, Landroid/graphics/Canvas;-><init>(Landroid/graphics/Bitmap;)V

    move-object v7, v10

    .line 564
    new-instance v10, Landroid/graphics/Paint;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    invoke-direct {v11}, Landroid/graphics/Paint;-><init>()V

    move-object v8, v10

    .line 565
    move-object v10, v8

    new-instance v11, Landroid/graphics/ColorMatrixColorFilter;

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    move-object v13, v5

    invoke-direct {v12, v13}, Landroid/graphics/ColorMatrixColorFilter;-><init>(Landroid/graphics/ColorMatrix;)V

    invoke-virtual {v10, v11}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    move-result-object v10

    .line 566
    move-object v10, v7

    move-object v11, v4

    const/4 v12, 0x0

    int-to-float v12, v12

    const/4 v13, 0x0

    int-to-float v13, v13

    move-object v14, v8

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/graphics/Canvas;->drawBitmap(Landroid/graphics/Bitmap;FFLandroid/graphics/Paint;)V

    .line 568
    move-object v10, v6

    move-object v11, v1

    invoke-static {v10, v11}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto/16 :goto_0
.end method

.method public static skewBitmapFile(Ljava/lang/String;Ljava/lang/String;FF)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "FF)V"
        }
    .end annotation

    .prologue
    .line 507
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object v11, v0

    invoke-static {v11}, Lpmm/by/p2077kng/hacker/FileUtil;->isExistFile(Ljava/lang/String;)Z

    move-result v11

    if-nez v11, :cond_0

    .line 516
    :goto_0
    return-void

    .line 508
    :cond_0
    move-object v11, v0

    invoke-static {v11}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v11

    move-object v5, v11

    .line 509
    new-instance v11, Landroid/graphics/Matrix;

    move-object/from16 v18, v11

    move-object/from16 v11, v18

    move-object/from16 v12, v18

    invoke-direct {v12}, Landroid/graphics/Matrix;-><init>()V

    move-object v6, v11

    .line 510
    move-object v11, v6

    move v12, v2

    move v13, v3

    invoke-virtual {v11, v12, v13}, Landroid/graphics/Matrix;->postSkew(FF)Z

    move-result v11

    .line 512
    move-object v11, v5

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v11

    move v7, v11

    .line 513
    move-object v11, v5

    invoke-virtual {v11}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v11

    move v8, v11

    .line 515
    move-object v11, v5

    const/4 v12, 0x0

    const/4 v13, 0x0

    move v14, v7

    move v15, v8

    move-object/from16 v16, v6

    const/16 v17, 0x1

    invoke-static/range {v11 .. v17}, Landroid/graphics/Bitmap;->createBitmap(Landroid/graphics/Bitmap;IIIILandroid/graphics/Matrix;Z)Landroid/graphics/Bitmap;

    move-result-object v11

    move-object v9, v11

    .line 516
    move-object v11, v9

    move-object v12, v1

    invoke-static {v11, v12}, Lpmm/by/p2077kng/hacker/FileUtil;->saveBitmap(Landroid/graphics/Bitmap;Ljava/lang/String;)V

    goto :goto_0
.end method

.method public static writeFile(Ljava/lang/String;Ljava/lang/String;)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 87
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object v13, v0

    invoke-static {v13}, Lpmm/by/p2077kng/hacker/FileUtil;->createNewFile(Ljava/lang/String;)V

    .line 88
    const/4 v13, 0x0

    check-cast v13, Ljava/io/FileWriter;

    move-object v3, v13

    .line 91
    :try_start_0
    new-instance v13, Ljava/io/FileWriter;

    move-object/from16 v18, v13

    move-object/from16 v13, v18

    move-object/from16 v14, v18

    new-instance v15, Ljava/io/File;

    move-object/from16 v18, v15

    move-object/from16 v15, v18

    move-object/from16 v16, v18

    move-object/from16 v17, v0

    invoke-direct/range {v16 .. v17}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    const/16 v16, 0x0

    invoke-direct/range {v14 .. v16}, Ljava/io/FileWriter;-><init>(Ljava/io/File;Z)V

    move-object v3, v13

    .line 92
    move-object v13, v3

    move-object v14, v1

    invoke-virtual {v13, v14}, Ljava/io/FileWriter;->write(Ljava/lang/String;)V

    .line 93
    move-object v13, v3

    invoke-virtual {v13}, Ljava/io/FileWriter;->flush()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 98
    :goto_0
    move-object v13, v3

    if-eqz v13, :cond_0

    .line 99
    move-object v13, v3

    :try_start_1
    invoke-virtual {v13}, Ljava/io/FileWriter;->close()V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_2

    .line 101
    :cond_0
    :goto_1
    return-void

    .line 93
    :catch_0
    move-exception v13

    move-object v8, v13

    .line 95
    move-object v13, v8

    :try_start_2
    invoke-virtual {v13}, Ljava/io/IOException;->printStackTrace()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v13

    move-object v4, v13

    .line 98
    move-object v13, v3

    if-eqz v13, :cond_1

    .line 99
    move-object v13, v3

    :try_start_3
    invoke-virtual {v13}, Ljava/io/FileWriter;->close()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_1

    .line 101
    :cond_1
    :goto_2
    move-object v13, v4

    throw v13

    .line 99
    :catch_1
    move-exception v13

    move-object v11, v13

    .line 101
    move-object v13, v11

    invoke-virtual {v13}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_2

    .line 99
    :catch_2
    move-exception v13

    move-object v11, v13

    .line 101
    move-object v13, v11

    invoke-virtual {v13}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_1
.end method
