.class public Lpmm/by/p2077kng/hacker/P2077KNG;
.super Ljava/lang/Object;
.source "P2077KNG.java"


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 114
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static native AAAAAAAAA()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native BBBBBBBB()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native CCCCCCCC()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native DDDDDDDD()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native EEEEEEEE()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native FFFFFFFF()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static Fuckass(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;
    .locals 10

    .prologue
    .line 101
    move-object v0, p0

    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    move-object v9, v4

    move-object v4, v9

    move-object v5, v9

    invoke-direct {v5}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v2, v4

    .line 102
    move-object v4, v2

    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setShape(I)V

    .line 103
    move-object v4, v2

    const/4 v5, 0x3

    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->colourP()Ljava/lang/String;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    invoke-static {v6}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 104
    move-object v4, v2

    const/4 v5, 0x1

    const/high16 v6, 0x40a00000    # 5.0f

    move-object v7, v0

    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v7

    invoke-virtual {v7}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v7

    check-cast v7, Landroid/util/DisplayMetrics;

    invoke-static {v5, v6, v7}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 105
    move-object v4, v2

    const/4 v5, 0x1

    const/16 v6, 0x78

    int-to-float v6, v6

    move-object v7, v0

    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v7

    invoke-virtual {v7}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v7

    invoke-static {v5, v6, v7}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    float-to-int v5, v5

    const/4 v6, 0x1

    const/16 v7, 0x3c

    int-to-float v7, v7

    move-object v8, v0

    invoke-virtual {v8}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v8

    invoke-virtual {v8}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v8

    invoke-static {v6, v7, v8}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v6

    float-to-int v6, v6

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setSize(II)V

    .line 106
    move-object v4, v2

    move-object v0, v4

    return-object v0
.end method

.method public static Fuckddk(Landroid/content/Context;)Landroid/graphics/drawable/StateListDrawable;
    .locals 10

    .prologue
    .line 92
    move-object v0, p0

    new-instance v4, Landroid/graphics/drawable/StateListDrawable;

    move-object v9, v4

    move-object v4, v9

    move-object v5, v9

    invoke-direct {v5}, Landroid/graphics/drawable/StateListDrawable;-><init>()V

    move-object v2, v4

    .line 93
    move-object v4, v2

    const/4 v5, 0x1

    new-array v5, v5, [I

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    const/4 v7, 0x0

    const v8, 0x10100a7

    aput v8, v6, v7

    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fucking(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v6

    check-cast v6, Landroid/graphics/drawable/Drawable;

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/StateListDrawable;->addState([ILandroid/graphics/drawable/Drawable;)V

    .line 94
    move-object v4, v2

    const/4 v5, 0x1

    new-array v5, v5, [I

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    const/4 v7, 0x0

    const v8, 0x101009c

    aput v8, v6, v7

    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fucking(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v6

    check-cast v6, Landroid/graphics/drawable/Drawable;

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/StateListDrawable;->addState([ILandroid/graphics/drawable/Drawable;)V

    .line 95
    move-object v4, v2

    const/4 v5, 0x2

    new-array v5, v5, [I

    fill-array-data v5, :array_0

    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fuckass(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v6

    check-cast v6, Landroid/graphics/drawable/Drawable;

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/StateListDrawable;->addState([ILandroid/graphics/drawable/Drawable;)V

    .line 96
    move-object v4, v2

    move-object v0, v4

    return-object v0

    .line 95
    :array_0
    .array-data 4
        -0x101009c
        -0x10100a7
    .end array-data
.end method

.method public static Fucking(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;
    .locals 10

    .prologue
    .line 73
    move-object v0, p0

    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    move-object v9, v4

    move-object v4, v9

    move-object v5, v9

    invoke-direct {v5}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v2, v4

    .line 74
    move-object v4, v2

    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setShape(I)V

    .line 75
    move-object v4, v2

    const/4 v5, 0x3

    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->colourP()Ljava/lang/String;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    invoke-static {v6}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 76
    move-object v4, v2

    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->colourP()Ljava/lang/String;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    invoke-static {v5}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v5

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 77
    move-object v4, v2

    const/4 v5, 0x1

    const/high16 v6, 0x40a00000    # 5.0f

    move-object v7, v0

    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v7

    invoke-virtual {v7}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v7

    check-cast v7, Landroid/util/DisplayMetrics;

    invoke-static {v5, v6, v7}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 78
    move-object v4, v2

    const/4 v5, 0x1

    const/16 v6, 0x78

    int-to-float v6, v6

    move-object v7, v0

    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v7

    invoke-virtual {v7}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v7

    invoke-static {v5, v6, v7}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    float-to-int v5, v5

    const/4 v6, 0x1

    const/16 v7, 0x3c

    int-to-float v7, v7

    move-object v8, v0

    invoke-virtual {v8}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v8

    invoke-virtual {v8}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v8

    invoke-static {v6, v7, v8}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v6

    float-to-int v6, v6

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setSize(II)V

    .line 79
    move-object v4, v2

    move-object v0, v4

    return-object v0
.end method

.method public static Fuckmmp(Landroid/content/Context;Ljava/lang/String;)Landroid/graphics/drawable/Drawable;
    .locals 11

    .prologue
    .line 111
    move-object v0, p0

    move-object v1, p1

    move-object v6, v1

    check-cast v6, Ljava/lang/String;

    const/4 v7, 0x0

    invoke-static {v6, v7}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v6

    move-object v3, v6

    .line 112
    move-object v6, v3

    check-cast v6, [B

    const/4 v7, 0x0

    move-object v8, v3

    array-length v8, v8

    invoke-static {v6, v7, v8}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v6

    move-object v4, v6

    .line 113
    new-instance v6, Landroid/graphics/drawable/BitmapDrawable;

    move-object v10, v6

    move-object v6, v10

    move-object v7, v10

    move-object v8, v0

    invoke-virtual {v8}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v8

    move-object v9, v4

    invoke-direct {v7, v8, v9}, Landroid/graphics/drawable/BitmapDrawable;-><init>(Landroid/content/res/Resources;Landroid/graphics/Bitmap;)V

    move-object v0, v6

    return-object v0
.end method

.method public static native GGGGGGGG()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native HHHHHHHH()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native IIIIIIIIII()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native JJJJJJJJ()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native LLLLLLLL(F)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(F)V"
        }
    .end annotation
.end method

.method public static native LMCLMCLMCLMC()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native MADAME()Ljava/lang/String;
.end method

.method public static native MMMMMMMM(I)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation
.end method

.method public static native MUMUMUMUMU()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native NNNNNNNN(I)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation
.end method

.method public static native PATOLINOBR()Ljava/lang/String;
.end method

.method public static native PPPPPPPP()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native QQQQQQQ()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native RRRRRRR()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native SCRIPT()Ljava/lang/String;
.end method

.method public static native SPSPSPSPSP()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native YYYYYYYYYYY()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native ZZZZZZZZZ()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public static native colourP()Ljava/lang/String;
.end method

.method public static nosize(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;
    .locals 9

    .prologue
    .line 83
    move-object v0, p0

    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    move-object v8, v4

    move-object v4, v8

    move-object v5, v8

    invoke-direct {v5}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v2, v4

    .line 84
    move-object v4, v2

    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setShape(I)V

    .line 85
    move-object v4, v2

    const/4 v5, 0x5

    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->colourP()Ljava/lang/String;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    invoke-static {v6}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v6

    invoke-virtual {v4, v5, v6}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 86
    move-object v4, v2

    const/4 v5, 0x1

    const/high16 v6, 0x40a00000    # 5.0f

    move-object v7, v0

    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v7

    invoke-virtual {v7}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v7

    invoke-static {v5, v6, v7}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 87
    move-object v4, v2

    move-object v0, v4

    return-object v0
.end method
