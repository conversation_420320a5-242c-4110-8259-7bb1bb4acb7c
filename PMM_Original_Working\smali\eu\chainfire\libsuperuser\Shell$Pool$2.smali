.class final Leu/chainfire/libsuperuser/Shell$Pool$2;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Pool;->get(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$fThreaded:Leu/chainfire/libsuperuser/Shell$Threaded;

.field final synthetic val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Leu/chainfire/libsuperuser/Shell$Threaded;)V
    .locals 5

    .prologue
    .line 3472
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Pool$2;->val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;

    move-object v3, v0

    move-object v4, v2

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Pool$2;->val$fThreaded:Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    .prologue
    .line 3476
    move-object v0, p0

    move-object v2, v0

    :try_start_0
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Pool$2;->val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;

    const/4 v3, 0x1

    const/4 v4, 0x0

    invoke-interface {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;->onOpenResult(ZI)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3478
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Pool$2;->val$fThreaded:Leu/chainfire/libsuperuser/Shell$Threaded;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Threaded;->endCallback()V

    .line 3480
    return-void

    .line 3478
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Pool$2;->val$fThreaded:Leu/chainfire/libsuperuser/Shell$Threaded;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Threaded;->endCallback()V

    .line 3479
    move-object v2, v1

    throw v2
.end method
