.class Lcom/projectvb/SwitchStyle$ViewState;
.super Ljava/lang/Object;
.source "SwitchStyle.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/SwitchStyle;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ViewState"
.end annotation


# instance fields
.field buttonX:F

.field checkStateColor:I

.field checkedLineColor:I

.field radius:F


# direct methods
.method constructor <init>()V
    .locals 0

    .line 1144
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000(Lcom/projectvb/SwitchStyle$ViewState;Lcom/projectvb/SwitchStyle$ViewState;)V
    .locals 0
    .param p0, "x0"    # Lcom/projectvb/SwitchStyle$ViewState;
    .param p1, "x1"    # Lcom/projectvb/SwitchStyle$ViewState;

    .line 1127
    invoke-direct {p0, p1}, Lcom/projectvb/SwitchStyle$ViewState;->copy(Lcom/projectvb/SwitchStyle$ViewState;)V

    return-void
.end method

.method private copy(Lcom/projectvb/SwitchStyle$ViewState;)V
    .locals 1
    .param p1, "source"    # Lcom/projectvb/SwitchStyle$ViewState;

    .line 1146
    iget v0, p1, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    iput v0, p0, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    .line 1147
    iget v0, p1, Lcom/projectvb/SwitchStyle$ViewState;->checkStateColor:I

    iput v0, p0, Lcom/projectvb/SwitchStyle$ViewState;->checkStateColor:I

    .line 1148
    iget v0, p1, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    iput v0, p0, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    .line 1149
    iget v0, p1, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    iput v0, p0, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    .line 1150
    return-void
.end method
