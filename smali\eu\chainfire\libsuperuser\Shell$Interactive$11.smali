.class Leu/chainfire/libsuperuser/Shell$Interactive$11;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;)I
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

.field final synthetic val$exitCode:[I

.field final synthetic val$onSyncCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;[I)V
    .locals 6

    .prologue
    .line 2644
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive$11;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive$11;->val$onSyncCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;

    move-object v4, v0

    move-object v5, v3

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive$11;->val$exitCode:[I

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCommandResult(II)V
    .locals 6

    .prologue
    .line 2657
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$11;->val$exitCode:[I

    const/4 v4, 0x0

    move v5, v2

    aput v5, v3, v4

    .line 2658
    return-void
.end method

.method public onInputStream(Ljava/io/InputStream;)V
    .locals 4
    .param p1    # Ljava/io/InputStream;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 2652
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$11;->val$onSyncCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;

    move-object v3, v1

    invoke-interface {v2, v3}, Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;->onInputStream(Ljava/io/InputStream;)V

    .line 2653
    return-void
.end method

.method public onSTDERR(Ljava/lang/String;)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 2647
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$11;->val$onSyncCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;

    move-object v3, v1

    invoke-interface {v2, v3}, Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;->onSTDERR(Ljava/lang/String;)V

    .line 2648
    return-void
.end method
