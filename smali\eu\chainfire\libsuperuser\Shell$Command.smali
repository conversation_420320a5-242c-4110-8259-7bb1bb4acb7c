.class Leu/chainfire/libsuperuser/Shell$Command;
.super Ljava/lang/Object;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "Command"
.end annotation


# static fields
.field private static commandCounter:I


# instance fields
.field private final code:I

.field private final commands:[Ljava/lang/String;

.field private final marker:Ljava/lang/String;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private volatile markerInputStream:Leu/chainfire/libsuperuser/MarkerInputStream;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final onCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final onCommandLineListener:Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final onCommandResultListener:Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final onCommandResultListener2:Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .prologue
    .line 909
    const/4 v0, 0x0

    sput v0, Leu/chainfire/libsuperuser/Shell$Command;->commandCounter:I

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V
    .locals 18
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Leu/chainfire/libsuperuser/Shell$OnResult;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .prologue
    .line 928
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object/from16 v3, p3

    move-object v8, v0

    invoke-direct {v8}, Ljava/lang/Object;-><init>()V

    .line 924
    move-object v8, v0

    const/4 v9, 0x0

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->markerInputStream:Leu/chainfire/libsuperuser/MarkerInputStream;

    .line 929
    move-object v8, v1

    instance-of v8, v8, Ljava/lang/String;

    if-eqz v8, :cond_1

    .line 930
    move-object v8, v0

    const/4 v9, 0x1

    new-array v9, v9, [Ljava/lang/String;

    move-object/from16 v17, v9

    move-object/from16 v9, v17

    move-object/from16 v10, v17

    const/4 v11, 0x0

    move-object v12, v1

    check-cast v12, Ljava/lang/String;

    aput-object v12, v10, v11

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->commands:[Ljava/lang/String;

    .line 938
    :goto_0
    move-object v8, v0

    move v9, v2

    iput v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->code:I

    .line 939
    move-object v8, v0

    new-instance v9, Ljava/lang/StringBuilder;

    move-object/from16 v17, v9

    move-object/from16 v9, v17

    move-object/from16 v10, v17

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Ljava/util/UUID;->randomUUID()Ljava/util/UUID;

    move-result-object v10

    invoke-virtual {v10}, Ljava/util/UUID;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    sget-object v10, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v11, "-%08x"

    const/4 v12, 0x1

    new-array v12, v12, [Ljava/lang/Object;

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    const/4 v14, 0x0

    sget v15, Leu/chainfire/libsuperuser/Shell$Command;->commandCounter:I

    const/16 v16, 0x1

    add-int/lit8 v15, v15, 0x1

    move/from16 v17, v15

    move/from16 v15, v17

    move/from16 v16, v17

    sput v16, Leu/chainfire/libsuperuser/Shell$Command;->commandCounter:I

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    aput-object v15, v13, v14

    invoke-static {v10, v11, v12}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->marker:Ljava/lang/String;

    .line 941
    const/4 v8, 0x0

    move-object v4, v8

    .line 942
    const/4 v8, 0x0

    move-object v5, v8

    .line 943
    const/4 v8, 0x0

    move-object v6, v8

    .line 944
    const/4 v8, 0x0

    move-object v7, v8

    .line 945
    move-object v8, v3

    if-eqz v8, :cond_0

    .line 946
    move-object v8, v3

    instance-of v8, v8, Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    if-eqz v8, :cond_4

    .line 947
    move-object v8, v3

    check-cast v8, Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-object v7, v8

    .line 958
    :cond_0
    :goto_1
    move-object v8, v0

    move-object v9, v4

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->onCommandResultListener:Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    .line 959
    move-object v8, v0

    move-object v9, v5

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->onCommandResultListener2:Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    .line 960
    move-object v8, v0

    move-object v9, v6

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->onCommandLineListener:Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    .line 961
    move-object v8, v0

    move-object v9, v7

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->onCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    .line 962
    return-void

    .line 931
    :cond_1
    move-object v8, v1

    instance-of v8, v8, Ljava/util/List;

    if-eqz v8, :cond_2

    .line 932
    move-object v8, v0

    move-object v9, v1

    check-cast v9, Ljava/util/List;

    const/4 v10, 0x0

    new-array v10, v10, [Ljava/lang/String;

    invoke-interface {v9, v10}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v9

    check-cast v9, [Ljava/lang/String;

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->commands:[Ljava/lang/String;

    goto/16 :goto_0

    .line 933
    :cond_2
    move-object v8, v1

    instance-of v8, v8, [Ljava/lang/String;

    if-eqz v8, :cond_3

    .line 934
    move-object v8, v0

    move-object v9, v1

    check-cast v9, [Ljava/lang/String;

    check-cast v9, [Ljava/lang/String;

    iput-object v9, v8, Leu/chainfire/libsuperuser/Shell$Command;->commands:[Ljava/lang/String;

    goto/16 :goto_0

    .line 936
    :cond_3
    new-instance v8, Ljava/lang/IllegalArgumentException;

    move-object/from16 v17, v8

    move-object/from16 v8, v17

    move-object/from16 v9, v17

    const-string v10, "commands parameter must be of type String, List<String> or String[]"

    invoke-direct {v9, v10}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v8

    .line 948
    :cond_4
    move-object v8, v3

    instance-of v8, v8, Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    if-eqz v8, :cond_5

    .line 949
    move-object v8, v3

    check-cast v8, Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-object v6, v8

    goto :goto_1

    .line 950
    :cond_5
    move-object v8, v3

    instance-of v8, v8, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    if-eqz v8, :cond_6

    .line 951
    move-object v8, v3

    check-cast v8, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-object v5, v8

    goto :goto_1

    .line 952
    :cond_6
    move-object v8, v3

    instance-of v8, v8, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    if-eqz v8, :cond_7

    .line 953
    move-object v8, v3

    check-cast v8, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-object v4, v8

    goto :goto_1

    .line 955
    :cond_7
    new-instance v8, Ljava/lang/IllegalArgumentException;

    move-object/from16 v17, v8

    move-object/from16 v8, v17

    move-object/from16 v9, v17

    const-string v10, "OnResult is not a supported callback interface"

    invoke-direct {v9, v10}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v8
.end method

.method static synthetic access$1600(Leu/chainfire/libsuperuser/Shell$Command;)[Ljava/lang/String;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->commands:[Ljava/lang/String;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->onCommandResultListener:Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->onCommandResultListener2:Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->onCommandInputStreamListener:Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->marker:Ljava/lang/String;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2100(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/MarkerInputStream;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->markerInputStream:Leu/chainfire/libsuperuser/MarkerInputStream;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2102(Leu/chainfire/libsuperuser/Shell$Command;Leu/chainfire/libsuperuser/MarkerInputStream;)Leu/chainfire/libsuperuser/MarkerInputStream;
    .locals 7

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    move-object v5, v2

    move-object v6, v3

    move-object v2, v6

    move-object v3, v5

    move-object v4, v6

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Command;->markerInputStream:Leu/chainfire/libsuperuser/MarkerInputStream;

    move-object v0, v2

    return-object v0
.end method

.method static synthetic access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->onCommandLineListener:Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I
    .locals 2

    .prologue
    .line 908
    move-object v0, p0

    move-object v1, v0

    iget v1, v1, Leu/chainfire/libsuperuser/Shell$Command;->code:I

    move v0, v1

    return v0
.end method
