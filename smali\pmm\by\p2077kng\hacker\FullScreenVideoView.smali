.class public Lpmm/by/p2077kng/hacker/FullScreenVideoView;
.super Landroid/widget/VideoView;
.source "FullScreenVideoView.java"


# instance fields
.field private mVideoHeight:I

.field private mVideoWidth:I


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 5

    .prologue
    .line 14
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    move-object v4, v1

    invoke-direct {v3, v4}, Landroid/widget/VideoView;-><init>(Landroid/content/Context;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 7

    .prologue
    .line 18
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    move-object v5, v1

    move-object v6, v2

    invoke-direct {v4, v5, v6}, Landroid/widget/VideoView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 9

    .prologue
    .line 22
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v5, v0

    move-object v6, v1

    move-object v7, v2

    move v8, v3

    invoke-direct {v5, v6, v7, v8}, Landroid/widget/VideoView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method protected onMeasure(II)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 33
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v9, v0

    iget v9, v9, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoWidth:I

    move v10, v1

    invoke-static {v9, v10}, Landroid/view/View;->getDefaultSize(II)I

    move-result v9

    move v4, v9

    .line 34
    move-object v9, v0

    iget v9, v9, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoHeight:I

    move v10, v2

    invoke-static {v9, v10}, Landroid/view/View;->getDefaultSize(II)I

    move-result v9

    move v5, v9

    .line 36
    move-object v9, v0

    iget v9, v9, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoWidth:I

    const/4 v10, 0x0

    if-le v9, v10, :cond_0

    move-object v9, v0

    iget v9, v9, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoHeight:I

    const/4 v10, 0x0

    if-le v9, v10, :cond_0

    .line 37
    move v9, v4

    int-to-float v9, v9

    move v10, v5

    int-to-float v10, v10

    div-float/2addr v9, v10

    move v6, v9

    .line 38
    move-object v9, v0

    iget v9, v9, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoWidth:I

    int-to-float v9, v9

    move-object v10, v0

    iget v10, v10, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoHeight:I

    int-to-float v10, v10

    div-float/2addr v9, v10

    move v7, v9

    .line 39
    move v9, v7

    move v10, v6

    cmpl-float v9, v9, v10

    if-lez v9, :cond_1

    .line 40
    move v9, v4

    int-to-float v9, v9

    move v10, v7

    div-float/2addr v9, v10

    float-to-int v9, v9

    move v5, v9

    .line 45
    :cond_0
    :goto_0
    move-object v9, v0

    move v10, v4

    move v11, v5

    invoke-virtual {v9, v10, v11}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->setMeasuredDimension(II)V

    return-void

    .line 42
    :cond_1
    move v9, v5

    int-to-float v9, v9

    move v10, v7

    mul-float/2addr v9, v10

    float-to-int v9, v9

    move v4, v9

    goto :goto_0
.end method

.method public setVideoSize(II)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)V"
        }
    .end annotation

    .prologue
    .line 26
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v4, v0

    move v5, v1

    iput v5, v4, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoWidth:I

    .line 27
    move-object v4, v0

    move v5, v2

    iput v5, v4, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->mVideoHeight:I

    .line 28
    move-object v4, v0

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->requestLayout()V

    return-void
.end method
