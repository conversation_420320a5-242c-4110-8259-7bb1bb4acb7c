.class Lpmm/by/p2077kng/hacker/Loader$100000025;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000025"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;

.field private final val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Loader$100000025;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Loader$100000025;->val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000025;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000025;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1213
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000025;->val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;

    invoke-interface {v3}, Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;->OnWrite()V

    return-void
.end method
