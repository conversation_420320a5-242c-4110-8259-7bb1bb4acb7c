.class interface Lcom/bad/modder/injector/InjectorService$BTN;
.super Ljava/lang/Object;
.source "InjectorService.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20a
    name = "BTN"
.end annotation


# virtual methods
.method public abstract OnWrite(Z)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)V"
        }
    .end annotation
.end method
