.class Lcom/bad/modder/injector/InjectorService$100000015;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000015"
.end annotation


# instance fields
.field private isActive:Z

.field private final this$0:Lcom/bad/modder/injector/InjectorService;

.field private final val$button:Landroid/widget/TextView;

.field private final val$feature2:Ljava/lang/String;

.field private final val$interfaceBtn:Lcom/bad/modder/injector/InjectorService$InterfaceBtn;


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/InjectorService;Lcom/bad/modder/injector/InjectorService$InterfaceBtn;Landroid/widget/TextView;Ljava/lang/String;)V
    .locals 8

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v6, v0

    invoke-direct {v6}, Ljava/lang/Object;-><init>()V

    move-object v6, v0

    move-object v7, v1

    iput-object v7, v6, Lcom/bad/modder/injector/InjectorService$100000015;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v6, v0

    move-object v7, v2

    iput-object v7, v6, Lcom/bad/modder/injector/InjectorService$100000015;->val$interfaceBtn:Lcom/bad/modder/injector/InjectorService$InterfaceBtn;

    move-object v6, v0

    move-object v7, v3

    iput-object v7, v6, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    move-object v6, v0

    move-object v7, v4

    iput-object v7, v6, Lcom/bad/modder/injector/InjectorService$100000015;->val$feature2:Ljava/lang/String;

    move-object v6, v0

    const/4 v7, 0x1

    iput-boolean v7, v6, Lcom/bad/modder/injector/InjectorService$100000015;->isActive:Z

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/InjectorService$100000015;)Lcom/bad/modder/injector/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000015;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1029
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$interfaceBtn:Lcom/bad/modder/injector/InjectorService$InterfaceBtn;

    invoke-interface {v7}, Lcom/bad/modder/injector/InjectorService$InterfaceBtn;->OnWrite()V

    .line 1030
    move-object v7, v0

    iget-boolean v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->isActive:Z

    if-eqz v7, :cond_1

    .line 1031
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    new-instance v8, Ljava/lang/StringBuffer;

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    move-object v9, v0

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService$100000015;->val$feature2:Ljava/lang/String;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, ""

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1032
    move-object v7, v0

    const/4 v8, 0x0

    iput-boolean v8, v7, Lcom/bad/modder/injector/InjectorService$100000015;->isActive:Z

    .line 1033
    new-instance v7, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    invoke-direct {v8}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v3, v7

    .line 1034
    move-object v7, v3

    const-string v8, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3DmULDyYlf30CHh57XBRmB35iZRUSYkAbJyd/VzEfBH9dExAxeVkdbBdPex8iE3pqPxEeFXEULHJ/ag07HmRCEx0kV1M7BCt4dBcscm5eIjMiYHs9Yx5vaSAyd3tdFBMxfmE+EXRgURd7EFd9MxYOFEASF3JVbDsWKGVRJT0XbFs8EXVdSQUsMVlpOxExYEAxMidKcWIcK0UNNxYHSF0AI21veRMOFX95JhoTY1kPORdrWWRsHkhAEwInfnk0EQBBUREtchxeOBFxYkEHOidVTGIcdVVQFywPYn4QFSJvUGwYJGlLAzcoHGgdLn51WhQRLmVQEzUjf1smFXVBWQ8QNWxeDSdzSEExNSdHcSIRE3tXEDkfaWkDPwJqUSE7I1R+Mh0rFAEcBj4fWWRkFmJqMSYfSnUCHR5/eQwXD0NqZGwFYHoHEidFfT8cdUFdGhYAWVlnJw5iUDFhIEBxBxUDRnoSFzFLYTtgFWVqMTwkb3UMHgFBdBQuJUJwEjMiYVZgZRFFaQM3K0VvFCMPa2s4ZCtqahcWFW9XJhV0VV0RByJXaxARPmNrMREfeWEiFg4UQBItMUtrZycSdHobbQVAaTged38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9/XTg/JGB5Ez4jfE8NEHZjXRoufx9ZEj8ea1AbeyB+fWYYKntcEjwfbG4CEW1oaWA9E3l9FhErXUAbPgd8XWc/EmFsE20feUwyEnRZSh0tD3lZPmwLZV8TFiJvaSAeE2dPED4LRGEDDiF3ahMSJ3x1Zh92e3MdIzEcWQJscnd7ISUnRVsCHCp7SBQ8AB1uAxUUbHBgExF8dQccK0VqEmYPQV0CETxgez0SBUVxIRkTWVEUPSV+XGY/Pn1PAxUneU8xMBR8chA+MX1rHWATd3sXbSNXVD0cdV1IMDwPX2JlPx9vQGx7HnlfYh90ew8PFg9HXmY/aWhwDz4ef1cgMj4UYhA+fkhZZBUkSEEXfyB6YQwfKFlbED4xS1oSYAVob2Q7J3p5bBF1QQETPANfYAAVJH1fMWweelt6EStrSxBmdktdACM/aGsheyN/XxQZE0lcEQMXf2INJ39sXwNhI3oIIh8taGwQLAtla2Q7In1BYGcnRXUDHg5GeREWfxxvADMXawsfYSdAaj0YEUl6HAcfS1odAQVoamA1I39bAh8eFE0UZhd4WmURIGl7MQIjfHUHH3dBchssC31pA2QFaGwtMyNKXCYedBQNFDxydVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsDmB5B2wlVVsdGR5BXxA8E2thEBUkfXsbbCd+cTEVDl5zFwAxfWk7FRN3egM8J39LDB53SXQTLBNfXWRgF2FwHxAeQFt6GCt7aRBmC39eZDchYFFgAh9FVyESd0VZED0XRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1035
    move-object v7, v3

    const/4 v8, 0x4

    int-to-float v8, v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1036
    new-instance v7, Landroid/graphics/drawable/RippleDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    new-instance v9, Landroid/content/res/ColorStateList;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    const/4 v11, 0x1

    new-array v11, v11, [[I

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    const/4 v13, 0x0

    const/4 v14, 0x0

    new-array v14, v14, [I

    aput-object v14, v12, v13

    const/4 v12, 0x1

    new-array v12, v12, [I

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x0

    const-string v15, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3DmULDyYlf30CHh57XBRmB35iZRUSYkAbJyd/VzEfBH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfaQA7HmRCE2ElaVNmF3RvDRIsH3xuDRECa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsVcn1PHz8XbAA4ESp/ARcsA0h+DRETYXAPIiRKcWIcK0UNNxw1RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHeV8DMD5/Wh0FH0twEhFtaGxgOxNvdRceHlVAHDwPYm4EYCJvCx8CHnx1eh0ofw4dLn51WhQVcmhrJXsDWm0mGRBBXxQWE2xhDT8kYFATFR96cTYfdRQPEBMfaWkDPwJqUSE7I1R+Mh0rFAEcBj4fWWRkFmJqMSYfSnUCHR5/eQwXD0NqZGwFYHoHEidFfT8cdUFdGhYAVWJlHiF3eht7EmpxNjAQe2kdEDFLYThgA3R6MSYXRXUMHgFBdBQuJUJeImQfb2oxHCRKaQMyK0VIEiwlR2oDNxVrYAMWEXwABBoRVV0RByJXaxARPmNrMREfeWEzMBR8aBAWC0tZAj8CfU8fJRN+fTgWEF1aBRYTQn4QFSB9fGE3JEp6PhIBFF4SLQN/XTg7K2V7ZBIjfE82Gh5VThpmF2thHT8kZ1AbeyN8CA8cLlVoEBAxbG4CHiplURM6I1VMJh0oQWgbPgd8XWc/EmFsE20feUwyESt/eRgAMUddDWQRZXs9AgV5eW0wKkETExMHS1pnOz5lewcfJ3x1ZhEeWV4SFQdlWWcncnd7ISYfbAAGFXd7ARA8EBxpAxUUb1AbYh5KcTsVK39qGAYxQV47EQ5oYAMOH295BBgTWWIUPn5uXgBsIGdqG38genExMHUVbBIQPh9gZmATaGsDIhNAfSwRHl1JBTwPX2JlPx9vQGwYJ1dLFxYRe0kMHDVHXhBsCGhwDz4VfAABGRFZUB0ANWJZExE+aFEXYQdpYSIRdFlbED4xS1oSYAVob2Q7J3p5bBF1QQETPANfYAAVJH1fMWweelt6EStrSxBmdktdACM/aGsheyN/XxQZE0lcEQMXf2INJ39sXwNhI3oIIh8taGwQLAtla2Q7In1BYGcnRXUDHg5GeREWfxxvADMXawsfYSdAaj0YEUl6HAcfS1odAQVoamA1I39bAh8eFE0UZhd4WmURIGl7MQIjfHUHH3dBchssC31pA2QFaGwtMyNKXCYedBQNFDxydVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsDmB5B2wlVVsdGR5BXxA8E2thEBUkfXsbbCd+cTEVDl5zFwAxfWk7FRN3egM8J39LDB53SXQTLBNfXWRgF2FwHxAeQFt6GCt7aRBmC39eZDchYFFgAh9FVyESd0VZED0XRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v15

    aput v15, v13, v14

    invoke-direct {v10, v11, v12}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    move-object v10, v3

    const/4 v11, 0x0

    check-cast v11, Landroid/graphics/drawable/Drawable;

    invoke-direct {v8, v9, v10, v11}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v4, v7

    .line 1037
    sget v7, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v8, 0x15

    if-lt v7, v8, :cond_0

    .line 1038
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    const/high16 v8, 0x42b40000    # 90.0f

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setElevation(F)V

    .line 1040
    :cond_0
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    move-object v8, v4

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1054
    :goto_0
    return-void

    .line 1043
    :cond_1
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    new-instance v8, Ljava/lang/StringBuffer;

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    move-object v9, v0

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService$100000015;->val$feature2:Ljava/lang/String;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, ""

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1044
    move-object v7, v0

    const/4 v8, 0x1

    iput-boolean v8, v7, Lcom/bad/modder/injector/InjectorService$100000015;->isActive:Z

    .line 1045
    new-instance v7, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    invoke-direct {v8}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v3, v7

    .line 1046
    move-object v7, v3

    const-string v8, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3DmULDyYlf30CHh57XBRmB35iZRUSYkAbJyd/VzEfBH9dExAxeVkdbBdPex8iE3pqPxEeFXEULHJ/ag07HmRCEx0kV1M7BCt4dBcscm5eIjMiYHs9Yx5vaSAyd3tdFBMxfmE+EXRgURd7EFd9MxYOFEASF3JVbDsWKGVRJT0XbFs8EXVdSQUsMVlpOxExYEAxMidKcWIcK0UNNxYHSF0AI21veRMOFX95JhoTY1kPORdrWWRsHkhAEwInfnk0EQBBUREtchxeOBFxYkEHOidVTGIcdVVQFywPYn4QFSJvUGwYJGlLAzcoHGgdLn51WhQRLmVQEzUjf1smFXVBWQ8QNWxeDSdzSEExNSdHcSIRE3tXEDkfaWkDPwJqUSE7I1R+Mh0rFAEcBj4fWWRkFmJqMSYfSnUCHR5/eQwXD0NqZGwFYHoHEidFfT8cdUFdGhYAWVlnJw5iUDFhIEBxBxUDRnoSFzFLYTtgFWVqMTwkb3UMHgFBdBQuJUJwEjMiYVZgZRFFaQM3K0VvFCMPa2s4ZCtqahcWFW9XJhV0VV0RByJXaxARPmNrMREfeWEiFg4UQBItMUtrZycSdHobbQVAaTged38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9/XTg/JGB5Ez4jfE8NEHZjXRoufx9ZEj8ea1AbeyB+fWYYKntcEjwfbG4CEW1oaWA9E3l9FhErXUAbPgd8XWc/EmFsE20feUwyEnRZSh0tD3lZPmwLZV8TFiJvaSAeE2dPED4LRGEDDiF3ahMSJ3x1Zh92e3MdIzEcWQJscnd7ISUnRVsCHCp7SBQ8AB1uAxUUbHBgExF8dQccK0VqEmYPQV0CETxgez0SBUVxIRkTWVEUPSV+XGY/Pn1PAxUneU8xMBR8chA+MX1rHWATd3sXbSNXVD0cdV1IMDwPX2JlPx9vQGx7HnlfYh90ew8PFg9HXmY/aWhwDz4ef1cgMj4UYhA+fkhZZBUkSEEXfyB6YQwfKFlbED4xS1oSYAVob2Q7J3p5bBF1QQETPANfYAAVJH1fMWweelt6EStrSxBmdktdACM/aGsheyN/XxQZE0lcEQMXf2INJ39sXwNhI3oIIh8taGwQLAtla2Q7In1BYGcnRXUDHg5GeREWfxxvADMXawsfYSdAaj0YEUl6HAcfS1odAQVoamA1I39bAh8eFE0UZhd4WmURIGl7MQIjfHUHH3dBchssC31pA2QFaGwtMyNKXCYedBQNFDxydVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsDmB5B2wlVVsdGR5BXxA8E2thEBUkfXsbbCd+cTEVDl5zFwAxfWk7FRN3egM8J39LDB53SXQTLBNfXWRgF2FwHxAeQFt6GCt7aRBmC39eZDchYFFgAh9FVyESd0VZED0XRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1047
    move-object v7, v3

    const/4 v8, 0x4

    int-to-float v8, v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1048
    sget v7, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v8, 0x15

    if-lt v7, v8, :cond_2

    .line 1049
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    const/high16 v8, 0x42b40000    # 90.0f

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setElevation(F)V

    .line 1051
    :cond_2
    move-object v7, v3

    move-object v4, v7

    .line 1052
    new-instance v7, Landroid/graphics/drawable/RippleDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    new-instance v9, Landroid/content/res/ColorStateList;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    const/4 v11, 0x1

    new-array v11, v11, [[I

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    const/4 v13, 0x0

    const/4 v14, 0x0

    new-array v14, v14, [I

    aput-object v14, v12, v13

    const/4 v12, 0x1

    new-array v12, v12, [I

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x0

    const-string v15, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3DmULDyYlf30CHh57XBRmB35iZRUSYkAbJyd/VzEfBH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfaQA7HmRCE2ElaVNmF3RvDRIsH3xuDRECa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsVcn1PHz8XbAA4ESp/ARcsA0h+DRETYXAPIiRKcWIcK0UNNxw1RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHeV8DMD5/Wh0FH0twEhFtaGxgOxNvdRceHlVAHDwPYm4EYCJvCx8CHnx1eh0ofw4dLn51WhQVcmhrJXsDWm0mGRBBXxQWE2xhDT8kYFATFR96cTYfdRQPEBMfaWkDPwJqUSE7I1R+Mh0rFAEcBj4fWWRkFmJqMSYfSnUCHR5/eQwXD0NqZGwFYHoHEidFfT8cdUFdGhYAVWJlHiF3eht7EmpxNjAQe2kdEDFLYThgA3R6MSYXRXUMHgFBdBQuJUJeImQfb2oxHCRKaQMyK0VIEiwlR2oDNxVrYAMWEXwABBoRVV0RByJXaxARPmNrMREfeWEzMBR8aBAWC0tZAj8CfU8fJRN+fTgWEF1aBRYTQn4QFSB9fGE3JEp6PhIBFF4SLQN/XTg7K2V7ZBIjfE82Gh5VThpmF2thHT8kZ1AbeyN8CA8cLlVoEBAxbG4CHiplURM6I1VMJh0oQWgbPgd8XWc/EmFsE20feUwyESt/eRgAMUddDWQRZXs9AgV5eW0wKkETExMHS1pnOz5lewcfJ3x1ZhEeWV4SFQdlWWcncnd7ISYfbAAGFXd7ARA8EBxpAxUUb1AbYh5KcTsVK39qGAYxQV47EQ5oYAMOH295BBgTWWIUPn5uXgBsIGdqG38genExMHUVbBIQPh9gZmATaGsDIhNAfSwRHl1JBTwPX2JlPx9vQGwYJ1dLFxYRe0kMHDVHXhBsCGhwDz4VfAABGRFZUB0ANWJZExE+aFEXYQdpYSIRdFlbED4xS1oSYAVob2Q7J3p5bBF1QQETPANfYAAVJH1fMWweelt6EStrSxBmdktdACM/aGsheyN/XxQZE0lcEQMXf2INJ39sXwNhI3oIIh8taGwQLAtla2Q7In1BYGcnRXUDHg5GeREWfxxvADMXawsfYSdAaj0YEUl6HAcfS1odAQVoamA1I39bAh8eFE0UZhd4WmURIGl7MQIjfHUHH3dBchssC31pA2QFaGwtMyNKXCYedBQNFDxydVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsDmB5B2wlVVsdGR5BXxA8E2thEBUkfXsbbCd+cTEVDl5zFwAxfWk7FRN3egM8J39LDB53SXQTLBNfXWRgF2FwHxAeQFt6GCt7aRBmC39eZDchYFFgAh9FVyESd0VZED0XRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v15

    aput v15, v13, v14

    invoke-direct {v10, v11, v12}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    move-object v10, v3

    const/4 v11, 0x0

    check-cast v11, Landroid/graphics/drawable/Drawable;

    invoke-direct {v8, v9, v10, v11}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v5, v7

    .line 1053
    move-object v7, v4

    sget-object v8, Landroid/graphics/drawable/GradientDrawable$Orientation;->TL_BR:Landroid/graphics/drawable/GradientDrawable$Orientation;

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setOrientation(Landroid/graphics/drawable/GradientDrawable$Orientation;)V

    .line 1054
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000015;->val$button:Landroid/widget/TextView;

    move-object v8, v5

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    goto/16 :goto_0
.end method
