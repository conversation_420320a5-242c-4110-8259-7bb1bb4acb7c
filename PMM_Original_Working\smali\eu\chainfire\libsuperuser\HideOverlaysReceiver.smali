.class public abstract Leu/chainfire/libsuperuser/HideOverlaysReceiver;
.super Landroid/content/BroadcastReceiver;
.source "HideOverlaysReceiver.java"


# static fields
.field public static final ACTION_HIDE_OVERLAYS:Ljava/lang/String; = "eu.chainfire.supersu.action.HIDE_OVERLAYS"

.field public static final CATEGORY_HIDE_OVERLAYS:Ljava/lang/String; = "android.intent.category.INFO"

.field public static final EXTRA_HIDE_OVERLAYS:Ljava/lang/String; = "eu.chainfire.supersu.extra.HIDE"


# direct methods
.method public constructor <init>()V
    .locals 2

    .prologue
    .line 41
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract onHideOverlays(Landroid/content/Context;Landroid/content/Intent;Z)V
.end method

.method public final onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 9

    .prologue
    .line 48
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, v2

    const-string v4, "eu.chainfire.supersu.extra.HIDE"

    invoke-virtual {v3, v4}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 49
    move-object v3, v0

    move-object v4, v1

    move-object v5, v2

    move-object v6, v2

    const-string v7, "eu.chainfire.supersu.extra.HIDE"

    const/4 v8, 0x0

    invoke-virtual {v6, v7, v8}, Landroid/content/Intent;->getBooleanExtra(Ljava/lang/String;Z)Z

    move-result v6

    invoke-virtual {v3, v4, v5, v6}, Leu/chainfire/libsuperuser/HideOverlaysReceiver;->onHideOverlays(Landroid/content/Context;Landroid/content/Intent;Z)V

    .line 51
    :cond_0
    return-void
.end method
