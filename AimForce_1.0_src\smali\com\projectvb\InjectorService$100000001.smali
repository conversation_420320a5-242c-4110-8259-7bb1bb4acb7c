.class Lcom/projectvb/InjectorService$100000001;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000001"
.end annotation


# instance fields
.field private final this$0:Lcom/projectvb/InjectorService;


# direct methods
.method constructor <init>(Lcom/projectvb/InjectorService;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/projectvb/InjectorService$100000001;->this$0:Lcom/projectvb/InjectorService;

    return-void
.end method

.method static access$0(Lcom/projectvb/InjectorService$100000001;)Lcom/projectvb/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000001;->this$0:Lcom/projectvb/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 433
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000001;->this$0:Lcom/projectvb/InjectorService;

    iget-object v3, v3, Lcom/projectvb/InjectorService;->collapse_view:Landroid/widget/RelativeLayout;

    const/4 v4, 0x0

    invoke-virtual {v3, v4}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 434
    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000001;->this$0:Lcom/projectvb/InjectorService;

    iget-object v3, v3, Lcom/projectvb/InjectorService;->mExpanded:Landroid/widget/LinearLayout;

    const/16 v4, 0x8

    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    return-void
.end method
