.class public final Lcom/bad/modder/injector/R;
.super Ljava/lang/Object;
.source "R.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bad/modder/injector/R$attr;,
        Lcom/bad/modder/injector/R$color;,
        Lcom/bad/modder/injector/R$drawable;,
        Lcom/bad/modder/injector/R$id;,
        Lcom/bad/modder/injector/R$layout;,
        Lcom/bad/modder/injector/R$string;,
        Lcom/bad/modder/injector/R$style;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 33
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method
