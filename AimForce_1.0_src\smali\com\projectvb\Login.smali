.class public Lcom/projectvb/Login;
.super Ljava/lang/Object;
.source "Login.java"


# static fields
.field private static final API_URL:Ljava/lang/String; = "https://keyauth.win/api/1.3/"

.field private static final APP_NAME:Ljava/lang/String; = "websit"

.field private static final OWNER_ID:Ljava/lang/String; = "PF7qFni9VY"

.field private static final SECRET:Ljava/lang/String; = "b684ef652abd78d502b9a4ac1e0d70c4687aeb0adbe79d3f9e1e8e63799b0bdd"

.field private static final VERSION:Ljava/lang/String; = "1.0"


# instance fields
.field private context:Landroid/content/Context;

.field private isSessionReady:Z

.field private loadingBar:Landroid/widget/ProgressBar;

.field private sessionId:Ljava/lang/String;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1
    .param p1, "glob_Context"    # Landroid/content/Context;

    .line 45
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 43
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/projectvb/Login;->isSessionReady:Z

    .line 46
    iput-object p1, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    .line 49
    invoke-direct {p0}, Lcom/projectvb/Login;->initializeSession()V

    .line 50
    return-void
.end method

.method private Init()V
    .locals 14

    .line 88
    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 89
    .local v0, "container":Landroid/widget/LinearLayout;
    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 90
    const/16 v2, 0x11

    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 91
    const-string v3, "#000000"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {v0, v3}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 93
    new-instance v3, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v3}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 94
    .local v3, "gradientContainer":Landroid/graphics/drawable/GradientDrawable;
    const/high16 v4, 0x41a00000    # 20.0f

    invoke-virtual {v3, v4}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 95
    const/4 v5, -0x1

    invoke-virtual {v3, v5}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 96
    const/4 v6, 0x2

    const v7, -0x777778

    invoke-virtual {v3, v6, v7}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 98
    new-instance v6, Landroid/widget/LinearLayout;

    iget-object v7, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-direct {v6, v7}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 99
    .local v6, "loginBox":Landroid/widget/LinearLayout;
    new-instance v7, Landroid/widget/LinearLayout$LayoutParams;

    const/16 v8, 0x2bc

    const/16 v9, 0x1c2

    invoke-direct {v7, v8, v9}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v6, v7}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 100
    invoke-virtual {v6, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 101
    invoke-virtual {v6, v1}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 102
    invoke-virtual {v6, v3}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 103
    const/16 v7, 0x28

    invoke-virtual {v6, v7, v7, v7, v7}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 105
    new-instance v7, Landroid/widget/TextView;

    iget-object v8, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-direct {v7, v8}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 106
    .local v7, "title":Landroid/widget/TextView;
    const-string v8, "Enter Your License Key"

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 107
    invoke-virtual {v7, v4}, Landroid/widget/TextView;->setTextSize(F)V

    .line 108
    invoke-virtual {v7, v2}, Landroid/widget/TextView;->setGravity(I)V

    .line 109
    const/4 v2, 0x0

    const/16 v4, 0x1e

    invoke-virtual {v7, v2, v2, v2, v4}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 111
    new-instance v4, Landroid/widget/EditText;

    iget-object v8, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-direct {v4, v8}, Landroid/widget/EditText;-><init>(Landroid/content/Context;)V

    .line 112
    .local v4, "inputLicenseKey":Landroid/widget/EditText;
    const-string v8, "License Key"

    invoke-virtual {v4, v8}, Landroid/widget/EditText;->setHint(Ljava/lang/CharSequence;)V

    .line 113
    const/high16 v8, 0x41800000    # 16.0f

    invoke-virtual {v4, v8}, Landroid/widget/EditText;->setTextSize(F)V

    .line 114
    const/4 v9, 0x0

    invoke-virtual {v4, v9}, Landroid/widget/EditText;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 115
    const/16 v10, 0x14

    invoke-virtual {v4, v10, v10, v10, v10}, Landroid/widget/EditText;->setPadding(IIII)V

    .line 118
    iget-object v11, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    const-string v12, "RageCheatsPrefs"

    invoke-virtual {v11, v12, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v2

    .line 119
    const-string v11, "saved_license"

    const-string v12, ""

    invoke-interface {v2, v11, v12}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 120
    .local v2, "savedKey":Ljava/lang/String;
    invoke-virtual {v4, v2}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 122
    new-instance v11, Landroid/widget/ProgressBar;

    iget-object v12, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-direct {v11, v12}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;)V

    iput-object v11, p0, Lcom/projectvb/Login;->loadingBar:Landroid/widget/ProgressBar;

    .line 123
    const/16 v12, 0x8

    invoke-virtual {v11, v12}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 124
    iget-object v11, p0, Lcom/projectvb/Login;->loadingBar:Landroid/widget/ProgressBar;

    new-instance v12, Landroid/widget/LinearLayout$LayoutParams;

    const/16 v13, 0x64

    invoke-direct {v12, v13, v13}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v11, v12}, Landroid/widget/ProgressBar;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 126
    new-instance v11, Landroid/widget/Button;

    iget-object v12, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-direct {v11, v12}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 127
    .local v11, "loginButton":Landroid/widget/Button;
    const-string v12, "LOGIN"

    invoke-virtual {v11, v12}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 128
    invoke-virtual {v11, v8}, Landroid/widget/Button;->setTextSize(F)V

    .line 129
    invoke-virtual {v11, v5}, Landroid/widget/Button;->setTextColor(I)V

    .line 130
    invoke-virtual {v11, v10, v10, v10, v10}, Landroid/widget/Button;->setPadding(IIII)V

    .line 131
    invoke-virtual {v11, v1, v9}, Landroid/widget/Button;->setLayerType(ILandroid/graphics/Paint;)V

    .line 133
    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 134
    .local v1, "drawable":Landroid/graphics/drawable/GradientDrawable;
    const v5, -0xffff01

    invoke-virtual {v1, v5}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 135
    const/high16 v8, 0x42480000    # 50.0f

    invoke-virtual {v1, v8}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 136
    const-string v8, "#AA0000"

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    const/4 v9, 0x5

    invoke-virtual {v1, v9, v8}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 138
    invoke-virtual {v11, v1}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 139
    const/high16 v8, 0x41700000    # 15.0f

    const/4 v9, 0x0

    invoke-virtual {v11, v8, v9, v9, v5}, Landroid/widget/Button;->setShadowLayer(FFFI)V

    .line 141
    new-instance v5, Lcom/projectvb/Login$2;

    invoke-direct {v5, p0, v4, v11}, Lcom/projectvb/Login$2;-><init>(Lcom/projectvb/Login;Landroid/widget/EditText;Landroid/widget/Button;)V

    invoke-virtual {v11, v5}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 212
    iget-object v5, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    check-cast v5, Landroid/app/Activity;

    invoke-virtual {v5, v0}, Landroid/app/Activity;->setContentView(Landroid/view/View;)V

    .line 213
    invoke-virtual {v0, v6}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 214
    invoke-virtual {v6, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 215
    invoke-virtual {v6, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 216
    iget-object v5, p0, Lcom/projectvb/Login;->loadingBar:Landroid/widget/ProgressBar;

    invoke-virtual {v6, v5}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 217
    invoke-virtual {v6, v11}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 218
    return-void
.end method

.method static synthetic access$000(Lcom/projectvb/Login;Ljava/lang/String;)Lorg/json/JSONObject;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Login;
    .param p1, "x1"    # Ljava/lang/String;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 30
    invoke-direct {p0, p1}, Lcom/projectvb/Login;->sendRequest(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v0

    return-object v0
.end method

.method static synthetic access$100(Lcom/projectvb/Login;)Ljava/lang/String;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Login;

    .line 30
    iget-object v0, p0, Lcom/projectvb/Login;->sessionId:Ljava/lang/String;

    return-object v0
.end method

.method static synthetic access$102(Lcom/projectvb/Login;Ljava/lang/String;)Ljava/lang/String;
    .locals 0
    .param p0, "x0"    # Lcom/projectvb/Login;
    .param p1, "x1"    # Ljava/lang/String;

    .line 30
    iput-object p1, p0, Lcom/projectvb/Login;->sessionId:Ljava/lang/String;

    return-object p1
.end method

.method static synthetic access$200(Lcom/projectvb/Login;)Z
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Login;

    .line 30
    iget-boolean v0, p0, Lcom/projectvb/Login;->isSessionReady:Z

    return v0
.end method

.method static synthetic access$202(Lcom/projectvb/Login;Z)Z
    .locals 0
    .param p0, "x0"    # Lcom/projectvb/Login;
    .param p1, "x1"    # Z

    .line 30
    iput-boolean p1, p0, Lcom/projectvb/Login;->isSessionReady:Z

    return p1
.end method

.method static synthetic access$300(Lcom/projectvb/Login;)V
    .locals 0
    .param p0, "x0"    # Lcom/projectvb/Login;

    .line 30
    invoke-direct {p0}, Lcom/projectvb/Login;->Init()V

    return-void
.end method

.method static synthetic access$400(Lcom/projectvb/Login;Ljava/lang/String;)V
    .locals 0
    .param p0, "x0"    # Lcom/projectvb/Login;
    .param p1, "x1"    # Ljava/lang/String;

    .line 30
    invoke-direct {p0, p1}, Lcom/projectvb/Login;->showToast(Ljava/lang/String;)V

    return-void
.end method

.method static synthetic access$500(Lcom/projectvb/Login;)Landroid/widget/ProgressBar;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Login;

    .line 30
    iget-object v0, p0, Lcom/projectvb/Login;->loadingBar:Landroid/widget/ProgressBar;

    return-object v0
.end method

.method static synthetic access$600(Lcom/projectvb/Login;)Ljava/lang/String;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Login;

    .line 30
    invoke-direct {p0}, Lcom/projectvb/Login;->getHWID()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method static synthetic access$700(Lcom/projectvb/Login;)Landroid/content/Context;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Login;

    .line 30
    iget-object v0, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    return-object v0
.end method

.method private getHWID()Ljava/lang/String;
    .locals 2

    .line 236
    iget-object v0, p0, Lcom/projectvb/Login;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "android_id"

    invoke-static {v0, v1}, Landroid/provider/Settings$Secure;->getString(Landroid/content/ContentResolver;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private initializeSession()V
    .locals 2

    .line 53
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/projectvb/Login$1;

    invoke-direct {v1, p0}, Lcom/projectvb/Login$1;-><init>(Lcom/projectvb/Login;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 84
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 85
    return-void
.end method

.method private sendRequest(Ljava/lang/String;)Lorg/json/JSONObject;
    .locals 7
    .param p1, "urlString"    # Ljava/lang/String;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 221
    new-instance v0, Ljava/net/URL;

    invoke-direct {v0, p1}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    .line 222
    .local v0, "url":Ljava/net/URL;
    invoke-virtual {v0}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object v1

    check-cast v1, Ljava/net/HttpURLConnection;

    .line 223
    .local v1, "conn":Ljava/net/HttpURLConnection;
    const-string v2, "GET"

    invoke-virtual {v1, v2}, Ljava/net/HttpURLConnection;->setRequestMethod(Ljava/lang/String;)V

    .line 225
    new-instance v2, Ljava/io/BufferedReader;

    new-instance v3, Ljava/io/InputStreamReader;

    invoke-virtual {v1}, Ljava/net/HttpURLConnection;->getInputStream()Ljava/io/InputStream;

    move-result-object v4

    invoke-direct {v3, v4}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v2, v3}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 226
    .local v2, "in":Ljava/io/BufferedReader;
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 228
    .local v3, "response":Ljava/lang/StringBuilder;
    :goto_0
    invoke-virtual {v2}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v4

    move-object v5, v4

    .local v5, "line":Ljava/lang/String;
    if-eqz v4, :cond_0

    .line 229
    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 231
    :cond_0
    invoke-virtual {v2}, Ljava/io/BufferedReader;->close()V

    .line 232
    new-instance v4, Lorg/json/JSONObject;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v4, v6}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    return-object v4
.end method

.method private showToast(Ljava/lang/String;)V
    .locals 2
    .param p1, "message"    # Ljava/lang/String;

    .line 240
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lcom/projectvb/Login$3;

    invoke-direct {v1, p0, p1}, Lcom/projectvb/Login$3;-><init>(Lcom/projectvb/Login;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 246
    return-void
.end method
