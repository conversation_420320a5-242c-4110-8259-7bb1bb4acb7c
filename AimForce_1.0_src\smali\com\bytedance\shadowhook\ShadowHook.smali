.class public final Lcom/bytedance/shadowhook/ShadowHook;
.super Ljava/lang/Object;
.source "ShadowHook.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;,
        Lcom/bytedance/shadowhook/ShadowHook$Config;,
        Lcom/bytedance/shadowhook/ShadowHook$ConfigBuilder;,
        Lcom/bytedance/shadowhook/ShadowHook$Mode;,
        Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
    }
.end annotation


# static fields
.field private static final ERRNO_INIT_EXCEPTION:I = 0x65

.field private static final ERRNO_LOAD_LIBRARY_EXCEPTION:I = 0x64

.field private static final ERRNO_OK:I = 0x0

.field private static final ERRNO_PENDING:I = 0x1

.field private static final ERRNO_UNINIT:I = 0x2

.field private static final defaultDebuggable:Z = false

.field private static final defaultLibLoader:Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

.field private static final defaultMode:I

.field private static final defaultRecordable:Z = false

.field private static initCostMs:J = 0x0L

.field private static initErrno:I = 0x0

.field private static inited:Z = false

.field private static final libName:Ljava/lang/String; = "shadowhook"

.field private static final recordItemAll:I = 0x3ff

.field private static final recordItemBackupLen:I = 0x80

.field private static final recordItemCallerLibName:I = 0x2

.field private static final recordItemErrno:I = 0x100

.field private static final recordItemLibName:I = 0x8

.field private static final recordItemNewAddr:I = 0x40

.field private static final recordItemOp:I = 0x4

.field private static final recordItemStub:I = 0x200

.field private static final recordItemSymAddr:I = 0x20

.field private static final recordItemSymName:I = 0x10

.field private static final recordItemTimestamp:I = 0x1


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 33
    const/4 v0, 0x0

    sput-boolean v0, Lcom/bytedance/shadowhook/ShadowHook;->inited:Z

    .line 34
    const/4 v0, 0x2

    sput v0, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    .line 35
    const-wide/16 v0, -0x1

    sput-wide v0, Lcom/bytedance/shadowhook/ShadowHook;->initCostMs:J

    .line 39
    const/4 v0, 0x0

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook;->defaultLibLoader:Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    .line 40
    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook$Mode;->SHARED:Lcom/bytedance/shadowhook/ShadowHook$Mode;

    invoke-virtual {v0}, Lcom/bytedance/shadowhook/ShadowHook$Mode;->getValue()I

    move-result v0

    sput v0, Lcom/bytedance/shadowhook/ShadowHook;->defaultMode:I

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 26
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000()Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;
    .locals 1

    .line 26
    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook;->defaultLibLoader:Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    return-object v0
.end method

.method static synthetic access$100()I
    .locals 1

    .line 26
    sget v0, Lcom/bytedance/shadowhook/ShadowHook;->defaultMode:I

    return v0
.end method

.method public static getArch()Ljava/lang/String;
    .locals 1

    .line 202
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 203
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetArch()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 205
    :cond_0
    const-string v0, "unknown"

    return-object v0
.end method

.method public static getDebuggable()Z
    .locals 1

    .line 108
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 109
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetDebuggable()Z

    move-result v0

    return v0

    .line 111
    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public static getInitCostMs()J
    .locals 2

    .line 97
    sget-wide v0, Lcom/bytedance/shadowhook/ShadowHook;->initCostMs:J

    return-wide v0
.end method

.method public static getInitErrno()I
    .locals 1

    .line 93
    sget v0, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    return v0
.end method

.method public static getMode()Lcom/bytedance/shadowhook/ShadowHook$Mode;
    .locals 2

    .line 101
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 102
    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook$Mode;->SHARED:Lcom/bytedance/shadowhook/ShadowHook$Mode;

    invoke-virtual {v0}, Lcom/bytedance/shadowhook/ShadowHook$Mode;->getValue()I

    move-result v0

    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetMode()I

    move-result v1

    if-ne v0, v1, :cond_0

    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook$Mode;->SHARED:Lcom/bytedance/shadowhook/ShadowHook$Mode;

    goto :goto_0

    :cond_0
    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook$Mode;->UNIQUE:Lcom/bytedance/shadowhook/ShadowHook$Mode;

    :goto_0
    return-object v0

    .line 104
    :cond_1
    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook$Mode;->SHARED:Lcom/bytedance/shadowhook/ShadowHook$Mode;

    return-object v0
.end method

.method public static getRecordable()Z
    .locals 1

    .line 121
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 122
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetRecordable()Z

    move-result v0

    return v0

    .line 124
    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public static varargs getRecords([Lcom/bytedance/shadowhook/ShadowHook$RecordItem;)Ljava/lang/String;
    .locals 6
    .param p0, "recordItems"    # [Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 153
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 154
    const/4 v0, 0x0

    .line 155
    .local v0, "itemFlags":I
    array-length v1, p0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, p0, v2

    .line 156
    .local v3, "recordItem":Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
    sget-object v4, Lcom/bytedance/shadowhook/ShadowHook$1;->$SwitchMap$com$bytedance$shadowhook$ShadowHook$RecordItem:[I

    invoke-virtual {v3}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->ordinal()I

    move-result v5

    aget v4, v4, v5

    packed-switch v4, :pswitch_data_0

    goto :goto_1

    .line 185
    :pswitch_0
    or-int/lit16 v0, v0, 0x200

    .line 186
    goto :goto_1

    .line 182
    :pswitch_1
    or-int/lit16 v0, v0, 0x100

    .line 183
    goto :goto_1

    .line 179
    :pswitch_2
    or-int/lit16 v0, v0, 0x80

    .line 180
    goto :goto_1

    .line 176
    :pswitch_3
    or-int/lit8 v0, v0, 0x40

    .line 177
    goto :goto_1

    .line 173
    :pswitch_4
    or-int/lit8 v0, v0, 0x20

    .line 174
    goto :goto_1

    .line 170
    :pswitch_5
    or-int/lit8 v0, v0, 0x10

    .line 171
    goto :goto_1

    .line 167
    :pswitch_6
    or-int/lit8 v0, v0, 0x8

    .line 168
    goto :goto_1

    .line 164
    :pswitch_7
    or-int/lit8 v0, v0, 0x4

    .line 165
    goto :goto_1

    .line 161
    :pswitch_8
    or-int/lit8 v0, v0, 0x2

    .line 162
    goto :goto_1

    .line 158
    :pswitch_9
    or-int/lit8 v0, v0, 0x1

    .line 159
    nop

    .line 155
    .end local v3    # "recordItem":Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 192
    :cond_0
    if-nez v0, :cond_1

    .line 193
    const/16 v0, 0x3ff

    .line 196
    :cond_1
    invoke-static {v0}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetRecords(I)Ljava/lang/String;

    move-result-object v1

    return-object v1

    .line 198
    .end local v0    # "itemFlags":I
    :cond_2
    const/4 v0, 0x0

    return-object v0

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static getVersion()Ljava/lang/String;
    .locals 1

    .line 45
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetVersion()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public static init()I
    .locals 1

    .line 49
    const/4 v0, 0x0

    invoke-static {v0}, Lcom/bytedance/shadowhook/ShadowHook;->init(Lcom/bytedance/shadowhook/ShadowHook$Config;)I

    move-result v0

    return v0
.end method

.method public static declared-synchronized init(Lcom/bytedance/shadowhook/ShadowHook$Config;)I
    .locals 6
    .param p0, "config"    # Lcom/bytedance/shadowhook/ShadowHook$Config;

    const-class v0, Lcom/bytedance/shadowhook/ShadowHook;

    monitor-enter v0

    .line 53
    :try_start_0
    sget-boolean v1, Lcom/bytedance/shadowhook/ShadowHook;->inited:Z

    if-eqz v1, :cond_0

    .line 54
    sget v1, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_2

    monitor-exit v0

    return v1

    .line 56
    :cond_0
    const/4 v1, 0x1

    :try_start_1
    sput-boolean v1, Lcom/bytedance/shadowhook/ShadowHook;->inited:Z

    .line 58
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    .line 60
    .local v1, "start":J
    if-nez p0, :cond_1

    .line 62
    new-instance v3, Lcom/bytedance/shadowhook/ShadowHook$ConfigBuilder;

    invoke-direct {v3}, Lcom/bytedance/shadowhook/ShadowHook$ConfigBuilder;-><init>()V

    invoke-virtual {v3}, Lcom/bytedance/shadowhook/ShadowHook$ConfigBuilder;->build()Lcom/bytedance/shadowhook/ShadowHook$Config;

    move-result-object v3

    move-object p0, v3

    .line 66
    :cond_1
    invoke-static {p0}, Lcom/bytedance/shadowhook/ShadowHook;->loadLibrary(Lcom/bytedance/shadowhook/ShadowHook$Config;)Z

    move-result v3

    if-nez v3, :cond_2

    .line 67
    const/16 v3, 0x64

    sput v3, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    .line 68
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    sub-long/2addr v3, v1

    sput-wide v3, Lcom/bytedance/shadowhook/ShadowHook;->initCostMs:J

    .line 69
    sget v3, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    monitor-exit v0

    return v3

    .line 74
    :cond_2
    const/16 v3, 0x65

    :try_start_2
    invoke-virtual {p0}, Lcom/bytedance/shadowhook/ShadowHook$Config;->getMode()I

    move-result v4

    invoke-virtual {p0}, Lcom/bytedance/shadowhook/ShadowHook$Config;->getDebuggable()Z

    move-result v5

    invoke-static {v4, v5}, Lcom/bytedance/shadowhook/ShadowHook;->nativeInit(IZ)I

    move-result v4

    sput v4, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 77
    goto :goto_0

    .line 75
    :catchall_0
    move-exception v4

    .line 76
    .local v4, "ignored":Ljava/lang/Throwable;
    :try_start_3
    sput v3, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    .line 80
    .end local v4    # "ignored":Ljava/lang/Throwable;
    :goto_0
    invoke-virtual {p0}, Lcom/bytedance/shadowhook/ShadowHook$Config;->getRecordable()Z

    move-result v4
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    if-eqz v4, :cond_3

    .line 82
    :try_start_4
    invoke-virtual {p0}, Lcom/bytedance/shadowhook/ShadowHook$Config;->getRecordable()Z

    move-result v4

    invoke-static {v4}, Lcom/bytedance/shadowhook/ShadowHook;->nativeSetRecordable(Z)V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    .line 85
    goto :goto_1

    .line 83
    :catchall_1
    move-exception v4

    .line 84
    .restart local v4    # "ignored":Ljava/lang/Throwable;
    :try_start_5
    sput v3, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    .line 88
    .end local v4    # "ignored":Ljava/lang/Throwable;
    :cond_3
    :goto_1
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    sub-long/2addr v3, v1

    sput-wide v3, Lcom/bytedance/shadowhook/ShadowHook;->initCostMs:J

    .line 89
    sget v3, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_2

    monitor-exit v0

    return v3

    .line 52
    .end local v1    # "start":J
    .end local p0    # "config":Lcom/bytedance/shadowhook/ShadowHook$Config;
    :catchall_2
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method private static isInitedOk()Z
    .locals 4

    .line 226
    sget-boolean v0, Lcom/bytedance/shadowhook/ShadowHook;->inited:Z

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    .line 227
    sget v0, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1

    .line 230
    :cond_1
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->loadLibrary()Z

    move-result v0

    if-nez v0, :cond_2

    .line 231
    return v2

    .line 235
    :cond_2
    :try_start_0
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->nativeGetInitErrno()I

    move-result v0

    .line 236
    .local v0, "errno":I
    const/4 v3, 0x2

    if-eq v0, v3, :cond_3

    .line 237
    sput v0, Lcom/bytedance/shadowhook/ShadowHook;->initErrno:I

    .line 238
    sput-boolean v1, Lcom/bytedance/shadowhook/ShadowHook;->inited:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 240
    :cond_3
    if-nez v0, :cond_4

    goto :goto_1

    :cond_4
    const/4 v1, 0x0

    :goto_1
    return v1

    .line 241
    .end local v0    # "errno":I
    :catchall_0
    move-exception v0

    .line 242
    .local v0, "ignored":Ljava/lang/Throwable;
    return v2
.end method

.method private static loadLibrary()Z
    .locals 1

    .line 222
    const/4 v0, 0x0

    invoke-static {v0}, Lcom/bytedance/shadowhook/ShadowHook;->loadLibrary(Lcom/bytedance/shadowhook/ShadowHook$Config;)Z

    move-result v0

    return v0
.end method

.method private static loadLibrary(Lcom/bytedance/shadowhook/ShadowHook$Config;)Z
    .locals 2
    .param p0, "config"    # Lcom/bytedance/shadowhook/ShadowHook$Config;

    .line 210
    const-string v0, "shadowhook"

    if-eqz p0, :cond_1

    :try_start_0
    invoke-virtual {p0}, Lcom/bytedance/shadowhook/ShadowHook$Config;->getLibLoader()Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_0

    .line 213
    :cond_0
    invoke-virtual {p0}, Lcom/bytedance/shadowhook/ShadowHook$Config;->getLibLoader()Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    move-result-object v1

    invoke-interface {v1, v0}, Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;->loadLibrary(Ljava/lang/String;)V

    goto :goto_1

    .line 216
    :catchall_0
    move-exception v0

    goto :goto_2

    .line 211
    :cond_1
    :goto_0
    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 215
    :goto_1
    const/4 v0, 0x1

    return v0

    .line 217
    .local v0, "ignored":Ljava/lang/Throwable;
    :goto_2
    const/4 v1, 0x0

    return v1
.end method

.method private static native nativeGetArch()Ljava/lang/String;
.end method

.method private static native nativeGetDebuggable()Z
.end method

.method private static native nativeGetInitErrno()I
.end method

.method private static native nativeGetMode()I
.end method

.method private static native nativeGetRecordable()Z
.end method

.method private static native nativeGetRecords(I)Ljava/lang/String;
.end method

.method private static native nativeGetVersion()Ljava/lang/String;
.end method

.method private static native nativeInit(IZ)I
.end method

.method private static native nativeSetDebuggable(Z)V
.end method

.method private static native nativeSetRecordable(Z)V
.end method

.method private static native nativeToErrmsg(I)Ljava/lang/String;
.end method

.method public static setDebuggable(Z)V
    .locals 1
    .param p0, "debuggable"    # Z

    .line 115
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 116
    invoke-static {p0}, Lcom/bytedance/shadowhook/ShadowHook;->nativeSetDebuggable(Z)V

    .line 118
    :cond_0
    return-void
.end method

.method public static setRecordable(Z)V
    .locals 1
    .param p0, "recordable"    # Z

    .line 128
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 129
    invoke-static {p0}, Lcom/bytedance/shadowhook/ShadowHook;->nativeSetRecordable(Z)V

    .line 131
    :cond_0
    return-void
.end method

.method public static toErrmsg(I)Ljava/lang/String;
    .locals 1
    .param p0, "errno"    # I

    .line 134
    if-nez p0, :cond_0

    .line 135
    const-string v0, "OK"

    return-object v0

    .line 136
    :cond_0
    const/4 v0, 0x1

    if-ne p0, v0, :cond_1

    .line 137
    const-string v0, "Pending task"

    return-object v0

    .line 138
    :cond_1
    const/4 v0, 0x2

    if-ne p0, v0, :cond_2

    .line 139
    const-string v0, "Not initialized"

    return-object v0

    .line 140
    :cond_2
    const/16 v0, 0x64

    if-ne p0, v0, :cond_3

    .line 141
    const-string v0, "Load libshadowhook.so failed"

    return-object v0

    .line 142
    :cond_3
    const/16 v0, 0x65

    if-ne p0, v0, :cond_4

    .line 143
    const-string v0, "Init exception"

    return-object v0

    .line 145
    :cond_4
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook;->isInitedOk()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 146
    invoke-static {p0}, Lcom/bytedance/shadowhook/ShadowHook;->nativeToErrmsg(I)Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 148
    :cond_5
    const-string v0, "Unknown"

    return-object v0
.end method
