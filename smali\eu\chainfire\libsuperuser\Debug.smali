.class public Leu/chainfire/libsuperuser/Debug;
.super Ljava/lang/Object;
.source "Debug.java"


# annotations
.annotation build Landroidx/annotation/AnyThread;
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Leu/chainfire/libsuperuser/Debug$OnLogListener;
    }
.end annotation


# static fields
.field public static final LOG_ALL:I = 0xffff

.field public static final LOG_COMMAND:I = 0x2

.field public static final LOG_GENERAL:I = 0x1

.field public static final LOG_NONE:I = 0x0

.field public static final LOG_OUTPUT:I = 0x4

.field public static final LOG_POOL:I = 0x8

.field public static final TAG:Ljava/lang/String; = "libsuperuser"

.field private static debug:Z

.field private static logListener:Leu/chainfire/libsuperuser/Debug$OnLogListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private static logTypes:I

.field private static sanityChecks:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .prologue
    .line 36
    const/4 v0, 0x0

    sput-boolean v0, Leu/chainfire/libsuperuser/Debug;->debug:Z

    .line 76
    const v0, 0xffff

    sput v0, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    .line 79
    const/4 v0, 0x0

    sput-object v0, Leu/chainfire/libsuperuser/Debug;->logListener:Leu/chainfire/libsuperuser/Debug$OnLogListener;

    .line 215
    const/4 v0, 0x1

    sput-boolean v0, Leu/chainfire/libsuperuser/Debug;->sanityChecks:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .prologue
    .line 32
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getDebug()Z
    .locals 1

    .prologue
    .line 57
    sget-boolean v0, Leu/chainfire/libsuperuser/Debug;->debug:Z

    return v0
.end method

.method public static getLogTypeEnabled(I)Z
    .locals 3

    .prologue
    .line 173
    move v0, p0

    sget v1, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    move v2, v0

    and-int/2addr v1, v2

    move v2, v0

    if-ne v1, v2, :cond_0

    const/4 v1, 0x1

    :goto_0
    move v0, v1

    return v0

    :cond_0
    const/4 v1, 0x0

    goto :goto_0
.end method

.method public static getLogTypeEnabledEffective(I)Z
    .locals 2

    .prologue
    .line 187
    move v0, p0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getDebug()Z

    move-result v1

    if-eqz v1, :cond_0

    move v1, v0

    invoke-static {v1}, Leu/chainfire/libsuperuser/Debug;->getLogTypeEnabled(I)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    :goto_0
    move v0, v1

    return v0

    :cond_0
    const/4 v1, 0x0

    goto :goto_0
.end method

.method public static getOnLogListener()Leu/chainfire/libsuperuser/Debug$OnLogListener;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .prologue
    .line 210
    sget-object v0, Leu/chainfire/libsuperuser/Debug;->logListener:Leu/chainfire/libsuperuser/Debug$OnLogListener;

    return-object v0
.end method

.method public static getSanityChecksEnabled()Z
    .locals 1

    .prologue
    .line 238
    sget-boolean v0, Leu/chainfire/libsuperuser/Debug;->sanityChecks:Z

    return v0
.end method

.method public static getSanityChecksEnabledEffective()Z
    .locals 1

    .prologue
    .line 249
    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getDebug()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabled()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static log(Ljava/lang/String;)V
    .locals 4
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 109
    move-object v0, p0

    const/4 v1, 0x1

    const-string v2, "G"

    move-object v3, v0

    invoke-static {v1, v2, v3}, Leu/chainfire/libsuperuser/Debug;->logCommon(ILjava/lang/String;Ljava/lang/String;)V

    .line 110
    return-void
.end method

.method public static logCommand(Ljava/lang/String;)V
    .locals 4
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 120
    move-object v0, p0

    const/4 v1, 0x2

    const-string v2, "C"

    move-object v3, v0

    invoke-static {v1, v2, v3}, Leu/chainfire/libsuperuser/Debug;->logCommon(ILjava/lang/String;Ljava/lang/String;)V

    .line 121
    return-void
.end method

.method private static logCommon(ILjava/lang/String;Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 92
    move v0, p0

    move-object v1, p1

    move-object v2, p2

    sget-boolean v3, Leu/chainfire/libsuperuser/Debug;->debug:Z

    if-eqz v3, :cond_0

    sget v3, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    move v4, v0

    and-int/2addr v3, v4

    move v4, v0

    if-ne v3, v4, :cond_0

    .line 93
    sget-object v3, Leu/chainfire/libsuperuser/Debug;->logListener:Leu/chainfire/libsuperuser/Debug$OnLogListener;

    if-eqz v3, :cond_1

    .line 94
    sget-object v3, Leu/chainfire/libsuperuser/Debug;->logListener:Leu/chainfire/libsuperuser/Debug$OnLogListener;

    move v4, v0

    move-object v5, v1

    move-object v6, v2

    invoke-interface {v3, v4, v5, v6}, Leu/chainfire/libsuperuser/Debug$OnLogListener;->onLog(ILjava/lang/String;Ljava/lang/String;)V

    .line 99
    :cond_0
    :goto_0
    return-void

    .line 96
    :cond_1
    const-string v3, "libsuperuser"

    new-instance v4, Ljava/lang/StringBuilder;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "[libsuperuser]["

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    move-object v5, v1

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, "]"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    move-object v5, v2

    const-string v6, "["

    invoke-virtual {v5, v6}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v5

    if-nez v5, :cond_2

    move-object v5, v2

    const-string v6, " "

    invoke-virtual {v5, v6}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v5

    if-nez v5, :cond_2

    const-string v5, " "

    :goto_1
    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    move-object v5, v2

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v3, v4}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    move-result v3

    goto :goto_0

    :cond_2
    const-string v5, ""

    goto :goto_1
.end method

.method public static logOutput(Ljava/lang/String;)V
    .locals 4
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 131
    move-object v0, p0

    const/4 v1, 0x4

    const-string v2, "O"

    move-object v3, v0

    invoke-static {v1, v2, v3}, Leu/chainfire/libsuperuser/Debug;->logCommon(ILjava/lang/String;Ljava/lang/String;)V

    .line 132
    return-void
.end method

.method public static logPool(Ljava/lang/String;)V
    .locals 4
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 140
    move-object v0, p0

    const/16 v1, 0x8

    const-string v2, "P"

    move-object v3, v0

    invoke-static {v1, v2, v3}, Leu/chainfire/libsuperuser/Debug;->logCommon(ILjava/lang/String;Ljava/lang/String;)V

    .line 141
    return-void
.end method

.method public static onMainThread()Z
    .locals 2

    .prologue
    .line 258
    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v0

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    if-ne v0, v1, :cond_0

    invoke-static {}, Landroid/os/Process;->myUid()I

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    :goto_0
    return v0

    :cond_0
    const/4 v0, 0x0

    goto :goto_0
.end method

.method public static setDebug(Z)V
    .locals 2

    .prologue
    .line 48
    move v0, p0

    move v1, v0

    sput-boolean v1, Leu/chainfire/libsuperuser/Debug;->debug:Z

    .line 49
    return-void
.end method

.method public static setLogTypeEnabled(IZ)V
    .locals 5

    .prologue
    .line 154
    move v0, p0

    move v1, p1

    move v2, v1

    if-eqz v2, :cond_0

    .line 155
    sget v2, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    move v3, v0

    or-int/2addr v2, v3

    sput v2, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    .line 159
    :goto_0
    return-void

    .line 157
    :cond_0
    sget v2, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    move v3, v0

    const/4 v4, -0x1

    xor-int/lit8 v3, v3, -0x1

    and-int/2addr v2, v3

    sput v2, Leu/chainfire/libsuperuser/Debug;->logTypes:I

    goto :goto_0
.end method

.method public static setOnLogListener(Leu/chainfire/libsuperuser/Debug$OnLogListener;)V
    .locals 2
    .param p0    # Leu/chainfire/libsuperuser/Debug$OnLogListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .prologue
    .line 200
    move-object v0, p0

    move-object v1, v0

    sput-object v1, Leu/chainfire/libsuperuser/Debug;->logListener:Leu/chainfire/libsuperuser/Debug$OnLogListener;

    .line 201
    return-void
.end method

.method public static setSanityChecksEnabled(Z)V
    .locals 2

    .prologue
    .line 226
    move v0, p0

    move v1, v0

    sput-boolean v1, Leu/chainfire/libsuperuser/Debug;->sanityChecks:Z

    .line 227
    return-void
.end method
