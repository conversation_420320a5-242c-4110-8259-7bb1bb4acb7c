.class public final Lcom/topjohnwu/superuser/internal/MainShell;
.super Ljava/lang/Object;


# static fields
.field private static defaultBuilder:Lcom/topjohnwu/superuser/internal/BuilderImpl;

.field private static isInitMain:Z

.field private static mainShell:Lcom/topjohnwu/superuser/internal/ShellImpl;


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static get()Lcom/topjohnwu/superuser/internal/ShellImpl;
    .locals 3

    const-class v0, Lcom/topjohnwu/superuser/internal/MainShell;

    monitor-enter v0

    :try_start_0
    invoke-static {}, Lcom/topjohnwu/superuser/internal/MainShell;->getCached()Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v1

    if-nez v1, :cond_0

    const/4 v1, 0x1

    sput-boolean v1, Lcom/topjohnwu/superuser/internal/MainShell;->isInitMain:Z

    invoke-static {}, Lcom/topjohnwu/superuser/internal/MainShell;->getBuilder()Lcom/topjohnwu/superuser/internal/BuilderImpl;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build()Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v1

    const/4 v2, 0x0

    sput-boolean v2, Lcom/topjohnwu/superuser/internal/MainShell;->isInitMain:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    monitor-exit v0

    throw v1
.end method

.method public static get(Ljava/util/concurrent/Executor;Lcom/topjohnwu/superuser/Shell$GetShellCallback;)V
    .locals 2

    invoke-static {}, Lcom/topjohnwu/superuser/internal/MainShell;->getCached()Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v0

    if-eqz v0, :cond_1

    if-nez p0, :cond_0

    invoke-interface {p1, v0}, Lcom/topjohnwu/superuser/Shell$GetShellCallback;->onShell(Lcom/topjohnwu/superuser/Shell;)V

    goto :goto_0

    :cond_0
    new-instance v1, Lcom/topjohnwu/superuser/internal/MainShell$$ExternalSyntheticLambda0;

    invoke-direct {v1, p1, v0}, Lcom/topjohnwu/superuser/internal/MainShell$$ExternalSyntheticLambda0;-><init>(Lcom/topjohnwu/superuser/Shell$GetShellCallback;Lcom/topjohnwu/superuser/Shell;)V

    invoke-interface {p0, v1}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    goto :goto_0

    :cond_1
    sget-object v0, Lcom/topjohnwu/superuser/Shell;->EXECUTOR:Ljava/util/concurrent/ExecutorService;

    new-instance v1, Lcom/topjohnwu/superuser/internal/MainShell$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0, p1}, Lcom/topjohnwu/superuser/internal/MainShell$$ExternalSyntheticLambda2;-><init>(Ljava/util/concurrent/Executor;Lcom/topjohnwu/superuser/Shell$GetShellCallback;)V

    invoke-interface {v0, v1}, Ljava/util/concurrent/ExecutorService;->execute(Ljava/lang/Runnable;)V

    :goto_0
    return-void
.end method

.method private static getBuilder()Lcom/topjohnwu/superuser/internal/BuilderImpl;
    .locals 1

    sget-object v0, Lcom/topjohnwu/superuser/internal/MainShell;->defaultBuilder:Lcom/topjohnwu/superuser/internal/BuilderImpl;

    if-nez v0, :cond_0

    new-instance v0, Lcom/topjohnwu/superuser/internal/BuilderImpl;

    invoke-direct {v0}, Lcom/topjohnwu/superuser/internal/BuilderImpl;-><init>()V

    sput-object v0, Lcom/topjohnwu/superuser/internal/MainShell;->defaultBuilder:Lcom/topjohnwu/superuser/internal/BuilderImpl;

    :cond_0
    sget-object v0, Lcom/topjohnwu/superuser/internal/MainShell;->defaultBuilder:Lcom/topjohnwu/superuser/internal/BuilderImpl;

    return-object v0
.end method

.method public static getCached()Lcom/topjohnwu/superuser/internal/ShellImpl;
    .locals 2

    const-class v0, Lcom/topjohnwu/superuser/internal/MainShell;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/topjohnwu/superuser/internal/MainShell;->mainShell:Lcom/topjohnwu/superuser/internal/ShellImpl;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/internal/ShellImpl;->getStatus()I

    move-result v1

    if-gez v1, :cond_0

    const/4 v1, 0x0

    sput-object v1, Lcom/topjohnwu/superuser/internal/MainShell;->mainShell:Lcom/topjohnwu/superuser/internal/ShellImpl;

    :cond_0
    sget-object v1, Lcom/topjohnwu/superuser/internal/MainShell;->mainShell:Lcom/topjohnwu/superuser/internal/ShellImpl;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    monitor-exit v0

    throw v1
.end method

.method static synthetic lambda$get$0(Lcom/topjohnwu/superuser/Shell$GetShellCallback;Lcom/topjohnwu/superuser/Shell;)V
    .locals 0

    invoke-interface {p0, p1}, Lcom/topjohnwu/superuser/Shell$GetShellCallback;->onShell(Lcom/topjohnwu/superuser/Shell;)V

    return-void
.end method

.method static synthetic lambda$get$1(Lcom/topjohnwu/superuser/Shell$GetShellCallback;Lcom/topjohnwu/superuser/Shell;)V
    .locals 0

    invoke-interface {p0, p1}, Lcom/topjohnwu/superuser/Shell$GetShellCallback;->onShell(Lcom/topjohnwu/superuser/Shell;)V

    return-void
.end method

.method static synthetic lambda$get$2(Ljava/util/concurrent/Executor;Lcom/topjohnwu/superuser/Shell$GetShellCallback;)V
    .locals 3

    const-class v0, Lcom/topjohnwu/superuser/internal/MainShell;

    :try_start_0
    monitor-enter v0
    :try_end_0
    .catch Lcom/topjohnwu/superuser/NoShellException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x1

    :try_start_1
    sput-boolean v1, Lcom/topjohnwu/superuser/internal/MainShell;->isInitMain:Z

    invoke-static {}, Lcom/topjohnwu/superuser/internal/MainShell;->getBuilder()Lcom/topjohnwu/superuser/internal/BuilderImpl;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build()Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v1

    const/4 v2, 0x0

    sput-boolean v2, Lcom/topjohnwu/superuser/internal/MainShell;->isInitMain:Z

    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez p0, :cond_0

    invoke-interface {p1, v1}, Lcom/topjohnwu/superuser/Shell$GetShellCallback;->onShell(Lcom/topjohnwu/superuser/Shell;)V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/topjohnwu/superuser/internal/MainShell$$ExternalSyntheticLambda1;

    invoke-direct {v0, p1, v1}, Lcom/topjohnwu/superuser/internal/MainShell$$ExternalSyntheticLambda1;-><init>(Lcom/topjohnwu/superuser/Shell$GetShellCallback;Lcom/topjohnwu/superuser/Shell;)V

    invoke-interface {p0, v0}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    :goto_0
    return-void

    :catchall_0
    move-exception p0

    :try_start_2
    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    throw p0
    :try_end_3
    .catch Lcom/topjohnwu/superuser/NoShellException; {:try_start_3 .. :try_end_3} :catch_0

    :catch_0
    move-exception p0

    invoke-static {p0}, Lcom/topjohnwu/superuser/internal/Utils;->ex(Ljava/lang/Throwable;)V

    return-void
.end method

.method public static newJob(ZLjava/io/InputStream;)Lcom/topjohnwu/superuser/Shell$Job;
    .locals 1

    new-instance v0, Lcom/topjohnwu/superuser/internal/PendingJob;

    invoke-direct {v0, p0}, Lcom/topjohnwu/superuser/internal/PendingJob;-><init>(Z)V

    invoke-virtual {v0, p1}, Lcom/topjohnwu/superuser/internal/PendingJob;->add(Ljava/io/InputStream;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object p0

    return-object p0
.end method

.method public static varargs newJob(Z[Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;
    .locals 1

    new-instance v0, Lcom/topjohnwu/superuser/internal/PendingJob;

    invoke-direct {v0, p0}, Lcom/topjohnwu/superuser/internal/PendingJob;-><init>(Z)V

    invoke-virtual {v0, p1}, Lcom/topjohnwu/superuser/internal/PendingJob;->add([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object p0

    return-object p0
.end method

.method static set(Lcom/topjohnwu/superuser/internal/ShellImpl;)V
    .locals 2

    const-class v0, Lcom/topjohnwu/superuser/internal/MainShell;

    monitor-enter v0

    :try_start_0
    sget-boolean v1, Lcom/topjohnwu/superuser/internal/MainShell;->isInitMain:Z

    if-eqz v1, :cond_0

    sput-object p0, Lcom/topjohnwu/superuser/internal/MainShell;->mainShell:Lcom/topjohnwu/superuser/internal/ShellImpl;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method public static setBuilder(Lcom/topjohnwu/superuser/Shell$Builder;)V
    .locals 1

    const-class v0, Lcom/topjohnwu/superuser/internal/MainShell;

    monitor-enter v0

    :try_start_0
    check-cast p0, Lcom/topjohnwu/superuser/internal/BuilderImpl;

    sput-object p0, Lcom/topjohnwu/superuser/internal/MainShell;->defaultBuilder:Lcom/topjohnwu/superuser/internal/BuilderImpl;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-void

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method
