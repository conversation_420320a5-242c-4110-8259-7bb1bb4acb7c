.class Lpmm/by/p2077kng/hacker/Loader$100000022;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000022"
.end annotation


# instance fields
.field private isActive:Z

.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;

.field private final val$button:Landroid/widget/TextView;

.field private final val$feature2:Ljava/lang/String;

.field private final val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;Landroid/widget/TextView;Ljava/lang/String;)V
    .locals 8

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v6, v0

    invoke-direct {v6}, Ljava/lang/Object;-><init>()V

    move-object v6, v0

    move-object v7, v1

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000022;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v6, v0

    move-object v7, v2

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;

    move-object v6, v0

    move-object v7, v3

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    move-object v6, v0

    move-object v7, v4

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$feature2:Ljava/lang/String;

    move-object v6, v0

    const/4 v7, 0x1

    iput-boolean v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000022;->isActive:Z

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000022;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000022;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1132
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;

    invoke-interface {v7}, Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;->OnWrite()V

    .line 1133
    move-object v7, v0

    iget-boolean v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->isActive:Z

    if-eqz v7, :cond_1

    .line 1134
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    new-instance v8, Ljava/lang/StringBuffer;

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    move-object v9, v0

    iget-object v9, v9, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$feature2:Ljava/lang/String;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, ""

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1135
    move-object v7, v0

    const/4 v8, 0x0

    iput-boolean v8, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->isActive:Z

    .line 1136
    new-instance v7, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    invoke-direct {v8}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v3, v7

    .line 1137
    move-object v7, v3

    const-string v8, "#555555"

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1138
    move-object v7, v3

    const/4 v8, 0x4

    int-to-float v8, v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1139
    new-instance v7, Landroid/graphics/drawable/RippleDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    new-instance v9, Landroid/content/res/ColorStateList;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    const/4 v11, 0x1

    new-array v11, v11, [[I

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    const/4 v13, 0x0

    const/4 v14, 0x0

    new-array v14, v14, [I

    aput-object v14, v12, v13

    const/4 v12, 0x1

    new-array v12, v12, [I

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x0

    const-string v15, "#696969"

    invoke-static {v15}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v15

    aput v15, v13, v14

    invoke-direct {v10, v11, v12}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    move-object v10, v3

    const/4 v11, 0x0

    check-cast v11, Landroid/graphics/drawable/Drawable;

    invoke-direct {v8, v9, v10, v11}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v4, v7

    .line 1140
    sget v7, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v8, 0x15

    if-lt v7, v8, :cond_0

    .line 1141
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    const/high16 v8, 0x42b40000    # 90.0f

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setElevation(F)V

    .line 1143
    :cond_0
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    move-object v8, v4

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1157
    :goto_0
    return-void

    .line 1146
    :cond_1
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    new-instance v8, Ljava/lang/StringBuffer;

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    move-object v9, v0

    iget-object v9, v9, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$feature2:Ljava/lang/String;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, ""

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1147
    move-object v7, v0

    const/4 v8, 0x1

    iput-boolean v8, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->isActive:Z

    .line 1148
    new-instance v7, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    invoke-direct {v8}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v3, v7

    .line 1149
    move-object v7, v3

    const-string v8, "#555555"

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1150
    move-object v7, v3

    const/4 v8, 0x4

    int-to-float v8, v8

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1151
    sget v7, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v8, 0x15

    if-lt v7, v8, :cond_2

    .line 1152
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    const/high16 v8, 0x42b40000    # 90.0f

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setElevation(F)V

    .line 1154
    :cond_2
    move-object v7, v3

    move-object v4, v7

    .line 1155
    new-instance v7, Landroid/graphics/drawable/RippleDrawable;

    move-object/from16 v16, v7

    move-object/from16 v7, v16

    move-object/from16 v8, v16

    new-instance v9, Landroid/content/res/ColorStateList;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    const/4 v11, 0x1

    new-array v11, v11, [[I

    move-object/from16 v16, v11

    move-object/from16 v11, v16

    move-object/from16 v12, v16

    const/4 v13, 0x0

    const/4 v14, 0x0

    new-array v14, v14, [I

    aput-object v14, v12, v13

    const/4 v12, 0x1

    new-array v12, v12, [I

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    const/4 v14, 0x0

    const-string v15, "#696969"

    invoke-static {v15}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v15

    aput v15, v13, v14

    invoke-direct {v10, v11, v12}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    move-object v10, v3

    const/4 v11, 0x0

    check-cast v11, Landroid/graphics/drawable/Drawable;

    invoke-direct {v8, v9, v10, v11}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v5, v7

    .line 1156
    move-object v7, v4

    sget-object v8, Landroid/graphics/drawable/GradientDrawable$Orientation;->TL_BR:Landroid/graphics/drawable/GradientDrawable$Orientation;

    invoke-virtual {v7, v8}, Landroid/graphics/drawable/GradientDrawable;->setOrientation(Landroid/graphics/drawable/GradientDrawable$Orientation;)V

    .line 1157
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000022;->val$button:Landroid/widget/TextView;

    move-object v8, v5

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    goto/16 :goto_0
.end method
