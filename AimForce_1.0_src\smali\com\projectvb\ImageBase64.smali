.class public Lcom/projectvb/ImageBase64;
.super Landroid/widget/ImageView;
.source "ImageBase64.java"


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0
    .param p1, "context"    # Landroid/content/Context;

    .line 12
    invoke-direct {p0, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 13
    return-void
.end method


# virtual methods
.method public setImageBase64(Ljava/lang/String;)V
    .locals 3
    .param p1, "image"    # Ljava/lang/String;

    .line 16
    const/4 v0, 0x0

    invoke-static {p1, v0}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v1

    .line 17
    .local v1, "decodeImageBase64":[B
    array-length v2, v1

    invoke-static {v1, v0, v2}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v0

    .line 18
    .local v0, "bitmap":Landroid/graphics/Bitmap;
    invoke-virtual {p0, v0}, Lcom/projectvb/ImageBase64;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 19
    return-void
.end method
