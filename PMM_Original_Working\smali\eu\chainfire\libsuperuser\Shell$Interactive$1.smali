.class Leu/chainfire/libsuperuser/Shell$Interactive$1;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;-><init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

.field final synthetic val$builder:Leu/chainfire/libsuperuser/Shell$Builder;

.field final synthetic val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V
    .locals 6

    .prologue
    .line 1621
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive$1;->val$builder:Leu/chainfire/libsuperuser/Shell$Builder;

    move-object v4, v0

    move-object v5, v3

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive$1;->val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCommandResult(IILjava/util/List;Ljava/util/List;)V
    .locals 12
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .prologue
    .line 1628
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object/from16 v4, p4

    move v6, v2

    if-nez v6, :cond_0

    move-object v6, v3

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    .line 1629
    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$1200(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$SU;->isSU(Ljava/lang/String;)Z

    move-result v7

    invoke-static {v6, v7}, Leu/chainfire/libsuperuser/Shell;->parseAvailableResult(Ljava/util/List;Z)Z

    move-result v6

    if-nez v6, :cond_0

    .line 1631
    const/4 v6, -0x4

    move v2, v6

    .line 1635
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    const/4 v7, 0x1

    invoke-static {v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$1302(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z

    move-result v6

    .line 1636
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately()V

    .line 1640
    :cond_0
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$1;->val$builder:Leu/chainfire/libsuperuser/Shell$Builder;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Builder;->access$900(Leu/chainfire/libsuperuser/Shell$Builder;)I

    move-result v7

    invoke-static {v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$1402(Leu/chainfire/libsuperuser/Shell$Interactive;I)I

    move-result v6

    .line 1643
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;

    if-eqz v6, :cond_1

    .line 1644
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    if-eqz v6, :cond_2

    .line 1645
    move v6, v2

    move v5, v6

    .line 1646
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->startCallback()V

    .line 1647
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    new-instance v7, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v0

    move v10, v5

    invoke-direct {v8, v9, v10}, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive$1;I)V

    invoke-virtual {v6, v7}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v6

    .line 1661
    :cond_1
    :goto_0
    return-void

    .line 1658
    :cond_2
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$1;->val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;

    move v7, v2

    if-nez v7, :cond_3

    const/4 v7, 0x1

    :goto_1
    move v8, v2

    invoke-interface {v6, v7, v8}, Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;->onOpenResult(ZI)V

    goto :goto_0

    :cond_3
    const/4 v7, 0x0

    goto :goto_1
.end method
