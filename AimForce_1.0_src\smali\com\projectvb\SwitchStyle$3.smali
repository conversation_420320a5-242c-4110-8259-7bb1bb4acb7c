.class Lcom/projectvb/SwitchStyle$3;
.super Ljava/lang/Object;
.source "SwitchStyle.java"

# interfaces
.implements Landroid/animation/Animator$AnimatorListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/SwitchStyle;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/SwitchStyle;


# direct methods
.method constructor <init>(Lcom/projectvb/SwitchStyle;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/SwitchStyle;

    .line 1069
    iput-object p1, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onAnimationCancel(Landroid/animation/Animator;)V
    .locals 0
    .param p1, "animation"    # Landroid/animation/Animator;

    .line 1115
    return-void
.end method

.method public onAnimationEnd(Landroid/animation/Animator;)V
    .locals 4
    .param p1, "animation"    # Landroid/animation/Animator;

    .line 1076
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$300(Lcom/projectvb/SwitchStyle;)I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_3

    const/4 v3, 0x3

    if-eq v0, v3, :cond_2

    const/4 v3, 0x4

    if-eq v0, v3, :cond_1

    const/4 v3, 0x5

    if-eq v0, v3, :cond_0

    goto :goto_0

    .line 1100
    :cond_0
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$1500(Lcom/projectvb/SwitchStyle;)Z

    move-result v3

    xor-int/2addr v1, v3

    invoke-static {v0, v1}, Lcom/projectvb/SwitchStyle;->access$1502(Lcom/projectvb/SwitchStyle;Z)Z

    .line 1101
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0, v2}, Lcom/projectvb/SwitchStyle;->access$302(Lcom/projectvb/SwitchStyle;I)I

    .line 1102
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-virtual {v0}, Lcom/projectvb/SwitchStyle;->postInvalidate()V

    .line 1103
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$1400(Lcom/projectvb/SwitchStyle;)V

    .line 1104
    goto :goto_0

    .line 1094
    :cond_1
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0, v2}, Lcom/projectvb/SwitchStyle;->access$302(Lcom/projectvb/SwitchStyle;I)I

    .line 1095
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-virtual {v0}, Lcom/projectvb/SwitchStyle;->postInvalidate()V

    .line 1096
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$1400(Lcom/projectvb/SwitchStyle;)V

    .line 1097
    goto :goto_0

    .line 1089
    :cond_2
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0, v2}, Lcom/projectvb/SwitchStyle;->access$302(Lcom/projectvb/SwitchStyle;I)I

    .line 1090
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-virtual {v0}, Lcom/projectvb/SwitchStyle;->postInvalidate()V

    .line 1091
    goto :goto_0

    .line 1081
    :cond_3
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    const/4 v1, 0x2

    invoke-static {v0, v1}, Lcom/projectvb/SwitchStyle;->access$302(Lcom/projectvb/SwitchStyle;I)I

    .line 1082
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v0

    iput v2, v0, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    .line 1083
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v0

    iget-object v1, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$1200(Lcom/projectvb/SwitchStyle;)F

    move-result v1

    iput v1, v0, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    .line 1085
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$3;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-virtual {v0}, Lcom/projectvb/SwitchStyle;->postInvalidate()V

    .line 1086
    nop

    .line 1111
    :goto_0
    return-void
.end method

.method public onAnimationRepeat(Landroid/animation/Animator;)V
    .locals 0
    .param p1, "animation"    # Landroid/animation/Animator;

    .line 1119
    return-void
.end method

.method public onAnimationStart(Landroid/animation/Animator;)V
    .locals 0
    .param p1, "animation"    # Landroid/animation/Animator;

    .line 1072
    return-void
.end method
