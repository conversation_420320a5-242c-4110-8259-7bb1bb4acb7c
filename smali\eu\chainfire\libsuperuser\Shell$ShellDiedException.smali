.class public Leu/chainfire/libsuperuser/Shell$ShellDiedException;
.super Ljava/lang/Exception;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ShellDiedException"
.end annotation


# static fields
.field public static final EXCEPTION_SHELL_DIED:Ljava/lang/String; = "Shell died (or access was not granted)"


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 93
    move-object v0, p0

    move-object v1, v0

    const-string v2, "Shell died (or access was not granted)"

    invoke-direct {v1, v2}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    .line 94
    return-void
.end method
