.class public Leu/chainfire/libsuperuser/Shell$Interactive;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$SyncCommands;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Interactive"
.end annotation


# instance fields
.field private STDERR:Leu/chainfire/libsuperuser/StreamGobbler;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private STDERRclosed:Z

.field private STDIN:Ljava/io/DataOutputStream;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private STDOUTclosed:Z

.field private final STDclosedSync:Ljava/lang/Object;

.field private final autoHandler:Z

.field private volatile bufferSTDERR:Ljava/util/List;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private volatile bufferSTDOUT:Ljava/util/List;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field protected final callbackSync:Ljava/lang/Object;

.field protected volatile callbacks:I

.field protected volatile closed:Z

.field private volatile command:Leu/chainfire/libsuperuser/Shell$Command;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final commands:Ljava/util/List;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Leu/chainfire/libsuperuser/Shell$Command;",
            ">;"
        }
    .end annotation
.end field

.field private volatile doCloseWhenIdle:Z

.field private final emptyStringList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final environment:Ljava/util/Map;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map",
            "<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field protected final handler:Landroid/os/Handler;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private volatile idle:Z

.field private final idleSync:Ljava/lang/Object;

.field private volatile lastExitCode:I

.field private volatile lastMarkerSTDERR:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private volatile lastMarkerSTDOUT:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private volatile lastOpening:Z

.field private final onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private volatile opening:Z

.field private final openingSync:Ljava/lang/Object;

.field private process:Ljava/lang/Process;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private volatile running:Z

.field private final shell:Ljava/lang/String;

.field private shellDiesOnSTDOUTERRClose:Z

.field private final wantSTDERR:Z

.field private watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private volatile watchdogCount:I

.field private watchdogTimeout:I


# direct methods
.method protected constructor <init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V
    .locals 15
    .param p1    # Leu/chainfire/libsuperuser/Shell$Builder;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 1594
    move-object v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    .line 1543
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    .line 1545
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    .line 1547
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    .line 1549
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    .line 1551
    move-object v3, v0

    new-instance v4, Ljava/lang/Object;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDclosedSync:Ljava/lang/Object;

    .line 1552
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUTclosed:Z

    .line 1553
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERRclosed:Z

    .line 1554
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    .line 1557
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->running:Z

    .line 1558
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    .line 1559
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 1560
    move-object v3, v0

    const/4 v4, 0x1

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 1561
    move-object v3, v0

    const/4 v4, 0x1

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    .line 1562
    move-object v3, v0

    const/4 v4, 0x0

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I

    .line 1564
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->doCloseWhenIdle:Z

    .line 1566
    move-object v3, v0

    new-instance v4, Ljava/lang/Object;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    .line 1567
    move-object v3, v0

    new-instance v4, Ljava/lang/Object;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbackSync:Ljava/lang/Object;

    .line 1568
    move-object v3, v0

    new-instance v4, Ljava/lang/Object;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    .line 1569
    move-object v3, v0

    new-instance v4, Ljava/util/ArrayList;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->emptyStringList:Ljava/util/List;

    .line 1571
    move-object v3, v0

    const/4 v4, 0x0

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastExitCode:I

    .line 1572
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDOUT:Ljava/lang/String;

    .line 1574
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDERR:Ljava/lang/String;

    .line 1576
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    .line 1578
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    .line 1580
    move-object v3, v0

    const/4 v4, 0x0

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    .line 1595
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$100(Leu/chainfire/libsuperuser/Shell$Builder;)Z

    move-result v4

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->autoHandler:Z

    .line 1596
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$200(Leu/chainfire/libsuperuser/Shell$Builder;)Ljava/lang/String;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    .line 1597
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$300(Leu/chainfire/libsuperuser/Shell$Builder;)Z

    move-result v4

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->shellDiesOnSTDOUTERRClose:Z

    .line 1598
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$400(Leu/chainfire/libsuperuser/Shell$Builder;)Z

    move-result v4

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->wantSTDERR:Z

    .line 1599
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$500(Leu/chainfire/libsuperuser/Shell$Builder;)Ljava/util/List;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    .line 1600
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$600(Leu/chainfire/libsuperuser/Shell$Builder;)Ljava/util/Map;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->environment:Ljava/util/Map;

    .line 1601
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$700(Leu/chainfire/libsuperuser/Shell$Builder;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 1602
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$800(Leu/chainfire/libsuperuser/Shell$Builder;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 1603
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$900(Leu/chainfire/libsuperuser/Shell$Builder;)I

    move-result v4

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogTimeout:I

    .line 1608
    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v3

    if-eqz v3, :cond_3

    move-object v3, v1

    invoke-static {v3}, Leu/chainfire/libsuperuser/Shell$Builder;->access$1000(Leu/chainfire/libsuperuser/Shell$Builder;)Landroid/os/Handler;

    move-result-object v3

    if-nez v3, :cond_3

    move-object v3, v0

    iget-boolean v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->autoHandler:Z

    if-eqz v3, :cond_3

    .line 1609
    move-object v3, v0

    new-instance v4, Landroid/os/Handler;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    invoke-direct {v5}, Landroid/os/Handler;-><init>()V

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    .line 1614
    :goto_0
    move-object v3, v2

    if-nez v3, :cond_0

    move-object v3, v1

    invoke-static {v3}, Leu/chainfire/libsuperuser/Shell$Builder;->access$1100(Leu/chainfire/libsuperuser/Shell$Builder;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 1615
    :cond_0
    move-object v3, v0

    const/4 v4, 0x1

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    .line 1616
    move-object v3, v0

    const/4 v4, 0x1

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 1620
    move-object v3, v0

    const/16 v4, 0x3c

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogTimeout:I

    .line 1621
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    const/4 v4, 0x0

    new-instance v5, Leu/chainfire/libsuperuser/Shell$Command;

    move-object v14, v5

    move-object v5, v14

    move-object v6, v14

    sget-object v7, Leu/chainfire/libsuperuser/Shell;->availableTestCommands:[Ljava/lang/String;

    const/4 v8, 0x0

    new-instance v9, Leu/chainfire/libsuperuser/Shell$Interactive$1;

    move-object v14, v9

    move-object v9, v14

    move-object v10, v14

    move-object v11, v0

    move-object v12, v1

    move-object v13, v2

    invoke-direct {v10, v11, v12, v13}, Leu/chainfire/libsuperuser/Shell$Interactive$1;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V

    invoke-direct {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Command;-><init>(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    invoke-interface {v3, v4, v5}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 1665
    :cond_1
    move-object v3, v0

    invoke-direct {v3}, Leu/chainfire/libsuperuser/Shell$Interactive;->open()Z

    move-result v3

    if-nez v3, :cond_2

    move-object v3, v2

    if-eqz v3, :cond_2

    .line 1666
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    if-eqz v3, :cond_4

    .line 1667
    move-object v3, v0

    invoke-virtual {v3}, Leu/chainfire/libsuperuser/Shell$Interactive;->startCallback()V

    .line 1668
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    new-instance v4, Leu/chainfire/libsuperuser/Shell$Interactive$2;

    move-object v14, v4

    move-object v4, v14

    move-object v5, v14

    move-object v6, v0

    move-object v7, v2

    invoke-direct {v5, v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive$2;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V

    invoke-virtual {v3, v4}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v3

    .line 1682
    :cond_2
    :goto_1
    return-void

    .line 1611
    :cond_3
    move-object v3, v0

    move-object v4, v1

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Builder;->access$1000(Leu/chainfire/libsuperuser/Shell$Builder;)Landroid/os/Handler;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    goto :goto_0

    .line 1679
    :cond_4
    move-object v3, v2

    const/4 v4, 0x0

    const/4 v5, -0x3

    invoke-interface {v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;->onOpenResult(ZI)V

    goto :goto_1
.end method

.method static synthetic access$1200(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/lang/String;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$1302(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    move-object v5, v2

    move v6, v3

    move v2, v6

    move-object v3, v5

    move v4, v6

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    move v0, v2

    return v0
.end method

.method static synthetic access$1402(Leu/chainfire/libsuperuser/Shell$Interactive;I)I
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    move-object v5, v2

    move v6, v3

    move v2, v6

    move-object v3, v5

    move v4, v6

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogTimeout:I

    move v0, v2

    return v0
.end method

.method static synthetic access$1500(Leu/chainfire/libsuperuser/Shell$Interactive;)V
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->handleWatchdog()V

    return-void
.end method

.method static synthetic access$2400(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->emptyStringList:Ljava/util/List;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2500(Leu/chainfire/libsuperuser/Shell$Interactive;)Z
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->shellDiesOnSTDOUTERRClose:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$2600(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2800(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/lang/Object;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->STDclosedSync:Ljava/lang/Object;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$2900(Leu/chainfire/libsuperuser/Shell$Interactive;)Z
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUTclosed:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$2902(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    move-object v5, v2

    move v6, v3

    move v2, v6

    move-object v3, v5

    move v4, v6

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUTclosed:Z

    move v0, v2

    return v0
.end method

.method static synthetic access$3000(Leu/chainfire/libsuperuser/Shell$Interactive;)Z
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERRclosed:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$3002(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    move-object v5, v2

    move v6, v3

    move v2, v6

    move-object v3, v5

    move v4, v6

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERRclosed:Z

    move v0, v2

    return v0
.end method

.method static synthetic access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$3102(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$Command;
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    move-object v5, v2

    move-object v6, v3

    move-object v2, v6

    move-object v3, v5

    move-object v4, v6

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    move-object v0, v2

    return-object v0
.end method

.method static synthetic access$3200(Leu/chainfire/libsuperuser/Shell$Interactive;)Z
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForCallbacks()Z

    move-result v1

    move v0, v1

    return v0
.end method

.method static synthetic access$3300(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$3400(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$3500(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z
    .locals 12

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-object/from16 v4, p4

    move-object/from16 v5, p5

    move-object v6, v0

    move-object v7, v1

    move v8, v2

    move-object v9, v3

    move-object v10, v4

    move-object v11, v5

    invoke-direct/range {v6 .. v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z

    move-result v6

    move v0, v6

    return v0
.end method

.method static synthetic access$3602(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    move-object v5, v2

    move v6, v3

    move v2, v6

    move-object v3, v5

    move v4, v6

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    move v0, v2

    return v0
.end method

.method static synthetic access$3700(Leu/chainfire/libsuperuser/Shell$Interactive;)V
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->runNextCommand()V

    return-void
.end method

.method static synthetic access$3800(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Z)V
    .locals 6

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, v0

    move-object v4, v1

    move v5, v2

    invoke-direct {v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->addBuffer(Ljava/lang/String;Z)V

    return-void
.end method

.method static synthetic access$3900(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$4000(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Ljava/lang/Object;Z)V
    .locals 8

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v4, v0

    move-object v5, v1

    move-object v6, v2

    move v7, v3

    invoke-direct {v4, v5, v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->processLine(Ljava/lang/String;Ljava/lang/Object;Z)V

    return-void
.end method

.method static synthetic access$4102(Leu/chainfire/libsuperuser/Shell$Interactive;I)I
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    move-object v5, v2

    move v6, v3

    move v2, v6

    move-object v3, v5

    move v4, v6

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastExitCode:I

    move v0, v2

    return v0
.end method

.method static synthetic access$4202(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;)Ljava/lang/String;
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    move-object v5, v2

    move-object v6, v3

    move-object v2, v6

    move-object v3, v5

    move-object v4, v6

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDOUT:Ljava/lang/String;

    move-object v0, v2

    return-object v0
.end method

.method static synthetic access$4300(Leu/chainfire/libsuperuser/Shell$Interactive;)V
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->processMarker()V

    return-void
.end method

.method static synthetic access$4400(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .locals 2

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$4502(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;)Ljava/lang/String;
    .locals 7

    .prologue
    .line 1526
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    move-object v5, v2

    move-object v6, v3

    move-object v2, v6

    move-object v3, v5

    move-object v4, v6

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDERR:Ljava/lang/String;

    move-object v0, v2

    return-object v0
.end method

.method private declared-synchronized addBuffer(Ljava/lang/String;Z)V
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 1962
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v5, p0

    monitor-enter v5

    move v3, v2

    if-eqz v3, :cond_2

    .line 1963
    move-object v3, v0

    :try_start_0
    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    if-eqz v3, :cond_1

    .line 1964
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    move-object v4, v1

    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v3

    .line 1971
    :cond_0
    :goto_0
    monitor-exit v5

    return-void

    .line 1965
    :cond_1
    move-object v3, v0

    :try_start_1
    iget-boolean v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->wantSTDERR:Z

    if-eqz v3, :cond_0

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    if-eqz v3, :cond_0

    .line 1966
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    move-object v4, v1

    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-result v3

    goto :goto_0

    .line 1968
    :cond_2
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    if-eqz v3, :cond_0

    .line 1969
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    move-object v4, v1

    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    move-result v3

    goto :goto_0

    .line 1962
    :catchall_0
    move-exception v0

    monitor-exit v5

    throw v0
.end method

.method private declared-synchronized handleWatchdog()V
    .locals 12

    .prologue
    .line 1735
    move-object v0, p0

    move-object v9, p0

    monitor-enter v9

    move-object v2, v0

    :try_start_0
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v2, :cond_0

    .line 1765
    :goto_0
    monitor-exit v9

    return-void

    .line 1737
    :cond_0
    move-object v2, v0

    :try_start_1
    iget v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogTimeout:I

    if-nez v2, :cond_1

    .line 1738
    goto :goto_0

    .line 1740
    :cond_1
    move-object v2, v0

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v2

    if-nez v2, :cond_3

    .line 1741
    const/4 v2, -0x2

    move v1, v2

    .line 1742
    sget-object v2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v3, "[%s%%] SHELL_DIED"

    const/4 v4, 0x1

    new-array v4, v4, [Ljava/lang/Object;

    move-object v10, v4

    move-object v4, v10

    move-object v5, v10

    const/4 v6, 0x0

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v8, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v7, v8}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v7

    aput-object v7, v5, v6

    invoke-static {v2, v3, v4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 1750
    :goto_1
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    if-eqz v2, :cond_2

    .line 1752
    move-object v2, v0

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    move v4, v1

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    const/4 v7, 0x0

    invoke-direct/range {v2 .. v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z

    move-result v2

    .line 1756
    :cond_2
    move-object v2, v0

    const/4 v3, 0x0

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    .line 1757
    move-object v2, v0

    const/4 v3, 0x0

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    .line 1758
    move-object v2, v0

    const/4 v3, 0x0

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    .line 1759
    move-object v2, v0

    const/4 v3, 0x1

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 1760
    move-object v2, v0

    const/4 v3, 0x0

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 1762
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    invoke-virtual {v2}, Ljava/util/concurrent/ScheduledThreadPoolExecutor;->shutdown()V

    .line 1763
    move-object v2, v0

    const/4 v3, 0x0

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    .line 1764
    move-object v2, v0

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->kill()V

    .line 1765
    goto :goto_0

    .line 1743
    :cond_3
    move-object v2, v0

    move-object v10, v2

    move-object v2, v10

    move-object v3, v10

    iget v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogCount:I

    move-object v10, v2

    move v11, v3

    move v2, v11

    move-object v3, v10

    move v4, v11

    const/4 v5, 0x1

    add-int/lit8 v4, v4, 0x1

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogCount:I

    move-object v3, v0

    iget v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogTimeout:I

    if-ge v2, v3, :cond_4

    .line 1744
    goto :goto_0

    .line 1746
    :cond_4
    const/4 v2, -0x1

    move v1, v2

    .line 1747
    sget-object v2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v3, "[%s%%] WATCHDOG_EXIT"

    const/4 v4, 0x1

    new-array v4, v4, [Ljava/lang/Object;

    move-object v10, v4

    move-object v4, v10

    move-object v5, v10

    const/4 v6, 0x0

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v8, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v7, v8}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v7

    aput-object v7, v5, v6

    invoke-static {v2, v3, v4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 1735
    :catchall_0
    move-exception v0

    monitor-exit v9

    throw v0
.end method

.method private declared-synchronized open()Z
    .locals 16

    .prologue
    .line 2061
    move-object/from16 v0, p0

    move-object/from16 v14, p0

    monitor-enter v14

    :try_start_0
    sget-object v6, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v7, "[%s%%] START"

    const/4 v8, 0x1

    new-array v8, v8, [Ljava/lang/Object;

    move-object v15, v8

    move-object v8, v15

    move-object v9, v15

    const/4 v10, 0x0

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v12, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v11, v12}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v11

    aput-object v11, v9, v10

    invoke-static {v6, v7, v8}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2066
    move-object v6, v0

    :try_start_1
    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->environment:Ljava/util/Map;

    invoke-interface {v6}, Ljava/util/Map;->size()I

    move-result v6

    if-nez v6, :cond_0

    .line 2067
    move-object v6, v0

    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v7

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    invoke-virtual {v7, v8}, Ljava/lang/Runtime;->exec(Ljava/lang/String;)Ljava/lang/Process;

    move-result-object v7

    iput-object v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    .line 2082
    :goto_0
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    if-nez v6, :cond_2

    new-instance v6, Ljava/lang/NullPointerException;

    move-object v15, v6

    move-object v6, v15

    move-object v7, v15

    invoke-direct {v7}, Ljava/lang/NullPointerException;-><init>()V

    throw v6
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2221
    :catch_0
    move-exception v6

    move-object v1, v6

    .line 2223
    const/4 v6, 0x0

    move v0, v6

    :goto_1
    monitor-exit v14

    return v0

    .line 2069
    :cond_0
    :try_start_2
    new-instance v6, Ljava/util/HashMap;

    move-object v15, v6

    move-object v6, v15

    move-object v7, v15

    invoke-direct {v7}, Ljava/util/HashMap;-><init>()V

    move-object v1, v6

    .line 2070
    move-object v6, v1

    invoke-static {}, Ljava/lang/System;->getenv()Ljava/util/Map;

    move-result-object v7

    invoke-interface {v6, v7}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 2071
    move-object v6, v1

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive;->environment:Ljava/util/Map;

    invoke-interface {v6, v7}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 2072
    const/4 v6, 0x0

    move v2, v6

    .line 2073
    move-object v6, v1

    invoke-interface {v6}, Ljava/util/Map;->size()I

    move-result v6

    new-array v6, v6, [Ljava/lang/String;

    move-object v3, v6

    .line 2074
    move-object v6, v1

    invoke-interface {v6}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v6

    move-object v4, v6

    :goto_2
    move-object v6, v4

    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_1

    move-object v6, v4

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/util/Map$Entry;

    move-object v5, v6

    .line 2075
    move-object v6, v3

    move v7, v2

    new-instance v8, Ljava/lang/StringBuilder;

    move-object v15, v8

    move-object v8, v15

    move-object v9, v15

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    move-object v9, v5

    invoke-interface {v9}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/String;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    const-string v9, "="

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    move-object v9, v5

    invoke-interface {v9}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/String;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    aput-object v8, v6, v7

    .line 2076
    add-int/lit8 v2, v2, 0x1

    .line 2077
    goto :goto_2

    .line 2078
    :cond_1
    move-object v6, v0

    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v7

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    move-object v9, v3

    invoke-virtual {v7, v8, v9}, Ljava/lang/Runtime;->exec(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/Process;

    move-result-object v7

    iput-object v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto/16 :goto_0

    .line 2061
    :catchall_0
    move-exception v0

    monitor-exit v14

    throw v0

    .line 2084
    :cond_2
    :try_start_3
    new-instance v6, Leu/chainfire/libsuperuser/Shell$Interactive$6;

    move-object v15, v6

    move-object v6, v15

    move-object v7, v15

    move-object v8, v0

    invoke-direct {v7, v8}, Leu/chainfire/libsuperuser/Shell$Interactive$6;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    move-object v1, v6

    .line 2125
    move-object v6, v0

    new-instance v7, Ljava/io/DataOutputStream;

    move-object v15, v7

    move-object v7, v15

    move-object v8, v15

    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    invoke-virtual {v9}, Ljava/lang/Process;->getOutputStream()Ljava/io/OutputStream;

    move-result-object v9

    invoke-direct {v8, v9}, Ljava/io/DataOutputStream;-><init>(Ljava/io/OutputStream;)V

    iput-object v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    .line 2126
    move-object v6, v0

    new-instance v7, Leu/chainfire/libsuperuser/StreamGobbler;

    move-object v15, v7

    move-object v7, v15

    move-object v8, v15

    new-instance v9, Ljava/lang/StringBuilder;

    move-object v15, v9

    move-object v9, v15

    move-object v10, v15

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v11, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v10, v11}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    const-string v10, "-"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    .line 2127
    invoke-virtual {v10}, Ljava/lang/Process;->getInputStream()Ljava/io/InputStream;

    move-result-object v10

    new-instance v11, Leu/chainfire/libsuperuser/Shell$Interactive$7;

    move-object v15, v11

    move-object v11, v15

    move-object v12, v15

    move-object v13, v0

    invoke-direct {v12, v13}, Leu/chainfire/libsuperuser/Shell$Interactive$7;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    move-object v12, v1

    invoke-direct {v8, v9, v10, v11, v12}, Leu/chainfire/libsuperuser/StreamGobbler;-><init>(Ljava/lang/String;Ljava/io/InputStream;Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;)V

    iput-object v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    .line 2177
    move-object v6, v0

    new-instance v7, Leu/chainfire/libsuperuser/StreamGobbler;

    move-object v15, v7

    move-object v7, v15

    move-object v8, v15

    new-instance v9, Ljava/lang/StringBuilder;

    move-object v15, v9

    move-object v9, v15

    move-object v10, v15

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v11, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v10, v11}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    const-string v10, "*"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    .line 2178
    invoke-virtual {v10}, Ljava/lang/Process;->getErrorStream()Ljava/io/InputStream;

    move-result-object v10

    new-instance v11, Leu/chainfire/libsuperuser/Shell$Interactive$8;

    move-object v15, v11

    move-object v11, v15

    move-object v12, v15

    move-object v13, v0

    invoke-direct {v12, v13}, Leu/chainfire/libsuperuser/Shell$Interactive$8;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    move-object v12, v1

    invoke-direct {v8, v9, v10, v11, v12}, Leu/chainfire/libsuperuser/StreamGobbler;-><init>(Ljava/lang/String;Ljava/io/InputStream;Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;)V

    iput-object v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    .line 2212
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/StreamGobbler;->start()V

    .line 2213
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/StreamGobbler;->start()V

    .line 2215
    move-object v6, v0

    const/4 v7, 0x1

    iput-boolean v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->running:Z

    .line 2216
    move-object v6, v0

    const/4 v7, 0x0

    iput-boolean v7, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    .line 2218
    move-object v6, v0

    invoke-direct {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->runNextCommand()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 2220
    const/4 v6, 0x1

    move v0, v6

    goto/16 :goto_1
.end method

.method private postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z
    .locals 16
    .param p1    # Leu/chainfire/libsuperuser/Shell$Command;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p5    # Ljava/io/InputStream;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Leu/chainfire/libsuperuser/Shell$Command;",
            "I",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/io/InputStream;",
            ")Z"
        }
    .end annotation

    .prologue
    .line 1990
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object/from16 v3, p3

    move-object/from16 v4, p4

    move-object/from16 v5, p5

    move-object v6, v1

    .line 1991
    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-result-object v6

    if-nez v6, :cond_0

    move-object v6, v1

    .line 1992
    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-result-object v6

    if-nez v6, :cond_0

    move-object v6, v1

    .line 1993
    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v6

    if-nez v6, :cond_0

    move-object v6, v1

    .line 1994
    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v6

    if-nez v6, :cond_0

    .line 1996
    const/4 v6, 0x1

    move v0, v6

    .line 2038
    :goto_0
    return v0

    .line 2001
    :cond_0
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    if-eqz v6, :cond_1

    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1600(Leu/chainfire/libsuperuser/Shell$Command;)[Ljava/lang/String;

    move-result-object v6

    sget-object v7, Leu/chainfire/libsuperuser/Shell;->availableTestCommands:[Ljava/lang/String;

    if-ne v6, v7, :cond_a

    .line 2002
    :cond_1
    move-object v6, v5

    if-nez v6, :cond_9

    .line 2003
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-result-object v6

    if-eqz v6, :cond_2

    .line 2004
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-result-object v6

    move-object v7, v1

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v7

    move v8, v2

    move-object v9, v3

    if-eqz v9, :cond_6

    move-object v9, v3

    :goto_1
    invoke-interface {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;->onCommandResult(IILjava/util/List;)V

    .line 2005
    :cond_2
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-result-object v6

    if-eqz v6, :cond_3

    .line 2006
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-result-object v6

    move-object v7, v1

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v7

    move v8, v2

    move-object v9, v3

    if-eqz v9, :cond_7

    move-object v9, v3

    :goto_2
    move-object v10, v4

    if-eqz v10, :cond_8

    move-object v10, v4

    :goto_3
    invoke-interface {v6, v7, v8, v9, v10}, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;->onCommandResult(IILjava/util/List;Ljava/util/List;)V

    .line 2007
    :cond_3
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v6

    if-eqz v6, :cond_4

    .line 2008
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v6

    move-object v7, v1

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v7

    move v8, v2

    invoke-interface {v6, v7, v8}, Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;->onCommandResult(II)V

    .line 2009
    :cond_4
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v6

    if-eqz v6, :cond_5

    .line 2010
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v6

    move-object v7, v1

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v7

    move v8, v2

    invoke-interface {v6, v7, v8}, Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;->onCommandResult(II)V

    .line 2014
    :cond_5
    :goto_4
    const/4 v6, 0x1

    move v0, v6

    goto :goto_0

    .line 2004
    :cond_6
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive;->emptyStringList:Ljava/util/List;

    goto :goto_1

    .line 2006
    :cond_7
    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/Shell$Interactive;->emptyStringList:Ljava/util/List;

    goto :goto_2

    :cond_8
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->emptyStringList:Ljava/util/List;

    goto :goto_3

    .line 2011
    :cond_9
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v6

    if-eqz v6, :cond_5

    .line 2012
    move-object v6, v1

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v6

    move-object v7, v5

    invoke-interface {v6, v7}, Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;->onInputStream(Ljava/io/InputStream;)V

    goto :goto_4

    .line 2016
    :cond_a
    move-object v6, v0

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->startCallback()V

    .line 2017
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    new-instance v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;

    move-object v15, v7

    move-object v7, v15

    move-object v8, v15

    move-object v9, v0

    move-object v10, v5

    move-object v11, v1

    move v12, v2

    move-object v13, v3

    move-object v14, v4

    invoke-direct/range {v8 .. v14}, Leu/chainfire/libsuperuser/Shell$Interactive$5;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/io/InputStream;Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;)V

    invoke-virtual {v6, v7}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v6

    .line 2038
    const/4 v6, 0x0

    move v0, v6

    goto/16 :goto_0
.end method

.method private declared-synchronized processLine(Ljava/lang/String;Ljava/lang/Object;Z)V
    .locals 13
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    .prologue
    .line 1924
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move/from16 v3, p3

    move-object v11, p0

    monitor-enter v11

    move-object v4, v2

    if-eqz v4, :cond_0

    .line 1925
    move-object v4, v0

    :try_start_0
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    if-eqz v4, :cond_1

    .line 1926
    move-object v4, v0

    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->startCallback()V

    .line 1927
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    new-instance v5, Leu/chainfire/libsuperuser/Shell$Interactive$4;

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    move-object v7, v0

    move-object v8, v2

    move-object v9, v1

    move v10, v3

    invoke-direct {v6, v7, v8, v9, v10}, Leu/chainfire/libsuperuser/Shell$Interactive$4;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/Object;Ljava/lang/String;Z)V

    invoke-virtual {v4, v5}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v4

    .line 1953
    :cond_0
    :goto_0
    monitor-exit v11

    return-void

    .line 1944
    :cond_1
    move-object v4, v2

    :try_start_1
    instance-of v4, v4, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    if-eqz v4, :cond_2

    .line 1945
    move-object v4, v2

    check-cast v4, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v5, v1

    invoke-interface {v4, v5}, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;->onLine(Ljava/lang/String;)V

    goto :goto_0

    .line 1946
    :cond_2
    move-object v4, v2

    instance-of v4, v4, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;

    if-eqz v4, :cond_3

    move v4, v3

    if-nez v4, :cond_3

    .line 1947
    move-object v4, v2

    check-cast v4, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;

    move-object v5, v1

    invoke-interface {v4, v5}, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;->onSTDOUT(Ljava/lang/String;)V

    goto :goto_0

    .line 1948
    :cond_3
    move-object v4, v2

    instance-of v4, v4, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;

    if-eqz v4, :cond_0

    move v4, v3

    if-eqz v4, :cond_0

    .line 1949
    move-object v4, v2

    check-cast v4, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;

    move-object v5, v1

    invoke-interface {v4, v5}, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;->onSTDERR(Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    .line 1924
    :catchall_0
    move-exception v0

    monitor-exit v11

    throw v0
.end method

.method private declared-synchronized processMarker()V
    .locals 8

    .prologue
    .line 1903
    move-object v0, p0

    move-object v7, p0

    monitor-enter v7

    move-object v1, v0

    :try_start_0
    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    if-eqz v1, :cond_0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    .line 1904
    invoke-static {v1}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDOUT:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    .line 1905
    invoke-static {v1}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDERR:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 1906
    move-object v1, v0

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    move-object v3, v0

    iget v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->lastExitCode:I

    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    const/4 v6, 0x0

    invoke-direct/range {v1 .. v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z

    move-result v1

    .line 1907
    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->stopWatchdog()V

    .line 1908
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    .line 1909
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    .line 1910
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    .line 1911
    move-object v1, v0

    const/4 v2, 0x1

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 1912
    move-object v1, v0

    const/4 v2, 0x0

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 1913
    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->runNextCommand()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1915
    :cond_0
    monitor-exit v7

    return-void

    .line 1903
    :catchall_0
    move-exception v0

    monitor-exit v7

    throw v0
.end method

.method private runNextCommand()V
    .locals 3

    .prologue
    .line 1725
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x1

    invoke-direct {v1, v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->runNextCommand(Z)V

    .line 1726
    return-void
.end method

.method private runNextCommand(Z)V
    .locals 18

    .prologue
    .line 1802
    move-object/from16 v0, p0

    move/from16 v1, p1

    move-object v10, v0

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v10

    move v2, v10

    .line 1803
    move v10, v2

    if-eqz v10, :cond_0

    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    if-eqz v10, :cond_1

    .line 1804
    :cond_0
    move-object v10, v0

    const/4 v11, 0x1

    iput-boolean v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 1805
    move-object v10, v0

    const/4 v11, 0x0

    iput-boolean v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 1808
    :cond_1
    move v10, v2

    if-eqz v10, :cond_e

    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    if-nez v10, :cond_e

    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    if-eqz v10, :cond_e

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    invoke-interface {v10}, Ljava/util/List;->size()I

    move-result v10

    if-lez v10, :cond_e

    .line 1809
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    const/4 v11, 0x0

    invoke-interface {v10, v11}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Leu/chainfire/libsuperuser/Shell$Command;

    move-object v3, v10

    .line 1810
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    const/4 v11, 0x0

    invoke-interface {v10, v11}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v10

    .line 1812
    move-object v10, v0

    const/4 v11, 0x0

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    .line 1813
    move-object v10, v0

    const/4 v11, 0x0

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;

    .line 1814
    move-object v10, v0

    const/4 v11, 0x0

    iput v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->lastExitCode:I

    .line 1815
    move-object v10, v0

    const/4 v11, 0x0

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDOUT:Ljava/lang/String;

    .line 1816
    move-object v10, v0

    const/4 v11, 0x0

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->lastMarkerSTDERR:Ljava/lang/String;

    .line 1818
    move-object v10, v3

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$1600(Leu/chainfire/libsuperuser/Shell$Command;)[Ljava/lang/String;

    move-result-object v10

    array-length v10, v10

    if-lez v10, :cond_d

    .line 1820
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    if-eqz v10, :cond_5

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eqz v10, :cond_5

    .line 1822
    move-object v10, v3

    :try_start_0
    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-result-object v10

    if-eqz v10, :cond_4

    .line 1823
    move-object v10, v0

    new-instance v11, Ljava/util/ArrayList;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v11}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v11

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    .line 1829
    :cond_2
    :goto_0
    move-object v10, v0

    const/4 v11, 0x0

    iput-boolean v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 1830
    move-object v10, v0

    move-object v11, v3

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->command:Leu/chainfire/libsuperuser/Shell$Command;

    .line 1831
    move-object v10, v3

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v10

    if-eqz v10, :cond_a

    .line 1832
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/StreamGobbler;->isSuspended()Z

    move-result v10

    if-nez v10, :cond_3

    .line 1833
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/Thread;->getId()J

    move-result-wide v10

    move-object v12, v0

    iget-object v12, v12, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v12}, Leu/chainfire/libsuperuser/StreamGobbler;->getId()J

    move-result-wide v12

    cmp-long v10, v10, v12

    if-nez v10, :cond_9

    .line 1836
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/StreamGobbler;->suspendGobbling()V

    .line 1850
    :cond_3
    :goto_1
    move-object v10, v3

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$1600(Leu/chainfire/libsuperuser/Shell$Command;)[Ljava/lang/String;

    move-result-object v10

    move-object v4, v10

    move-object v10, v4

    array-length v10, v10

    move v5, v10

    const/4 v10, 0x0

    move v6, v10

    :goto_2
    move v10, v6

    move v11, v5

    if-ge v10, v11, :cond_b

    move-object v10, v4

    move v11, v6

    aget-object v10, v10, v11

    move-object v7, v10

    .line 1851
    sget-object v10, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v11, "[%s+] %s"

    const/4 v12, 0x2

    new-array v12, v12, [Ljava/lang/Object;

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    const/4 v14, 0x0

    move-object v15, v0

    iget-object v15, v15, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v16, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 1852
    invoke-virtual/range {v15 .. v16}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v15

    aput-object v15, v13, v14

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    const/4 v14, 0x1

    move-object v15, v7

    aput-object v15, v13, v14

    .line 1851
    invoke-static {v10, v11, v12}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Leu/chainfire/libsuperuser/Debug;->logCommand(Ljava/lang/String;)V

    .line 1853
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    new-instance v11, Ljava/lang/StringBuilder;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    move-object v12, v7

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    const-string v12, "\n"

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    const-string v12, "UTF-8"

    invoke-virtual {v11, v12}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/io/DataOutputStream;->write([B)V

    .line 1850
    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    .line 1824
    :cond_4
    move-object v10, v3

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-result-object v10

    if-eqz v10, :cond_2

    .line 1825
    move-object v10, v0

    new-instance v11, Ljava/util/ArrayList;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v11}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v11

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDOUT:Ljava/util/List;

    .line 1826
    move-object v10, v0

    new-instance v11, Ljava/util/ArrayList;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v11}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v11

    iput-object v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->bufferSTDERR:Ljava/util/List;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto/16 :goto_0

    .line 1862
    :catch_0
    move-exception v10

    move-object v4, v10

    .line 1878
    :cond_5
    :goto_3
    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    if-eqz v10, :cond_7

    .line 1879
    move v10, v2

    if-eqz v10, :cond_6

    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->doCloseWhenIdle:Z

    if-eqz v10, :cond_6

    .line 1880
    move-object v10, v0

    const/4 v11, 0x0

    iput-boolean v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->doCloseWhenIdle:Z

    .line 1881
    move-object v10, v0

    const/4 v11, 0x1

    invoke-virtual {v10, v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately(Z)V

    .line 1883
    :cond_6
    move v10, v1

    if-eqz v10, :cond_7

    .line 1884
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    move-object v3, v11

    monitor-enter v10

    .line 1885
    move-object v10, v0

    :try_start_1
    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    invoke-virtual {v10}, Ljava/lang/Object;->notifyAll()V

    .line 1886
    move-object v10, v3

    monitor-exit v10
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 1890
    :cond_7
    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    if-eqz v10, :cond_8

    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    if-nez v10, :cond_8

    .line 1891
    move-object v10, v0

    move-object v11, v0

    iget-boolean v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    iput-boolean v11, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    .line 1892
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    move-object v3, v11

    monitor-enter v10

    .line 1893
    move-object v10, v0

    :try_start_2
    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    invoke-virtual {v10}, Ljava/lang/Object;->notifyAll()V

    .line 1894
    move-object v10, v3

    monitor-exit v10
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 1896
    :cond_8
    return-void

    .line 1841
    :cond_9
    move-object v10, v0

    :try_start_3
    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    const-string v11, "echo inputstream\n"

    const-string v12, "UTF-8"

    invoke-virtual {v11, v12}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/io/DataOutputStream;->write([B)V

    .line 1842
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    invoke-virtual {v10}, Ljava/io/DataOutputStream;->flush()V

    .line 1843
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/StreamGobbler;->waitForSuspend()V

    goto/16 :goto_1

    .line 1847
    :cond_a
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/StreamGobbler;->resumeGobbling()V

    .line 1848
    move-object v10, v0

    invoke-direct {v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->startWatchdog()V

    goto/16 :goto_1

    .line 1855
    :cond_b
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    new-instance v11, Ljava/lang/StringBuilder;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "echo "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    move-object v12, v3

    invoke-static {v12}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    const-string v12, " $?\n"

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    const-string v12, "UTF-8"

    invoke-virtual {v11, v12}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/io/DataOutputStream;->write([B)V

    .line 1856
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    new-instance v11, Ljava/lang/StringBuilder;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "echo "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    move-object v12, v3

    invoke-static {v12}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    const-string v12, " >&2\n"

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    const-string v12, "UTF-8"

    invoke-virtual {v11, v12}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/io/DataOutputStream;->write([B)V

    .line 1857
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    invoke-virtual {v10}, Ljava/io/DataOutputStream;->flush()V

    .line 1858
    move-object v10, v3

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v10

    if-eqz v10, :cond_c

    .line 1859
    move-object v10, v3

    new-instance v11, Leu/chainfire/libsuperuser/MarkerInputStream;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    move-object v13, v0

    iget-object v13, v13, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    move-object v14, v3

    invoke-static {v14}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v14

    invoke-direct {v12, v13, v14}, Leu/chainfire/libsuperuser/MarkerInputStream;-><init>(Leu/chainfire/libsuperuser/StreamGobbler;Ljava/lang/String;)V

    invoke-static {v10, v11}, Leu/chainfire/libsuperuser/Shell$Command;->access$2102(Leu/chainfire/libsuperuser/Shell$Command;Leu/chainfire/libsuperuser/MarkerInputStream;)Leu/chainfire/libsuperuser/MarkerInputStream;

    move-result-object v10

    .line 1860
    move-object v10, v0

    move-object v11, v3

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    move-object v15, v3

    invoke-static {v15}, Leu/chainfire/libsuperuser/Shell$Command;->access$2100(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/MarkerInputStream;

    move-result-object v15

    invoke-direct/range {v10 .. v15}, Leu/chainfire/libsuperuser/Shell$Interactive;->postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0

    move-result v10

    .line 1864
    :cond_c
    goto/16 :goto_3

    .line 1867
    :cond_d
    move-object v10, v0

    const/4 v11, 0x0

    invoke-direct {v10, v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->runNextCommand(Z)V

    goto/16 :goto_3

    .line 1869
    :cond_e
    move v10, v2

    if-eqz v10, :cond_f

    move-object v10, v0

    iget-boolean v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    if-eqz v10, :cond_5

    .line 1871
    :cond_f
    sget-object v10, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v11, "[%s%%] SHELL_DIED"

    const/4 v12, 0x1

    new-array v12, v12, [Ljava/lang/Object;

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    const/4 v14, 0x0

    move-object v15, v0

    iget-object v15, v15, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v16, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual/range {v15 .. v16}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v15

    aput-object v15, v13, v14

    invoke-static {v10, v11, v12}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 1872
    :goto_4
    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    invoke-interface {v10}, Ljava/util/List;->size()I

    move-result v10

    if-lez v10, :cond_10

    .line 1873
    move-object v10, v0

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    const/4 v12, 0x0

    invoke-interface {v11, v12}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Leu/chainfire/libsuperuser/Shell$Command;

    const/4 v12, -0x2

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/4 v15, 0x0

    invoke-direct/range {v10 .. v15}, Leu/chainfire/libsuperuser/Shell$Interactive;->postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z

    move-result v10

    goto :goto_4

    .line 1875
    :cond_10
    move-object v10, v0

    invoke-virtual {v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->onClosed()V

    goto/16 :goto_3

    .line 1886
    :catchall_0
    move-exception v10

    move-object v8, v10

    move-object v10, v3

    :try_start_4
    monitor-exit v10
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    move-object v10, v8

    throw v10

    .line 1894
    :catchall_1
    move-exception v10

    move-object v9, v10

    move-object v10, v3

    :try_start_5
    monitor-exit v10
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    move-object v10, v9

    throw v10
.end method

.method private startWatchdog()V
    .locals 10

    .prologue
    .line 1771
    move-object v1, p0

    move-object v2, v1

    iget v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogTimeout:I

    if-nez v2, :cond_0

    .line 1782
    :goto_0
    return-void

    .line 1774
    :cond_0
    move-object v2, v1

    const/4 v3, 0x0

    iput v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdogCount:I

    .line 1775
    move-object v2, v1

    new-instance v3, Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    move-object v9, v3

    move-object v3, v9

    move-object v4, v9

    const/4 v5, 0x1

    invoke-direct {v4, v5}, Ljava/util/concurrent/ScheduledThreadPoolExecutor;-><init>(I)V

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    .line 1776
    move-object v2, v1

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    new-instance v3, Leu/chainfire/libsuperuser/Shell$Interactive$3;

    move-object v9, v3

    move-object v3, v9

    move-object v4, v9

    move-object v5, v1

    invoke-direct {v4, v5}, Leu/chainfire/libsuperuser/Shell$Interactive$3;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    const-wide/16 v4, 0x1

    const-wide/16 v6, 0x1

    sget-object v8, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual/range {v2 .. v8}, Ljava/util/concurrent/ScheduledThreadPoolExecutor;->scheduleAtFixedRate(Ljava/lang/Runnable;JJLjava/util/concurrent/TimeUnit;)Ljava/util/concurrent/ScheduledFuture;

    move-result-object v2

    .line 1782
    goto :goto_0
.end method

.method private stopWatchdog()V
    .locals 3

    .prologue
    .line 1788
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    if-eqz v1, :cond_0

    .line 1789
    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    invoke-virtual {v1}, Ljava/util/concurrent/ScheduledThreadPoolExecutor;->shutdownNow()Ljava/util/List;

    move-result-object v1

    .line 1790
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->watchdog:Ljava/util/concurrent/ScheduledThreadPoolExecutor;

    .line 1792
    :cond_0
    return-void
.end method

.method private waitForCallbacks()Z
    .locals 7

    .prologue
    .line 2449
    move-object v0, p0

    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    if-eqz v4, :cond_1

    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    .line 2450
    invoke-virtual {v4}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object v4

    if-eqz v4, :cond_1

    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    .line 2451
    invoke-virtual {v4}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object v4

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v5

    if-eq v4, v5, :cond_1

    .line 2459
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->callbackSync:Ljava/lang/Object;

    move-object v6, v4

    move-object v4, v6

    move-object v5, v6

    move-object v1, v5

    monitor-enter v4

    .line 2460
    :goto_0
    move-object v4, v0

    :try_start_0
    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-lez v4, :cond_0

    .line 2462
    move-object v4, v0

    :try_start_1
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->callbackSync:Ljava/lang/Object;

    invoke-virtual {v4}, Ljava/lang/Object;->wait()V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2465
    goto :goto_0

    .line 2463
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 2464
    const/4 v4, 0x0

    move-object v5, v1

    :try_start_2
    monitor-exit v5

    move v0, v4

    .line 2469
    :goto_1
    return v0

    .line 2467
    :cond_0
    move-object v4, v1

    monitor-exit v4

    .line 2469
    :cond_1
    const/4 v4, 0x1

    move v0, v4

    goto :goto_1

    .line 2467
    :catchall_0
    move-exception v4

    move-object v3, v4

    move-object v4, v1

    monitor-exit v4
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-object v4, v3

    throw v4
.end method


# virtual methods
.method public declared-synchronized addCommand(Ljava/lang/Object;)V
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 1701
    move-object v0, p0

    move-object v1, p1

    move-object v6, p0

    monitor-enter v6

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    const/4 v5, 0x0

    :try_start_0
    invoke-virtual {v2, v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1702
    monitor-exit v6

    return-void

    .line 1701
    :catchall_0
    move-exception v0

    monitor-exit v6

    throw v0
.end method

.method public declared-synchronized addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V
    .locals 12
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Leu/chainfire/libsuperuser/Shell$OnResult;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 1716
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-object v10, p0

    monitor-enter v10

    move-object v4, v0

    :try_start_0
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    new-instance v5, Leu/chainfire/libsuperuser/Shell$Command;

    move-object v11, v5

    move-object v5, v11

    move-object v6, v11

    move-object v7, v1

    move v8, v2

    move-object v9, v3

    invoke-direct {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Command;-><init>(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-result v4

    .line 1717
    move-object v4, v0

    invoke-direct {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->runNextCommand()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1718
    monitor-exit v10

    return-void

    .line 1716
    :catchall_0
    move-exception v0

    monitor-exit v10

    throw v0
.end method

.method public close()V
    .locals 2
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 2242
    move-object v0, p0

    move-object v1, v0

    invoke-virtual {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately()V

    .line 2243
    return-void
.end method

.method public closeImmediately()V
    .locals 3
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 2254
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately(Z)V

    .line 2255
    return-void
.end method

.method protected closeImmediately(Z)V
    .locals 13

    .prologue
    .line 2259
    move-object v0, p0

    move v1, p1

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    if-eqz v5, :cond_0

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eqz v5, :cond_0

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eqz v5, :cond_0

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    if-nez v5, :cond_1

    :cond_0
    new-instance v5, Ljava/lang/NullPointerException;

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    invoke-direct {v6}, Ljava/lang/NullPointerException;-><init>()V

    throw v5

    .line 2261
    :cond_1
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->isIdle()Z

    move-result v5

    move v2, v5

    .line 2263
    move-object v5, v0

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    move-object v3, v6

    monitor-enter v5

    .line 2264
    move-object v5, v0

    :try_start_0
    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->running:Z

    if-nez v5, :cond_2

    .line 2265
    move-object v5, v3

    monitor-exit v5

    .line 2335
    :goto_0
    return-void

    .line 2266
    :cond_2
    move-object v5, v0

    const/4 v6, 0x0

    iput-boolean v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->running:Z

    .line 2267
    move-object v5, v0

    const/4 v6, 0x1

    iput-boolean v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    .line 2268
    move-object v5, v3

    monitor-exit v5
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2270
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v5

    if-nez v5, :cond_3

    .line 2271
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->onClosed()V

    .line 2272
    goto :goto_0

    .line 2268
    :catchall_0
    move-exception v5

    move-object v4, v5

    move-object v5, v3

    :try_start_1
    monitor-exit v5
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    move-object v5, v4

    throw v5

    .line 2278
    :cond_3
    move v5, v2

    if-nez v5, :cond_4

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabledEffective()Z

    move-result v5

    if-eqz v5, :cond_4

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->onMainThread()Z

    move-result v5

    if-eqz v5, :cond_4

    .line 2279
    const-string v5, "Application attempted to wait for a non-idle shell to close on the main thread"

    invoke-static {v5}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 2280
    new-instance v5, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    const-string v7, "Application attempted to wait for a non-idle shell to close on the main thread"

    invoke-direct {v6, v7}, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;-><init>(Ljava/lang/String;)V

    throw v5

    .line 2283
    :cond_4
    move v5, v2

    if-nez v5, :cond_5

    .line 2284
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForIdle()Z

    move-result v5

    .line 2288
    :cond_5
    move-object v5, v0

    :try_start_2
    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    const-string v6, "exit\n"

    const-string v7, "UTF-8"

    invoke-virtual {v6, v7}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/io/DataOutputStream;->write([B)V

    .line 2289
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    invoke-virtual {v5}, Ljava/io/DataOutputStream;->flush()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_3

    .line 2301
    :cond_6
    :goto_1
    move-object v5, v0

    :try_start_3
    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    invoke-virtual {v5}, Ljava/lang/Process;->waitFor()I
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_3 .. :try_end_3} :catch_3

    move-result v5

    .line 2309
    move-object v5, v0

    :try_start_4
    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    invoke-virtual {v5}, Ljava/io/DataOutputStream;->close()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_2
    .catch Ljava/lang/InterruptedException; {:try_start_4 .. :try_end_4} :catch_3

    .line 2315
    :goto_2
    :try_start_5
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v5

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eq v5, v6, :cond_7

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/StreamGobbler;->resumeGobbling()V

    .line 2316
    :cond_7
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v5

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eq v5, v6, :cond_8

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/StreamGobbler;->resumeGobbling()V

    .line 2319
    :cond_8
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v5

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eq v5, v6, :cond_9

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v5

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    if-eq v5, v6, :cond_9

    .line 2320
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDOUT:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/StreamGobbler;->join()V

    .line 2321
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->STDERR:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/StreamGobbler;->join()V

    .line 2324
    :cond_9
    move-object v5, v0

    invoke-direct {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->stopWatchdog()V

    .line 2325
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    invoke-virtual {v5}, Ljava/lang/Process;->destroy()V
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_5 .. :try_end_5} :catch_3

    .line 2332
    :goto_3
    sget-object v5, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v6, "[%s%%] END"

    const/4 v7, 0x1

    new-array v7, v7, [Ljava/lang/Object;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    const/4 v9, 0x0

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive;->shell:Ljava/lang/String;

    sget-object v11, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v10, v11}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v10

    aput-object v10, v8, v9

    invoke-static {v5, v6, v7}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 2334
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->onClosed()V

    .line 2335
    goto/16 :goto_0

    .line 2290
    :catch_0
    move-exception v5

    move-object v3, v5

    .line 2291
    move-object v5, v3

    :try_start_6
    invoke-virtual {v5}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v5

    const-string v6, "EPIPE"

    invoke-virtual {v5, v6}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_6

    move-object v5, v3

    invoke-virtual {v5}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v5

    const-string v6, "Stream closed"

    invoke-virtual {v5, v6}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_a

    goto/16 :goto_1

    .line 2295
    :cond_a
    move-object v5, v3

    throw v5
    :try_end_6
    .catch Ljava/io/IOException; {:try_start_6 .. :try_end_6} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_6 .. :try_end_6} :catch_3

    .line 2326
    :catch_1
    move-exception v5

    move-object v3, v5

    .line 2330
    goto :goto_3

    .line 2310
    :catch_2
    move-exception v5

    move-object v3, v5

    goto/16 :goto_2

    .line 2328
    :catch_3
    move-exception v5

    move-object v3, v5

    goto :goto_3
.end method

.method public closeWhenIdle()V
    .locals 3
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2344
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    if-eqz v1, :cond_0

    .line 2345
    move-object v1, v0

    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately(Z)V

    .line 2349
    :goto_0
    return-void

    .line 2347
    :cond_0
    move-object v1, v0

    const/4 v2, 0x1

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->doCloseWhenIdle:Z

    goto :goto_0
.end method

.method endCallback()V
    .locals 7

    .prologue
    .line 2046
    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbackSync:Ljava/lang/Object;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v1, v4

    monitor-enter v3

    .line 2047
    move-object v3, v0

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    :try_start_0
    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I

    const/4 v5, 0x1

    add-int/lit8 v4, v4, -0x1

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I

    .line 2048
    move-object v3, v0

    iget v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I

    if-nez v3, :cond_0

    .line 2049
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbackSync:Ljava/lang/Object;

    invoke-virtual {v3}, Ljava/lang/Object;->notifyAll()V

    .line 2051
    :cond_0
    move-object v3, v1

    monitor-exit v3

    .line 2052
    return-void

    .line 2051
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v1

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method

.method protected finalize()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .prologue
    .line 1686
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    if-nez v1, :cond_0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabledEffective()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 1688
    const-string v1, "Application did not close() interactive shell"

    invoke-static {v1}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 1689
    new-instance v1, Leu/chainfire/libsuperuser/Shell$ShellNotClosedException;

    move-object v3, v1

    move-object v1, v3

    move-object v2, v3

    invoke-direct {v2}, Leu/chainfire/libsuperuser/Shell$ShellNotClosedException;-><init>()V

    throw v1

    .line 1691
    :cond_0
    move-object v1, v0

    invoke-super {v1}, Ljava/lang/Object;->finalize()V

    .line 1692
    return-void
.end method

.method public hasCommands()Z
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2581
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->commands:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_0

    const/4 v1, 0x1

    :goto_0
    move v0, v1

    return v0

    :cond_0
    const/4 v1, 0x0

    goto :goto_0
.end method

.method public hasHandler()Z
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2571
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->handler:Landroid/os/Handler;

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    :goto_0
    move v0, v1

    return v0

    :cond_0
    const/4 v1, 0x0

    goto :goto_0
.end method

.method public declared-synchronized isIdle()Z
    .locals 8
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2432
    move-object v0, p0

    move-object v6, p0

    monitor-enter v6

    move-object v4, v0

    :try_start_0
    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v4

    if-nez v4, :cond_0

    .line 2433
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 2434
    move-object v4, v0

    const/4 v5, 0x0

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 2435
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v1, v5

    monitor-enter v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 2436
    move-object v4, v0

    :try_start_1
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    invoke-virtual {v4}, Ljava/lang/Object;->notifyAll()V

    .line 2437
    move-object v4, v1

    monitor-exit v4
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2438
    move-object v4, v0

    :try_start_2
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    if-eqz v4, :cond_0

    move-object v4, v0

    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    if-nez v4, :cond_0

    .line 2439
    move-object v4, v0

    move-object v5, v0

    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    .line 2440
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v1, v5

    monitor-enter v4
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 2441
    move-object v4, v0

    :try_start_3
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    invoke-virtual {v4}, Ljava/lang/Object;->notifyAll()V

    .line 2442
    move-object v4, v1

    monitor-exit v4
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 2445
    :cond_0
    move-object v4, v0

    :try_start_4
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    move v0, v4

    monitor-exit v6

    return v0

    .line 2437
    :catchall_0
    move-exception v4

    move-object v2, v4

    move-object v4, v1

    :try_start_5
    monitor-exit v4
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    move-object v4, v2

    :try_start_6
    throw v4
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_1

    .line 2432
    :catchall_1
    move-exception v0

    monitor-exit v6

    throw v0

    .line 2442
    :catchall_2
    move-exception v4

    move-object v3, v4

    move-object v4, v1

    :try_start_7
    monitor-exit v4
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_2

    move-object v4, v3

    :try_start_8
    throw v4
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_1
.end method

.method public isOpening()Z
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2403
    move-object v0, p0

    move-object v1, v0

    invoke-virtual {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v1

    if-eqz v1, :cond_0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    :goto_0
    move v0, v1

    return v0

    :cond_0
    const/4 v1, 0x0

    goto :goto_0
.end method

.method public isRunning()Z
    .locals 3
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2413
    move-object v0, p0

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    if-nez v2, :cond_0

    .line 2414
    const/4 v2, 0x0

    move v0, v2

    .line 2422
    :goto_0
    return v0

    .line 2417
    :cond_0
    move-object v2, v0

    :try_start_0
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    invoke-virtual {v2}, Ljava/lang/Process;->exitValue()I
    :try_end_0
    .catch Ljava/lang/IllegalThreadStateException; {:try_start_0 .. :try_end_0} :catch_0

    move-result v2

    .line 2418
    const/4 v2, 0x0

    move v0, v2

    goto :goto_0

    .line 2419
    :catch_0
    move-exception v2

    move-object v1, v2

    .line 2422
    const/4 v2, 0x1

    move v0, v2

    goto :goto_0
.end method

.method public declared-synchronized kill()V
    .locals 8
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 2359
    move-object v0, p0

    move-object v6, p0

    monitor-enter v6

    move-object v4, v0

    :try_start_0
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    if-eqz v4, :cond_0

    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    if-nez v4, :cond_1

    :cond_0
    new-instance v4, Ljava/lang/NullPointerException;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    invoke-direct {v5}, Ljava/lang/NullPointerException;-><init>()V

    throw v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :catchall_0
    move-exception v0

    monitor-exit v6

    throw v0

    .line 2361
    :cond_1
    move-object v4, v0

    const/4 v5, 0x0

    :try_start_1
    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->running:Z

    .line 2362
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2365
    move-object v4, v0

    :try_start_2
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->STDIN:Ljava/io/DataOutputStream;

    invoke-virtual {v4}, Ljava/io/DataOutputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 2370
    :goto_0
    move-object v4, v0

    :try_start_3
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->process:Ljava/lang/Process;

    invoke-virtual {v4}, Ljava/lang/Process;->destroy()V
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_1
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 2375
    :goto_1
    move-object v4, v0

    const/4 v5, 0x1

    :try_start_4
    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z

    .line 2376
    move-object v4, v0

    const/4 v5, 0x0

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    .line 2377
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v1, v5

    monitor-enter v4
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 2378
    move-object v4, v0

    :try_start_5
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    invoke-virtual {v4}, Ljava/lang/Object;->notifyAll()V

    .line 2379
    move-object v4, v1

    monitor-exit v4
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 2380
    move-object v4, v0

    :try_start_6
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    if-eqz v4, :cond_2

    move-object v4, v0

    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    if-nez v4, :cond_2

    .line 2381
    move-object v4, v0

    move-object v5, v0

    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->lastOpening:Z

    .line 2382
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v1, v5

    monitor-enter v4
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    .line 2383
    move-object v4, v0

    :try_start_7
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    invoke-virtual {v4}, Ljava/lang/Object;->notifyAll()V

    .line 2384
    move-object v4, v1

    monitor-exit v4
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_2

    .line 2387
    :cond_2
    move-object v4, v0

    :try_start_8
    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->onClosed()V
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_0

    .line 2388
    monitor-exit v6

    return-void

    .line 2366
    :catch_0
    move-exception v4

    move-object v1, v4

    goto :goto_0

    .line 2371
    :catch_1
    move-exception v4

    move-object v1, v4

    goto :goto_1

    .line 2379
    :catchall_1
    move-exception v4

    move-object v2, v4

    move-object v4, v1

    :try_start_9
    monitor-exit v4
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_1

    move-object v4, v2

    :try_start_a
    throw v4
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_0

    .line 2384
    :catchall_2
    move-exception v4

    move-object v3, v4

    move-object v4, v1

    :try_start_b
    monitor-exit v4
    :try_end_b
    .catchall {:try_start_b .. :try_end_b} :catchall_2

    move-object v4, v3

    :try_start_c
    throw v4
    :try_end_c
    .catchall {:try_start_c .. :try_end_c} :catchall_0
.end method

.method protected onClosed()V
    .locals 0

    .prologue
    .line 2233
    return-void
.end method

.method public run(Ljava/lang/Object;)I
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 2588
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    invoke-virtual {v2, v3, v4, v5, v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I

    move-result v2

    move v0, v2

    return v0
.end method

.method public run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;)I
    .locals 13
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 2643
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    const/4 v4, 0x1

    new-array v4, v4, [I

    move-object v3, v4

    .line 2644
    move-object v4, v0

    move-object v5, v1

    const/4 v6, 0x0

    new-instance v7, Leu/chainfire/libsuperuser/Shell$Interactive$11;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v0

    move-object v10, v2

    move-object v11, v3

    invoke-direct {v8, v9, v10, v11}, Leu/chainfire/libsuperuser/Shell$Interactive$11;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;[I)V

    invoke-virtual {v4, v5, v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    .line 2660
    move-object v4, v0

    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForIdle()Z

    move-result v4

    .line 2661
    move-object v4, v3

    const/4 v5, 0x0

    aget v4, v4, v5

    if-gez v4, :cond_0

    new-instance v4, Leu/chainfire/libsuperuser/Shell$ShellDiedException;

    move-object v12, v4

    move-object v4, v12

    move-object v5, v12

    invoke-direct {v5}, Leu/chainfire/libsuperuser/Shell$ShellDiedException;-><init>()V

    throw v4

    .line 2662
    :cond_0
    move-object v4, v3

    const/4 v5, 0x0

    aget v4, v4, v5

    move v0, v4

    return v0
.end method

.method public run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;)I
    .locals 13
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 2617
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    const/4 v4, 0x1

    new-array v4, v4, [I

    move-object v3, v4

    .line 2618
    move-object v4, v0

    move-object v5, v1

    const/4 v6, 0x0

    new-instance v7, Leu/chainfire/libsuperuser/Shell$Interactive$10;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v0

    move-object v10, v2

    move-object v11, v3

    invoke-direct {v8, v9, v10, v11}, Leu/chainfire/libsuperuser/Shell$Interactive$10;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;[I)V

    invoke-virtual {v4, v5, v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    .line 2634
    move-object v4, v0

    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForIdle()Z

    move-result v4

    .line 2635
    move-object v4, v3

    const/4 v5, 0x0

    aget v4, v4, v5

    if-gez v4, :cond_0

    new-instance v4, Leu/chainfire/libsuperuser/Shell$ShellDiedException;

    move-object v12, v4

    move-object v4, v12

    move-object v5, v12

    invoke-direct {v5}, Leu/chainfire/libsuperuser/Shell$ShellDiedException;-><init>()V

    throw v4

    .line 2636
    :cond_0
    move-object v4, v3

    const/4 v5, 0x0

    aget v4, v4, v5

    move v0, v4

    return v0
.end method

.method public run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I
    .locals 16
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;Z)I"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 2595
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    move/from16 v4, p4

    move v6, v4

    if-eqz v6, :cond_1

    .line 2596
    move-object v6, v2

    if-eqz v6, :cond_0

    move-object v6, v2

    invoke-interface {v6}, Ljava/util/List;->clear()V

    .line 2597
    :cond_0
    move-object v6, v3

    if-eqz v6, :cond_1

    move-object v6, v3

    invoke-interface {v6}, Ljava/util/List;->clear()V

    .line 2599
    :cond_1
    const/4 v6, 0x1

    new-array v6, v6, [I

    move-object v5, v6

    .line 2600
    move-object v6, v0

    move-object v7, v1

    const/4 v8, 0x0

    new-instance v9, Leu/chainfire/libsuperuser/Shell$Interactive$9;

    move-object v15, v9

    move-object v9, v15

    move-object v10, v15

    move-object v11, v0

    move-object v12, v5

    move-object v13, v2

    move-object v14, v3

    invoke-direct {v10, v11, v12, v13, v14}, Leu/chainfire/libsuperuser/Shell$Interactive$9;-><init>(Leu/chainfire/libsuperuser/Shell$Interactive;[ILjava/util/List;Ljava/util/List;)V

    invoke-virtual {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    .line 2608
    move-object v6, v0

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForIdle()Z

    move-result v6

    .line 2609
    move-object v6, v5

    const/4 v7, 0x0

    aget v6, v6, v7

    if-gez v6, :cond_2

    new-instance v6, Leu/chainfire/libsuperuser/Shell$ShellDiedException;

    move-object v15, v6

    move-object v6, v15

    move-object v7, v15

    invoke-direct {v7}, Leu/chainfire/libsuperuser/Shell$ShellDiedException;-><init>()V

    throw v6

    .line 2610
    :cond_2
    move-object v6, v5

    const/4 v7, 0x0

    aget v6, v6, v7

    move v0, v6

    return v0
.end method

.method startCallback()V
    .locals 7

    .prologue
    .line 1977
    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbackSync:Ljava/lang/Object;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v1, v4

    monitor-enter v3

    .line 1978
    move-object v3, v0

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    :try_start_0
    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I

    const/4 v5, 0x1

    add-int/lit8 v4, v4, 0x1

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive;->callbacks:I

    .line 1979
    move-object v3, v1

    monitor-exit v3

    .line 1980
    return-void

    .line 1979
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v1

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method

.method public waitForIdle()Z
    .locals 8
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 2506
    move-object v0, p0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabledEffective()Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->onMainThread()Z

    move-result v4

    if-eqz v4, :cond_0

    .line 2507
    const-string v4, "Application attempted to wait for a shell to become idle on the main thread"

    invoke-static {v4}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 2508
    new-instance v4, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    const-string v6, "Application attempted to wait for a shell to become idle on the main thread"

    invoke-direct {v5, v6}, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;-><init>(Ljava/lang/String;)V

    throw v4

    .line 2511
    :cond_0
    move-object v4, v0

    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 2512
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v1, v5

    monitor-enter v4

    .line 2513
    :goto_0
    move-object v4, v0

    :try_start_0
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idle:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v4, :cond_1

    .line 2515
    move-object v4, v0

    :try_start_1
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive;->idleSync:Ljava/lang/Object;

    invoke-virtual {v4}, Ljava/lang/Object;->wait()V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2518
    goto :goto_0

    .line 2516
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 2517
    const/4 v4, 0x0

    move-object v5, v1

    :try_start_2
    monitor-exit v5

    move v0, v4

    .line 2525
    :goto_1
    return v0

    .line 2520
    :cond_1
    move-object v4, v1

    monitor-exit v4
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 2522
    move-object v4, v0

    invoke-direct {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForCallbacks()Z

    move-result v4

    move v0, v4

    goto :goto_1

    .line 2520
    :catchall_0
    move-exception v4

    move-object v3, v4

    move-object v4, v1

    :try_start_3
    monitor-exit v4
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    move-object v4, v3

    throw v4

    .line 2525
    :cond_2
    const/4 v4, 0x1

    move v0, v4

    goto :goto_1
.end method

.method public waitForOpened(Ljava/lang/Boolean;)Z
    .locals 9
    .param p1    # Ljava/lang/Boolean;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 2542
    move-object v0, p0

    move-object v1, p1

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabledEffective()Z

    move-result v5

    if-eqz v5, :cond_0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->onMainThread()Z

    move-result v5

    if-eqz v5, :cond_0

    .line 2543
    const-string v5, "Application attempted to wait for a shell to become idle on the main thread"

    invoke-static {v5}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 2544
    new-instance v5, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    const-string v7, "Application attempted to wait for a shell to become idle on the main thread"

    invoke-direct {v6, v7}, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;-><init>(Ljava/lang/String;)V

    throw v5

    .line 2547
    :cond_0
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v5

    if-eqz v5, :cond_3

    .line 2548
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v2, v6

    monitor-enter v5

    .line 2549
    :goto_0
    move-object v5, v0

    :try_start_0
    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->opening:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v5, :cond_2

    .line 2551
    move-object v5, v0

    :try_start_1
    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive;->openingSync:Ljava/lang/Object;

    invoke-virtual {v5}, Ljava/lang/Object;->wait()V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2556
    goto :goto_0

    .line 2552
    :catch_0
    move-exception v5

    move-object v3, v5

    .line 2553
    move-object v5, v1

    if-eqz v5, :cond_1

    .line 2554
    move-object v5, v1

    :try_start_2
    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    move-object v6, v2

    monitor-exit v6

    move v0, v5

    .line 2561
    :goto_1
    return v0

    .line 2556
    :cond_1
    goto :goto_0

    .line 2558
    :cond_2
    move-object v5, v2

    monitor-exit v5
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 2561
    :cond_3
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v5

    move v0, v5

    goto :goto_1

    .line 2558
    :catchall_0
    move-exception v5

    move-object v4, v5

    move-object v5, v2

    :try_start_3
    monitor-exit v5
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    move-object v5, v4

    throw v5
.end method
