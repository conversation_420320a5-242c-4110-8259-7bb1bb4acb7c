.class Lcom/projectvb/SwitchStyle$1;
.super Ljava/lang/Object;
.source "SwitchStyle.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/SwitchStyle;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/SwitchStyle;


# direct methods
.method constructor <init>(Lcom/projectvb/SwitchStyle;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/SwitchStyle;

    .line 995
    iput-object p1, p0, Lcom/projectvb/SwitchStyle$1;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 998
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$1;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$100(Lcom/projectvb/SwitchStyle;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 999
    iget-object v0, p0, Lcom/projectvb/SwitchStyle$1;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v0}, Lcom/projectvb/SwitchStyle;->access$200(Lcom/projectvb/SwitchStyle;)V

    .line 1001
    :cond_0
    return-void
.end method
