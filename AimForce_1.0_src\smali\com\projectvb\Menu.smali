.class public Lcom/projectvb/Menu;
.super Ljava/lang/Object;
.source "Menu.java"


# static fields
.field public static PrimaryColor:I

.field static container_features:Landroid/widget/LinearLayout;

.field private static context:Landroid/content/Context;

.field private static utils:Lcom/projectvb/Utils;


# instance fields
.field private buttonClick:I

.field drawView:Lcom/projectvb/DrawView;

.field private frameLayout:Landroid/widget/FrameLayout;

.field private injectType:I

.field private target:Ljava/lang/String;

.field private windowManager:Landroid/view/WindowManager;

.field windowManagerDrawViewParams:Landroid/view/WindowManager$LayoutParams;

.field private windowManagerParams:Landroid/view/WindowManager$LayoutParams;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 47
    const/high16 v0, -0x10000

    sput v0, Lcom/projectvb/Menu;->PrimaryColor:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;I)V
    .locals 2
    .param p1, "globContext"    # Landroid/content/Context;
    .param p2, "glob_injectType"    # I

    .line 87
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 42
    const-string v0, "com.dts.freefiremax"

    iput-object v0, p0, Lcom/projectvb/Menu;->target:Ljava/lang/String;

    .line 46
    const/4 v0, 0x0

    iput v0, p0, Lcom/projectvb/Menu;->buttonClick:I

    .line 88
    sput-object p1, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    .line 89
    new-instance v0, Lcom/projectvb/Utils;

    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Lcom/projectvb/Utils;-><init>(Landroid/content/Context;)V

    sput-object v0, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    .line 90
    iput p2, p0, Lcom/projectvb/Menu;->injectType:I

    .line 91
    # Load real PMM library
    :try_start_0
    const-string v0, "P2077KNG"
    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/UnsatisfiedLinkError; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_lib_loaded

    :catch_0
    move-exception v0
    # Fallback to original library
    :try_start_1
    const-string v0, "hawdawdawdawda"
    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V
    :try_end_1
    .catch Ljava/lang/UnsatisfiedLinkError; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_lib_loaded

    :catch_1
    move-exception v0

    :goto_lib_loaded

    .line 92
    invoke-virtual {p0}, Lcom/projectvb/Menu;->onCreate()V

    .line 92
    # Initialize Real PMM Interface
    invoke-virtual {p0}, Lcom/projectvb/Menu;->FlutuanteService()V

    .line 93
    return-void
.end method

.method public static native ChangesID(II)V
.end method

.method public static native Functions()V
.end method

.method public static native Init()V
.end method

.method private InjectX86(Ljava/lang/String;)Z
    .locals 14
    .param p1, "Lib"    # Ljava/lang/String;

    .line 479
    const-string v0, "chmod 777 "

    const-string v1, " > "

    const-string v2, "cat "

    const-string v3, "rm -f "

    const/4 v4, 0x0

    :try_start_0
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v6, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-virtual {v6}, Landroid/content/Context;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v6

    iget-object v6, v6, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v6, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, "libinjectEmulator.so"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 480
    .local v5, "injector":Ljava/lang/String;
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v7, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-virtual {v7}, Landroid/content/Context;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v7

    iget-object v7, v7, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v7, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 482
    .local v6, "payload_source":Ljava/lang/String;
    const-string v7, "/data/local/tmp/libinject"

    .line 483
    .local v7, "injector_dest":Ljava/lang/String;
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "/data/local/tmp/"

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    .line 484
    .local v8, "payload_dest":Ljava/lang/String;
    const-string v9, "/data/local/tmp/libmeuovo.so"

    .line 487
    .local v9, "payload_alias":Ljava/lang/String;
    const/4 v10, 0x1

    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 488
    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 489
    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 492
    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    move-result-object v11

    .line 496
    .local v11, "result":Lcom/topjohnwu/superuser/Shell$Result;
    new-array v12, v10, [Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    aput-object v13, v12, v4

    invoke-static {v12}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v12

    invoke-virtual {v12}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 497
    new-array v12, v10, [Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    aput-object v1, v12, v4

    invoke-static {v12}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 500
    new-array v1, v10, [Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 501
    new-array v1, v10, [Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 502
    new-array v1, v10, [Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    aput-object v0, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v0

    invoke-virtual {v0}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 505
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "su -c \"/data/local/tmp/libinject -pkg com.dts.freefiremax -lib "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " -open -dl_memfd\""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 507
    .local v0, "injectCmd":Ljava/lang/String;
    new-array v1, v10, [Ljava/lang/String;

    aput-object v0, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    move-result-object v1

    .line 510
    .end local v11    # "result":Lcom/topjohnwu/superuser/Shell$Result;
    .local v1, "result":Lcom/topjohnwu/superuser/Shell$Result;
    new-array v2, v10, [Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    aput-object v11, v2, v4

    invoke-static {v2}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v2

    invoke-virtual {v2}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 511
    new-array v2, v10, [Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    aput-object v11, v2, v4

    invoke-static {v2}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v2

    invoke-virtual {v2}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 512
    new-array v2, v10, [Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v2, v4

    invoke-static {v2}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v2

    invoke-virtual {v2}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 514
    invoke-static {}, Lcom/projectvb/Menu;->Functions()V

    .line 515
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    const-string v3, "Successfully Started"

    invoke-static {v2, v3, v4}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v2

    invoke-virtual {v2}, Landroid/widget/Toast;->show()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 516
    return v10

    .line 517
    .end local v0    # "injectCmd":Ljava/lang/String;
    .end local v1    # "result":Lcom/topjohnwu/superuser/Shell$Result;
    .end local v5    # "injector":Ljava/lang/String;
    .end local v6    # "payload_source":Ljava/lang/String;
    .end local v7    # "injector_dest":Ljava/lang/String;
    .end local v8    # "payload_dest":Ljava/lang/String;
    .end local v9    # "payload_alias":Ljava/lang/String;
    :catch_0
    move-exception v0

    .line 519
    .local v0, "e":Ljava/lang/Exception;
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 520
    return v4
.end method

.method public static native OnDrawLoad(Lcom/projectvb/DrawView;Landroid/graphics/Canvas;)V
.end method

.method static synthetic access$000(Lcom/projectvb/Menu;)I
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Menu;

    .line 35
    iget v0, p0, Lcom/projectvb/Menu;->buttonClick:I

    return v0
.end method

.method static synthetic access$008(Lcom/projectvb/Menu;)I
    .locals 2
    .param p0, "x0"    # Lcom/projectvb/Menu;

    .line 35
    iget v0, p0, Lcom/projectvb/Menu;->buttonClick:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/projectvb/Menu;->buttonClick:I

    return v0
.end method

.method static synthetic access$100(Lcom/projectvb/Menu;)I
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Menu;

    .line 35
    iget v0, p0, Lcom/projectvb/Menu;->injectType:I

    return v0
.end method

.method static synthetic access$200(Lcom/projectvb/Menu;Ljava/lang/String;)Z
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Menu;
    .param p1, "x1"    # Ljava/lang/String;

    .line 35
    invoke-direct {p0, p1}, Lcom/projectvb/Menu;->InjectX86(Ljava/lang/String;)Z

    move-result v0

    return v0
.end method

.method static synthetic access$300(Lcom/projectvb/Menu;)Landroid/widget/FrameLayout;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Menu;

    .line 35
    iget-object v0, p0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    return-object v0
.end method

.method static synthetic access$400(Lcom/projectvb/Menu;)Landroid/view/WindowManager$LayoutParams;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Menu;

    .line 35
    iget-object v0, p0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    return-object v0
.end method

.method static synthetic access$500(Lcom/projectvb/Menu;)Landroid/view/WindowManager;
    .locals 1
    .param p0, "x0"    # Lcom/projectvb/Menu;

    .line 35
    iget-object v0, p0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    return-object v0
.end method

.method public static addCategory(Ljava/lang/String;)V
    .locals 5
    .param p0, "name"    # Ljava/lang/String;

    .line 288
    new-instance v0, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v0}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 289
    .local v0, "gradientDrawable":Landroid/graphics/drawable/GradientDrawable;
    sget v1, Lcom/projectvb/Menu;->PrimaryColor:I

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 290
    sget-object v1, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/4 v2, 0x4

    invoke-virtual {v1, v2}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v1

    int-to-float v1, v1

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 292
    new-instance v1, Landroid/widget/LinearLayout;

    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 293
    .local v1, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v3, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v4, 0x19

    invoke-virtual {v3, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v3

    const/4 v4, -0x1

    invoke-direct {v2, v4, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 294
    invoke-virtual {v1, v0}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 295
    const/16 v2, 0x11

    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 297
    new-instance v2, Landroid/widget/TextView;

    sget-object v3, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v2, v3}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 298
    .local v2, "textView":Landroid/widget/TextView;
    invoke-virtual {v2, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 299
    const/high16 v3, 0x41300000    # 11.0f

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setTextSize(F)V

    .line 300
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 302
    sget-object v3, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v3, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 303
    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 304
    return-void
.end method

.method public static addSeekBar(Ljava/lang/String;IILjava/lang/String;I)V
    .locals 11
    .param p0, "name"    # Ljava/lang/String;
    .param p1, "value"    # I
    .param p2, "max"    # I
    .param p3, "type"    # Ljava/lang/String;
    .param p4, "ID"    # I

    .line 345
    new-instance v0, Landroid/widget/LinearLayout;

    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 346
    .local v0, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x2

    invoke-direct {v1, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 347
    sget-object v1, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/4 v3, 0x2

    invoke-virtual {v1, v3}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v1

    sget-object v4, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v4, v3}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v4

    const/4 v5, 0x0

    invoke-virtual {v0, v5, v1, v5, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 348
    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 350
    new-instance v4, Landroid/widget/TextView;

    sget-object v5, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v4, v5}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 351
    .local v4, "textView":Landroid/widget/TextView;
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, ": "

    invoke-virtual {p0, v6}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 352
    const/high16 v5, 0x41300000    # 11.0f

    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setTextSize(F)V

    .line 353
    invoke-virtual {v4, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 354
    const-string v2, "Color"

    invoke-virtual {p3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    const/16 v6, 0x9

    const-string v7, "LineType"

    const-string v8, "BoxType"

    if-eqz v5, :cond_9

    .line 355
    if-nez p1, :cond_0

    .line 356
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#ffffff\'>White</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 357
    :cond_0
    if-ne p1, v1, :cond_1

    .line 358
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#00FF00\'>Green</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 359
    :cond_1
    if-ne p1, v3, :cond_2

    .line 360
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#0000FF\'>Blue</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 361
    :cond_2
    const/4 v1, 0x3

    if-ne p1, v1, :cond_3

    .line 362
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#FF0000\'>Red</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 363
    :cond_3
    const/4 v1, 0x4

    if-ne p1, v1, :cond_4

    .line 364
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#000000\'>Black</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 365
    :cond_4
    const/4 v1, 0x5

    if-ne p1, v1, :cond_5

    .line 366
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#FFFF00\'>Yellow</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 367
    :cond_5
    const/4 v1, 0x6

    if-ne p1, v1, :cond_6

    .line 368
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#00FFFF\'>Cyan</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 369
    :cond_6
    const/4 v1, 0x7

    if-ne p1, v1, :cond_7

    .line 370
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#FF00FF\'>Magenta</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 371
    :cond_7
    const/16 v1, 0x8

    if-ne p1, v1, :cond_8

    .line 372
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#808080\'>Gray</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 373
    :cond_8
    if-ne p1, v6, :cond_f

    .line 374
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ": <font color=\'#A020F0\'>Purple</font>"

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 376
    :cond_9
    invoke-virtual {p3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_c

    .line 377
    if-nez p1, :cond_a

    .line 378
    const-string v1, ": Stroke"

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 379
    :cond_a
    if-ne p1, v1, :cond_b

    .line 380
    const-string v1, ": Filled"

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 381
    :cond_b
    if-ne p1, v3, :cond_f

    .line 382
    const-string v1, ": Rounded"

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 384
    :cond_c
    invoke-virtual {p3, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_f

    .line 385
    if-nez p1, :cond_d

    .line 386
    const-string v1, ": Top"

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 387
    :cond_d
    if-ne p1, v1, :cond_e

    .line 388
    const-string v1, ": Center"

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 389
    :cond_e
    if-ne p1, v3, :cond_f

    .line 390
    const-string v1, ": Bottom"

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 394
    :cond_f
    :goto_0
    new-instance v1, Landroid/widget/SeekBar;

    sget-object v5, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v5}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    .line 395
    .local v1, "seekBar":Landroid/widget/SeekBar;
    invoke-virtual {v1}, Landroid/widget/SeekBar;->getThumb()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    sget v9, Lcom/projectvb/Menu;->PrimaryColor:I

    sget-object v10, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v5, v9, v10}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 396
    invoke-virtual {v1}, Landroid/widget/SeekBar;->getProgressDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    sget v9, Lcom/projectvb/Menu;->PrimaryColor:I

    sget-object v10, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v5, v9, v10}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 397
    invoke-virtual {v1, p1}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 398
    invoke-virtual {v1, p2}, Landroid/widget/SeekBar;->setMax(I)V

    .line 399
    invoke-virtual {p3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_10

    .line 400
    invoke-virtual {v1, v6}, Landroid/widget/SeekBar;->setMax(I)V

    goto :goto_1

    .line 401
    :cond_10
    invoke-virtual {p3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_11

    .line 402
    invoke-virtual {v1, v3}, Landroid/widget/SeekBar;->setMax(I)V

    goto :goto_1

    .line 403
    :cond_11
    invoke-virtual {p3, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_12

    .line 404
    invoke-virtual {v1, v3}, Landroid/widget/SeekBar;->setMax(I)V

    .line 407
    :cond_12
    :goto_1
    new-instance v2, Lcom/projectvb/Menu$6;

    invoke-direct {v2, p3, v4, p0, p4}, Lcom/projectvb/Menu$6;-><init>(Ljava/lang/String;Landroid/widget/TextView;Ljava/lang/String;I)V

    invoke-virtual {v1, v2}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 467
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 468
    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 469
    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 470
    return-void
.end method

.method public static addSwitch(Ljava/lang/String;I)V
    .locals 6
    .param p0, "name"    # Ljava/lang/String;
    .param p1, "ID"    # I

    .line 307
    new-instance v0, Landroid/widget/LinearLayout;

    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 308
    .local v0, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x2

    invoke-direct {v1, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 309
    sget-object v1, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/4 v4, 0x2

    invoke-virtual {v1, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v1

    sget-object v5, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v5, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v4

    const/4 v5, 0x0

    invoke-virtual {v0, v5, v1, v5, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 310
    invoke-virtual {v0, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 311
    const/16 v1, 0x11

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 313
    new-instance v1, Landroid/widget/TextView;

    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v4}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 314
    .local v1, "textView":Landroid/widget/TextView;
    new-instance v4, Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v5, 0x3f800000    # 1.0f

    invoke-direct {v4, v2, v3, v5}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v1, v4}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 315
    const/16 v4, 0x10

    invoke-virtual {v1, v4}, Landroid/widget/TextView;->setGravity(I)V

    .line 316
    invoke-virtual {v1, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 317
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 318
    const/high16 v2, 0x41300000    # 11.0f

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextSize(F)V

    .line 320
    new-instance v2, Landroid/widget/CheckBox;

    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v2, v4}, Landroid/widget/CheckBox;-><init>(Landroid/content/Context;)V

    .line 321
    .local v2, "checkBox":Landroid/widget/CheckBox;
    new-instance v4, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v4, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v4}, Landroid/widget/CheckBox;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 322
    invoke-virtual {v2}, Landroid/widget/CheckBox;->getButtonDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v3

    const v4, -0xffff01

    invoke-virtual {v3, v4}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 324
    new-instance v3, Lcom/projectvb/Menu$4;

    invoke-direct {v3, p1}, Lcom/projectvb/Menu$4;-><init>(I)V

    invoke-virtual {v2, v3}, Landroid/widget/CheckBox;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 331
    new-instance v3, Lcom/projectvb/Menu$5;

    invoke-direct {v3, v2}, Lcom/projectvb/Menu$5;-><init>(Landroid/widget/CheckBox;)V

    invoke-virtual {v0, v3}, Landroid/widget/LinearLayout;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 338
    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 339
    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 340
    sget-object v3, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v3, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 341
    return-void
.end method

.method private native imageBase64()Ljava/lang/String;
.end method

.method private onTouchListener()Landroid/view/View$OnTouchListener;
    .locals 1

    .line 249
    new-instance v0, Lcom/projectvb/Menu$3;

    invoke-direct {v0, p0}, Lcom/projectvb/Menu$3;-><init>(Lcom/projectvb/Menu;)V

    return-object v0
.end method


# virtual methods
.method public DrawCanvas()V
    .locals 8

    .line 72
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1a

    if-lt v0, v1, :cond_0

    .line 73
    const/16 v0, 0x7f6

    .local v0, "LAYOUT_FLAG":I
    goto :goto_0

    .line 75
    .end local v0    # "LAYOUT_FLAG":I
    :cond_0
    const/16 v0, 0x7d2

    .line 78
    .restart local v0    # "LAYOUT_FLAG":I
    :goto_0
    new-instance v1, Lcom/projectvb/DrawView;

    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Lcom/projectvb/DrawView;-><init>(Landroid/content/Context;)V

    iput-object v1, p0, Lcom/projectvb/Menu;->drawView:Lcom/projectvb/DrawView;

    .line 79
    new-instance v7, Landroid/view/WindowManager$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x1

    const/16 v5, 0x438

    const/4 v6, -0x2

    move-object v1, v7

    move v4, v0

    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    iput-object v7, p0, Lcom/projectvb/Menu;->windowManagerDrawViewParams:Landroid/view/WindowManager$LayoutParams;

    .line 80
    const/16 v1, 0x11

    iput v1, v7, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 81
    iget-object v1, p0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    iget-object v2, p0, Lcom/projectvb/Menu;->drawView:Lcom/projectvb/DrawView;

    iget-object v3, p0, Lcom/projectvb/Menu;->windowManagerDrawViewParams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 82
    return-void
.end method

.method public onCreate()V
    .locals 0

    .line 96
    invoke-virtual {p0}, Lcom/projectvb/Menu;->onCreateSystemWindow()V

    .line 97
    invoke-virtual {p0}, Lcom/projectvb/Menu;->onCreateTemplate()V

    .line 98
    return-void
.end method

.method public onCreateSystemWindow()V
    .locals 8

    .line 226
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1a

    if-lt v0, v1, :cond_0

    .line 227
    const/16 v0, 0x7f6

    .local v0, "LAYOUT_FLAG":I
    goto :goto_0

    .line 229
    .end local v0    # "LAYOUT_FLAG":I
    :cond_0
    const/16 v0, 0x7d2

    .line 232
    .restart local v0    # "LAYOUT_FLAG":I
    :goto_0
    new-instance v1, Landroid/widget/FrameLayout;

    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    iput-object v1, p0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    .line 233
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v3, -0x2

    invoke-direct {v2, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 234
    iget-object v1, p0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    invoke-direct {p0}, Lcom/projectvb/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 235
    iget-object v1, p0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    const v2, 0x3f4ccccd    # 0.8f

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setAlpha(F)V

    .line 237
    new-instance v7, Landroid/view/WindowManager$LayoutParams;

    const/4 v2, -0x2

    const v5, 0x2820108

    const/4 v6, -0x2

    move-object v1, v7

    move v4, v0

    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    iput-object v7, p0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    .line 238
    const/16 v1, 0x33

    iput v1, v7, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 239
    iget-object v1, p0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    const/16 v2, 0xf

    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 240
    iget-object v1, p0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 242
    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    const-string v2, "window"

    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/view/WindowManager;

    iput-object v1, p0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    .line 243
    invoke-virtual {p0}, Lcom/projectvb/Menu;->DrawCanvas()V

    .line 244
    iget-object v1, p0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    iget-object v2, p0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    iget-object v3, p0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 245
    return-void
.end method

.method public onCreateTemplate()V
    .locals 19

    .line 102
    move-object/from16 v0, p0

    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 103
    .local v1, "gradientDrawable_container":Landroid/graphics/drawable/GradientDrawable;
    const v2, -0xeeeeef

    invoke-virtual {v1, v2}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 104
    sget-object v3, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v4, 0x8

    invoke-virtual {v3, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v3

    int-to-float v3, v3

    invoke-virtual {v1, v3}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 106
    new-instance v3, Landroid/widget/LinearLayout;

    sget-object v5, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v3, v5}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 107
    .local v3, "container":Landroid/widget/LinearLayout;
    const/4 v5, 0x1

    invoke-virtual {v3, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 109
    new-instance v6, Landroid/widget/LinearLayout;

    sget-object v7, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v6, v7}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 110
    .local v6, "container_menu":Landroid/widget/LinearLayout;
    new-instance v7, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v8, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v9, 0xd2

    invoke-virtual {v8, v9}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v8

    const/4 v9, -0x2

    invoke-direct {v7, v8, v9}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v6, v7}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 111
    invoke-virtual {v6, v2}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 112
    invoke-virtual {v6, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 113
    invoke-virtual {v6, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 114
    invoke-virtual {v6, v1}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 116
    new-instance v7, Lcom/projectvb/ImageBase64;

    sget-object v8, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v7, v8}, Lcom/projectvb/ImageBase64;-><init>(Landroid/content/Context;)V

    .line 117
    .local v7, "icon_cheat":Lcom/projectvb/ImageBase64;
    new-instance v8, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v10, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v11, 0x32

    invoke-virtual {v10, v11}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v10

    sget-object v12, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v12, v11}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v11

    invoke-direct {v8, v10, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v7, v8}, Lcom/projectvb/ImageBase64;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 118
    invoke-direct/range {p0 .. p0}, Lcom/projectvb/Menu;->imageBase64()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Lcom/projectvb/ImageBase64;->setImageBase64(Ljava/lang/String;)V

    .line 119
    invoke-direct/range {p0 .. p0}, Lcom/projectvb/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v8

    invoke-virtual {v7, v8}, Lcom/projectvb/ImageBase64;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 120
    new-instance v8, Lcom/projectvb/Menu$1;

    invoke-direct {v8, v0, v7, v6}, Lcom/projectvb/Menu$1;-><init>(Lcom/projectvb/Menu;Lcom/projectvb/ImageBase64;Landroid/widget/LinearLayout;)V

    invoke-virtual {v7, v8}, Lcom/projectvb/ImageBase64;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 128
    new-instance v8, Landroid/widget/LinearLayout;

    sget-object v10, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v8, v10}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 129
    .local v8, "container_top":Landroid/widget/LinearLayout;
    new-instance v10, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v11, -0x1

    invoke-direct {v10, v11, v9}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v8, v10}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 130
    sget-object v10, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v10, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v10

    sget-object v12, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v12, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v12

    sget-object v13, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v13, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v13

    sget-object v14, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v14, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v14

    invoke-virtual {v8, v10, v12, v13, v14}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 131
    const/16 v10, 0x11

    invoke-virtual {v8, v10}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 132
    const/4 v12, 0x0

    invoke-virtual {v8, v12}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 134
    new-instance v13, Lcom/projectvb/ImageBase64;

    sget-object v14, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v13, v14}, Lcom/projectvb/ImageBase64;-><init>(Landroid/content/Context;)V

    .line 135
    .local v13, "icon_menu":Lcom/projectvb/ImageBase64;
    new-instance v14, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v15, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v2, 0x2d

    invoke-virtual {v15, v2}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v15

    sget-object v9, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v9, v2}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v2

    invoke-direct {v14, v15, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v13, v14}, Lcom/projectvb/ImageBase64;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 136
    invoke-direct/range {p0 .. p0}, Lcom/projectvb/Menu;->imageBase64()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v13, v2}, Lcom/projectvb/ImageBase64;->setImageBase64(Ljava/lang/String;)V

    .line 138
    new-instance v2, Landroid/widget/LinearLayout;

    sget-object v9, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v2, v9}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 139
    .local v2, "container_center":Landroid/widget/LinearLayout;
    new-instance v9, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v14, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v15, 0xb4

    invoke-virtual {v14, v15}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v14

    invoke-direct {v9, v11, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v9}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 140
    invoke-virtual {v2, v10}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 142
    new-instance v9, Landroid/widget/ScrollView;

    sget-object v10, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v9, v10}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    .line 143
    .local v9, "scrollView_center":Landroid/widget/ScrollView;
    new-instance v10, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v10, v11, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v9, v10}, Landroid/widget/ScrollView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 145
    new-instance v10, Landroid/widget/LinearLayout;

    sget-object v14, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v10, v14}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    sput-object v10, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    .line 146
    new-instance v14, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v14, v11, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v14}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 147
    sget-object v10, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    sget-object v14, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v14, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v14

    sget-object v15, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v15, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v15

    invoke-virtual {v10, v14, v12, v15, v12}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 148
    sget-object v10, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v10, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 150
    new-instance v10, Landroid/widget/ProgressBar;

    sget-object v14, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v10, v14}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;)V

    .line 151
    .local v10, "progressBar":Landroid/widget/ProgressBar;
    new-instance v14, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v15, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v12, 0x28

    invoke-virtual {v15, v12}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v15

    sget-object v5, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v5, v12}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v5

    invoke-direct {v14, v15, v5}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v14}, Landroid/widget/ProgressBar;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 152
    invoke-virtual {v10}, Landroid/widget/ProgressBar;->getIndeterminateDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    sget v12, Lcom/projectvb/Menu;->PrimaryColor:I

    sget-object v14, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v5, v12, v14}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 154
    new-instance v5, Landroid/widget/LinearLayout;

    sget-object v12, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v5, v12}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 155
    .local v5, "container_bottom":Landroid/widget/LinearLayout;
    new-instance v12, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v14, -0x2

    invoke-direct {v12, v11, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v12}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 156
    sget-object v12, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/4 v14, 0x3

    invoke-virtual {v12, v14}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v12

    sget-object v15, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v15, v4}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v15

    sget-object v11, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v11, v14}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v11

    sget-object v4, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    invoke-virtual {v4, v14}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v4

    invoke-virtual {v5, v12, v15, v11, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 157
    const/4 v4, 0x1

    invoke-virtual {v5, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 158
    const/16 v4, 0x15

    invoke-virtual {v5, v4}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 160
    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v4}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 161
    .local v4, "gradientDrawable_hide_close":Landroid/graphics/drawable/GradientDrawable;
    sget v11, Lcom/projectvb/Menu;->PrimaryColor:I

    invoke-virtual {v4, v11}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 162
    sget-object v11, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v12, 0x8

    invoke-virtual {v11, v12}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v11

    int-to-float v11, v11

    invoke-virtual {v4, v11}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 163
    new-instance v11, Landroid/graphics/drawable/RippleDrawable;

    const v12, -0xeeeeef

    invoke-static {v12}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v14

    const/4 v12, 0x0

    invoke-direct {v11, v14, v4, v12}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    .line 166
    .local v11, "rippleDrawable1":Landroid/graphics/drawable/RippleDrawable;
    invoke-virtual {v2, v9}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 169
    new-instance v14, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v14}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 170
    .local v14, "gradientDrawable_inject_close":Landroid/graphics/drawable/GradientDrawable;
    sget v15, Lcom/projectvb/Menu;->PrimaryColor:I

    invoke-virtual {v14, v15}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 171
    sget-object v15, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    const/16 v12, 0x8

    invoke-virtual {v15, v12}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v12

    int-to-float v12, v12

    invoke-virtual {v14, v12}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 172
    new-instance v12, Landroid/graphics/drawable/RippleDrawable;

    const v15, -0xeeeeef

    invoke-static {v15}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v15

    move-object/from16 v16, v1

    const/4 v1, 0x0

    .end local v1    # "gradientDrawable_container":Landroid/graphics/drawable/GradientDrawable;
    .local v16, "gradientDrawable_container":Landroid/graphics/drawable/GradientDrawable;
    invoke-direct {v12, v15, v14, v1}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v1, v12

    .line 174
    .local v1, "rippleDrawable":Landroid/graphics/drawable/RippleDrawable;
    new-instance v12, Landroid/widget/Button;

    sget-object v15, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v12, v15}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 175
    .local v12, "inject_close":Landroid/widget/Button;
    new-instance v15, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v17, v4

    .end local v4    # "gradientDrawable_hide_close":Landroid/graphics/drawable/GradientDrawable;
    .local v17, "gradientDrawable_hide_close":Landroid/graphics/drawable/GradientDrawable;
    sget-object v4, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    move-object/from16 v18, v10

    .end local v10    # "progressBar":Landroid/widget/ProgressBar;
    .local v18, "progressBar":Landroid/widget/ProgressBar;
    const/16 v10, 0x21

    invoke-virtual {v4, v10}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v4

    const/4 v10, -0x1

    invoke-direct {v15, v10, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v12, v15}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 176
    const/4 v4, 0x0

    invoke-virtual {v12, v4, v4, v4, v4}, Landroid/widget/Button;->setPadding(IIII)V

    .line 177
    const-string v4, "INJECT"

    invoke-virtual {v12, v4}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 178
    const/high16 v4, 0x41200000    # 10.0f

    invoke-virtual {v12, v4}, Landroid/widget/Button;->setTextSize(F)V

    .line 179
    invoke-virtual {v12, v10}, Landroid/widget/Button;->setTextColor(I)V

    .line 180
    invoke-virtual {v12, v1}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 181
    new-instance v4, Lcom/projectvb/Menu$2;

    invoke-direct {v4, v0, v12, v7, v6}, Lcom/projectvb/Menu$2;-><init>(Lcom/projectvb/Menu;Landroid/widget/Button;Lcom/projectvb/ImageBase64;Landroid/widget/LinearLayout;)V

    invoke-virtual {v12, v4}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 203
    iget-object v4, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    invoke-virtual {v4, v3}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 204
    invoke-virtual {v3, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 205
    invoke-virtual {v3, v6}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 207
    invoke-virtual {v6, v8}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 208
    invoke-virtual {v8, v13}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 210
    invoke-virtual {v6, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 213
    sget-object v4, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v9, v4}, Landroid/widget/ScrollView;->addView(Landroid/view/View;)V

    .line 214
    new-instance v4, Landroid/view/View;

    sget-object v10, Lcom/projectvb/Menu;->context:Landroid/content/Context;

    invoke-direct {v4, v10}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 215
    .local v4, "spacer":Landroid/view/View;
    new-instance v10, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v15, Lcom/projectvb/Menu;->utils:Lcom/projectvb/Utils;

    .line 216
    const/4 v0, 0x5

    invoke-virtual {v15, v0}, Lcom/projectvb/Utils;->FixDP(I)I

    move-result v0

    const/4 v15, -0x1

    invoke-direct {v10, v15, v0}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 215
    invoke-virtual {v4, v10}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 218
    invoke-virtual {v6, v5}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 219
    invoke-virtual {v5, v12}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 221
    return-void
.end method

.method public createPMMStyleInterface()V
    .locals 20

    .prologue
    .line 225
    move-object/from16 v0, p0

    # Create main floating container
    new-instance v1, Landroid/widget/FrameLayout;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V
    iput-object v1, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    # Create main container
    new-instance v1, Landroid/widget/LinearLayout;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V
    sput-object v1, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;

    # Set container orientation
    sget-object v1, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    const/4 v2, 0x1
    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setOrientation(I)V

    # Set container background
    sget-object v1, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    const v2, -0xcc4a1b
    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    # Set container padding
    sget-object v1, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    const/16 v2, 0x14
    const/16 v3, 0x14
    const/16 v4, 0x14
    const/16 v5, 0x14
    invoke-virtual {v1, v2, v3, v4, v5}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    # Add title first
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addPMMTitle()V

    # Add inject button
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addInjectButton()V

    # Add separator
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addSeparator()V

    # Add features
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addPMMFeatures()V

    # Add close button
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addCloseButton()V

    # Add container to frame
    iget-object v1, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    # Setup window manager
    invoke-virtual {v0}, Lcom/projectvb/Menu;->setupPMMWindow()V

    return-void
.end method

.method public addPMMTitle()V
    .locals 5

    .prologue
    .line 270
    move-object v0, p0

    # Create title text
    new-instance v1, Landroid/widget/TextView;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    # Set title text
    const-string v2, "🎯 PMM Injector"
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    # Set title style
    const/4 v2, -0x1
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V
    const/high16 v2, 0x41800000    # 16.0f
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextSize(F)V
    const/16 v2, 0x11
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setGravity(I)V

    # Add to container
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public addInjectButton()V
    .locals 5

    .prologue
    .line 300
    move-object v0, p0

    # Create inject button
    new-instance v1, Landroid/widget/Button;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set button text
    const-string v2, "INJECT/LOAD"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Set button style
    const v2, -0x10000
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setBackgroundColor(I)V
    const/4 v2, -0x1
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setTextColor(I)V

    # Set button click listener
    new-instance v2, Lcom/projectvb/Menu$InjectClickListener;
    invoke-direct {v2, v0}, Lcom/projectvb/Menu$InjectClickListener;-><init>(Lcom/projectvb/Menu;)V
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    # Add to container
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public addSeparator()V
    .locals 5

    .prologue
    .line 320
    move-object v0, p0

    # Create separator line
    new-instance v1, Landroid/view/View;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    # Set separator style
    const v2, -0x666667
    invoke-virtual {v1, v2}, Landroid/view/View;->setBackgroundColor(I)V

    # Set separator size
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;
    const/4 v3, -0x1
    const/4 v4, 0x2
    invoke-direct {v2, v3, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V
    const/16 v3, 0xa
    const/16 v4, 0xa
    invoke-virtual {v2, v3, v3, v3, v4}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    # Add to container
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public addCloseButton()V
    .locals 5

    .prologue
    .line 350
    move-object v0, p0

    # Create close button
    new-instance v1, Landroid/widget/Button;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set close button text
    const-string v2, "CLOSE"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Set close button style
    const v2, -0x10000
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setBackgroundColor(I)V
    const/4 v2, -0x1
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setTextColor(I)V

    # Set close button click listener
    new-instance v2, Lcom/projectvb/Menu$CloseClickListener;
    invoke-direct {v2, v0}, Lcom/projectvb/Menu$CloseClickListener;-><init>(Lcom/projectvb/Menu;)V
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    # Add to container
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public addPMMFeatures()V
    .locals 10

    .prologue
    .line 330
    move-object v0, p0

    # Create features label
    new-instance v1, Landroid/widget/TextView;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V
    const-string v2, "🎮 FEATURES"
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V
    const/4 v2, -0x1
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V
    const/high16 v2, 0x41600000    # 14.0f
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextSize(F)V
    const/16 v2, 0x11
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setGravity(I)V
    sget-object v2, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    # Add ESP feature
    const-string v1, "ESP"
    const-string v2, "OFF"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addFeatureButton(Ljava/lang/String;Ljava/lang/String;)V

    # Add Aimbot feature
    const-string v1, "Aimbot"
    const-string v2, "OFF"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addFeatureButton(Ljava/lang/String;Ljava/lang/String;)V

    # Add Speed feature
    const-string v1, "Speed"
    const-string v2, "OFF"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addFeatureButton(Ljava/lang/String;Ljava/lang/String;)V

    # Add Wallhack feature
    const-string v1, "Wallhack"
    const-string v2, "OFF"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addFeatureButton(Ljava/lang/String;Ljava/lang/String;)V

    # Add No Recoil feature
    const-string v1, "No Recoil"
    const-string v2, "OFF"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addFeatureButton(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public addFeatureButton(Ljava/lang/String;Ljava/lang/String;)V
    .locals 8
    .param p1, "name"    # Ljava/lang/String;
    .param p2, "status"    # Ljava/lang/String;

    .prologue
    .line 360
    move-object v0, p0
    move-object v1, p1
    move-object v2, p2

    # Create button
    new-instance v3, Landroid/widget/Button;
    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v3, v4}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set button text
    new-instance v4, Ljava/lang/StringBuilder;
    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V
    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    const-string v5, ": "
    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;
    move-result-object v4
    invoke-virtual {v3, v4}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Set button style
    const v4, -0x333334
    invoke-virtual {v3, v4}, Landroid/widget/Button;->setBackgroundColor(I)V
    const/4 v4, -0x1
    invoke-virtual {v3, v4}, Landroid/widget/Button;->setTextColor(I)V

    # Set button padding
    const/16 v4, 0x10
    const/16 v5, 0x8
    invoke-virtual {v3, v4, v5, v4, v5}, Landroid/widget/Button;->setPadding(IIII)V

    # Set button margins
    new-instance v4, Landroid/widget/LinearLayout$LayoutParams;
    const/4 v5, -0x1
    const/4 v6, -0x2
    invoke-direct {v4, v5, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V
    const/4 v5, 0x5
    invoke-virtual {v4, v5, v5, v5, v5}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V
    invoke-virtual {v3, v4}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    # Add to container
    sget-object v4, Lcom/projectvb/Menu;->container_features:Landroid/widget/LinearLayout;
    invoke-virtual {v4, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public setupPMMWindow()V
    .locals 10

    .prologue
    .line 400
    move-object v0, p0

    # Get window manager
    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    const-string v2, "window"
    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;
    move-result-object v1
    check-cast v1, Landroid/view/WindowManager;
    iput-object v1, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    # Create layout params with better compatibility
    new-instance v1, Landroid/view/WindowManager$LayoutParams;
    const/4 v2, -0x2
    const/4 v3, -0x2
    const/16 v4, 0x7d2
    const/16 v5, 0x8
    const/4 v6, -0x3
    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V
    iput-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    # Set position
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x33
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->gravity:I
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x64
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->x:I
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x64
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->y:I

    # Add view to window manager with error handling
    :try_start_0
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;
    iget-object v2, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    iget-object v3, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v1

    # If failed, try simple interface
    invoke-virtual {v0}, Lcom/projectvb/Menu;->createSimpleInterface()V

    :goto_end

    return-void
.end method

.method public createSimpleInterface()V
    .locals 10

    .prologue
    .line 550
    move-object v0, p0

    # Create simple floating button
    new-instance v1, Landroid/widget/Button;
    sget-object v2, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set button text and style
    const-string v2, "🎯 PMM INJECT"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V
    const v2, -0x10000
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setBackgroundColor(I)V
    const/4 v2, -0x1
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setTextColor(I)V

    # Set click listener
    new-instance v2, Lcom/projectvb/Menu$InjectClickListener;
    invoke-direct {v2, v0}, Lcom/projectvb/Menu$InjectClickListener;-><init>(Lcom/projectvb/Menu;)V
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    # Create frame layout
    new-instance v2, Landroid/widget/FrameLayout;
    sget-object v3, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v2, v3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V
    iput-object v2, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    # Add button to frame
    iget-object v2, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    invoke-virtual {v2, v1}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    # Setup window with simpler params
    invoke-virtual {v0}, Lcom/projectvb/Menu;->setupSimpleWindow()V

    return-void
.end method

.method public setupSimpleWindow()V
    .locals 8

    .prologue
    .line 580
    move-object v0, p0

    # Get window manager
    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    const-string v2, "window"
    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;
    move-result-object v1
    check-cast v1, Landroid/view/WindowManager;
    iput-object v1, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    # Create simple layout params
    new-instance v1, Landroid/view/WindowManager$LayoutParams;
    const/4 v2, -0x2
    const/4 v3, -0x2
    const/16 v4, 0x7d2
    const/16 v5, 0x8
    const/4 v6, -0x3
    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V
    iput-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    # Set position
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x33
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->gravity:I

    # Add view to window manager
    :try_start_0
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;
    iget-object v2, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    iget-object v3, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v1

    :goto_end
    return-void
.end method

.method private FlutuanteService()V
    .locals 15

    .prologue
    .line 467
    move-object/from16 v2, p0

    # Create root frame
    new-instance v3, Landroid/widget/FrameLayout;
    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v3, v4}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V
    iput-object v3, v2, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;

    # Create root container
    new-instance v3, Landroid/widget/RelativeLayout;
    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v3, v4}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V
    sput-object v3, Lcom/projectvb/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    # Create collapsed view
    new-instance v3, Landroid/widget/LinearLayout;
    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v3, v4}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V
    sput-object v3, Lcom/projectvb/Menu;->mCollapsed:Landroid/widget/LinearLayout;

    # Create expanded view
    new-instance v3, Landroid/widget/LinearLayout;
    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v3, v4}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V
    sput-object v3, Lcom/projectvb/Menu;->mExpanded:Landroid/widget/LinearLayout;

    # Set expanded view orientation
    sget-object v3, Lcom/projectvb/Menu;->mExpanded:Landroid/widget/LinearLayout;
    const/4 v4, 0x1
    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    # Set expanded view background
    sget-object v3, Lcom/projectvb/Menu;->mExpanded:Landroid/widget/LinearLayout;
    const v4, -0xcc4a1b
    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    # Add real PMM features
    invoke-virtual {v2}, Lcom/projectvb/Menu;->addRealPMMFeatures()V

    # Setup window
    invoke-virtual {v2}, Lcom/projectvb/Menu;->setupRealPMMWindow()V

    return-void
.end method

.method private addRealPMMFeatures()V
    .locals 10

    .prologue
    .line 500
    move-object v0, p0

    # Add PMM title
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addPMMTitle()V

    # Add inject button
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addInjectButton()V

    # Add real ESP feature
    const-string v1, "ESP"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addRealSwitch(Ljava/lang/String;Z)V

    # Add real Aimbot feature
    const-string v1, "Aimbot"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addRealSwitch(Ljava/lang/String;Z)V

    # Add real Speed feature
    const-string v1, "Speed"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addRealSwitch(Ljava/lang/String;Z)V

    # Add real Wallhack feature
    const-string v1, "Wallhack"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addRealSwitch(Ljava/lang/String;Z)V

    # Add real No Recoil feature
    const-string v1, "No Recoil"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->addRealSwitch(Ljava/lang/String;Z)V

    # Add close button
    invoke-virtual {v0}, Lcom/projectvb/Menu;->addCloseButton()V

    return-void
.end method

.method private addRealSwitch(Ljava/lang/String;Z)V
    .locals 10
    .param p1, "name"    # Ljava/lang/String;
    .param p2, "enabled"    # Z

    .prologue
    .line 540
    move-object v0, p0
    move-object v1, p1
    move v2, p2

    # Create horizontal layout for switch
    new-instance v3, Landroid/widget/LinearLayout;
    sget-object v4, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v3, v4}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V
    const/4 v4, 0x0
    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    # Create text view for feature name
    new-instance v4, Landroid/widget/TextView;
    sget-object v5, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v4, v5}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V
    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V
    const/4 v5, -0x1
    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setTextColor(I)V
    const/high16 v5, 0x41600000    # 14.0f
    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setTextSize(F)V

    # Create switch
    new-instance v5, Landroid/widget/Switch;
    sget-object v6, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    invoke-direct {v5, v6}, Landroid/widget/Switch;-><init>(Landroid/content/Context;)V
    invoke-virtual {v5, v2}, Landroid/widget/Switch;->setChecked(Z)V

    # Set switch listener
    new-instance v6, Lcom/projectvb/Menu$FeatureSwitchListener;
    invoke-direct {v6, v0, v1}, Lcom/projectvb/Menu$FeatureSwitchListener;-><init>(Lcom/projectvb/Menu;Ljava/lang/String;)V
    invoke-virtual {v5, v6}, Landroid/widget/Switch;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    # Add views to layout
    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V
    invoke-virtual {v3, v5}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    # Add to expanded view
    sget-object v6, Lcom/projectvb/Menu;->mExpanded:Landroid/widget/LinearLayout;
    invoke-virtual {v6, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public toggleESP(Z)V
    .locals 3
    .param p1, "enabled"    # Z

    .prologue
    .line 900
    :try_start_0
    # Call native ESP function
    if-eqz p1, :cond_disable

    # Enable ESP
    const/4 v0, 0x1
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setESP(I)V

    goto :goto_end

    :cond_disable
    # Disable ESP
    const/4 v0, 0x0
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setESP(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public toggleAimbot(Z)V
    .locals 3
    .param p1, "enabled"    # Z

    .prologue
    .line 920
    :try_start_0
    # Call native Aimbot function
    if-eqz p1, :cond_disable

    # Enable Aimbot
    const/4 v0, 0x1
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setAimbot(I)V

    goto :goto_end

    :cond_disable
    # Disable Aimbot
    const/4 v0, 0x0
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setAimbot(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public toggleSpeed(Z)V
    .locals 3
    .param p1, "enabled"    # Z

    .prologue
    .line 940
    :try_start_0
    # Call native Speed function
    if-eqz p1, :cond_disable

    # Enable Speed
    const/4 v0, 0x1
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setSpeed(I)V

    goto :goto_end

    :cond_disable
    # Disable Speed
    const/4 v0, 0x0
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setSpeed(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public toggleWallhack(Z)V
    .locals 3
    .param p1, "enabled"    # Z

    .prologue
    .line 960
    :try_start_0
    # Call native Wallhack function
    if-eqz p1, :cond_disable

    # Enable Wallhack
    const/4 v0, 0x1
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setWallhack(I)V

    goto :goto_end

    :cond_disable
    # Disable Wallhack
    const/4 v0, 0x0
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setWallhack(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public toggleNoRecoil(Z)V
    .locals 3
    .param p1, "enabled"    # Z

    .prologue
    .line 980
    :try_start_0
    # Call native No Recoil function
    if-eqz p1, :cond_disable

    # Enable No Recoil
    const/4 v0, 0x1
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setNoRecoil(I)V

    goto :goto_end

    :cond_disable
    # Disable No Recoil
    const/4 v0, 0x0
    invoke-static {v0}, Lcom/projectvb/NativeLib;->setNoRecoil(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method private setupRealPMMWindow()V
    .locals 10

    .prologue
    .line 1000
    move-object v0, p0

    # Add expanded view to root container
    sget-object v1, Lcom/projectvb/Menu;->mRootContainer:Landroid/widget/RelativeLayout;
    sget-object v2, Lcom/projectvb/Menu;->mExpanded:Landroid/widget/LinearLayout;
    invoke-virtual {v1, v2}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    # Add root container to frame
    iget-object v1, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    sget-object v2, Lcom/projectvb/Menu;->mRootContainer:Landroid/widget/RelativeLayout;
    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    # Get window manager
    sget-object v1, Lcom/projectvb/Menu;->context:Landroid/content/Context;
    const-string v2, "window"
    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;
    move-result-object v1
    check-cast v1, Landroid/view/WindowManager;
    iput-object v1, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;

    # Create layout params
    new-instance v1, Landroid/view/WindowManager$LayoutParams;
    const/4 v2, -0x2
    const/4 v3, -0x2
    const/16 v4, 0x7d2
    const/16 v5, 0x8
    const/4 v6, -0x3
    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V
    iput-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    # Set position
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x33
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->gravity:I
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x64
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->x:I
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    const/16 v2, 0x64
    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->y:I

    # Add view to window manager
    :try_start_0
    iget-object v1, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;
    iget-object v2, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    iget-object v3, v0, Lcom/projectvb/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;
    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v1

    :goto_end
    return-void
.end method
