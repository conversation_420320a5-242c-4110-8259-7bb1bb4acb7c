.class Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/content/DialogInterface$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader$100000002;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000001"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader$100000002;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader$100000002;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;->this$0:Lpmm/by/p2077kng/hacker/Loader$100000002;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;)Lpmm/by/p2077kng/hacker/Loader$100000002;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;->this$0:Lpmm/by/p2077kng/hacker/Loader$100000002;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/content/DialogInterface;I)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/DialogInterface;",
            "I)V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 272
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v1

    invoke-interface {v4}, Landroid/content/DialogInterface;->dismiss()V

    return-void
.end method
