.class public Lcom/projectvb/PMMInterface;
.super Ljava/lang/Object;
.source "PMMInterface.java"

# static fields
.field public static Bypass:Landroid/widget/Button;
.field public static mCollapsed:Landroid/widget/LinearLayout;
.field public static mExpanded:Landroid/widget/LinearLayout;
.field public static mRootContainer:Landroid/widget/RelativeLayout;
.field public static rootFrame:Landroid/widget/FrameLayout;

# instance fields
.field private context:Landroid/content/Context;

# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0
    .param p1, "ctx"    # Landroid/content/Context;

    .prologue
    .line 20
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 21
    iput-object p1, p0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;

    return-void
.end method

.method private Icon()Ljava/lang/String;
    .locals 1

    .prologue
    .line 25
    const-string v0, "🎯"

    return-object v0
.end method

.method private TitleMain()Ljava/lang/String;
    .locals 1

    .prologue
    .line 30
    const-string v0, "PMM Injector"

    return-object v0
.end method

.method private TitleOnn()Ljava/lang/String;
    .locals 1

    .prologue
    .line 35
    const-string v0, "Status: Ready"

    return-object v0
.end method

.method private P2077KNG()Ljava/lang/String;
    .locals 1

    .prologue
    .line 40
    const-string v0, "PMM by River"

    return-object v0
.end method

.method public createFloatingInterface()V
    .locals 15

    .prologue
    .line 45
    move-object v0, p0

    # Create root frame
    new-instance v1, Landroid/widget/FrameLayout;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V
    sput-object v1, Lcom/projectvb/PMMInterface;->rootFrame:Landroid/widget/FrameLayout;

    # Create root container
    new-instance v1, Landroid/widget/RelativeLayout;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V
    sput-object v1, Lcom/projectvb/PMMInterface;->mRootContainer:Landroid/widget/RelativeLayout;

    # Create collapsed view
    new-instance v1, Landroid/widget/LinearLayout;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V
    sput-object v1, Lcom/projectvb/PMMInterface;->mCollapsed:Landroid/widget/LinearLayout;

    # Create expanded view
    new-instance v1, Landroid/widget/LinearLayout;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V
    sput-object v1, Lcom/projectvb/PMMInterface;->mExpanded:Landroid/widget/LinearLayout;

    # Create inject button
    new-instance v1, Landroid/widget/Button;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V
    sput-object v1, Lcom/projectvb/PMMInterface;->Bypass:Landroid/widget/Button;

    # Set button text
    sget-object v1, Lcom/projectvb/PMMInterface;->Bypass:Landroid/widget/Button;
    const-string v2, "INJECT/LOAD"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Set button click listener
    sget-object v1, Lcom/projectvb/PMMInterface;->Bypass:Landroid/widget/Button;
    new-instance v2, Lcom/projectvb/PMMInterface$1;
    invoke-direct {v2, v0}, Lcom/projectvb/PMMInterface$1;-><init>(Lcom/projectvb/PMMInterface;)V
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    # Add features to expanded view
    invoke-direct {v0}, Lcom/projectvb/PMMInterface;->addFeatures()V

    return-void
.end method

.method private addFeatures()V
    .locals 10

    .prologue
    .line 100
    move-object v0, p0

    # Add ESP feature
    invoke-direct {v0}, Lcom/projectvb/PMMInterface;->addESPFeature()V

    # Add Aimbot feature
    invoke-direct {v0}, Lcom/projectvb/PMMInterface;->addAimbotFeature()V

    # Add Speed feature
    invoke-direct {v0}, Lcom/projectvb/PMMInterface;->addSpeedFeature()V

    return-void
.end method

.method private addESPFeature()V
    .locals 5

    .prologue
    .line 110
    move-object v0, p0

    # Create ESP button
    new-instance v1, Landroid/widget/Button;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set ESP button text
    const-string v2, "ESP: OFF"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Add to expanded view
    sget-object v2, Lcom/projectvb/PMMInterface;->mExpanded:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addAimbotFeature()V
    .locals 5

    .prologue
    .line 125
    move-object v0, p0

    # Create Aimbot button
    new-instance v1, Landroid/widget/Button;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set Aimbot button text
    const-string v2, "Aimbot: OFF"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Add to expanded view
    sget-object v2, Lcom/projectvb/PMMInterface;->mExpanded:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSpeedFeature()V
    .locals 5

    .prologue
    .line 140
    move-object v0, p0

    # Create Speed button
    new-instance v1, Landroid/widget/Button;
    iget-object v2, v0, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    invoke-direct {v1, v2}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    # Set Speed button text
    const-string v2, "Speed: OFF"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Add to expanded view
    sget-object v2, Lcom/projectvb/PMMInterface;->mExpanded:Landroid/widget/LinearLayout;
    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method
