.class Leu/chainfire/libsuperuser/Shell$Interactive$6;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->open()Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V
    .locals 4

    .prologue
    .line 2084
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onStreamClosed()V
    .locals 14

    .prologue
    .line 2087
    move-object v0, p0

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2500(Leu/chainfire/libsuperuser/Shell$Interactive;)Z

    move-result v7

    if-nez v7, :cond_0

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->isRunning()Z

    move-result v7

    if-nez v7, :cond_7

    .line 2088
    :cond_0
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2600(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v7

    if-eqz v7, :cond_1

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v7

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v8

    if-ne v7, v8, :cond_1

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2600(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v7

    invoke-virtual {v7}, Leu/chainfire/libsuperuser/StreamGobbler;->resumeGobbling()V

    .line 2089
    :cond_1
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v7

    if-eqz v7, :cond_2

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v7

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2600(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v8

    if-ne v7, v8, :cond_2

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v7

    invoke-virtual {v7}, Leu/chainfire/libsuperuser/StreamGobbler;->resumeGobbling()V

    .line 2092
    :cond_2
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2800(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/lang/Object;

    move-result-object v7

    move-object v13, v7

    move-object v7, v13

    move-object v8, v13

    move-object v2, v8

    monitor-enter v7

    .line 2093
    :try_start_0
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v7

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2700(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v8

    if-ne v7, v8, :cond_3

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    const/4 v8, 0x1

    invoke-static {v7, v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2902(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z

    move-result v7

    .line 2094
    :cond_3
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v7

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2600(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler;

    move-result-object v8

    if-ne v7, v8, :cond_4

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    const/4 v8, 0x1

    invoke-static {v7, v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3002(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z

    move-result v7

    .line 2095
    :cond_4
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2900(Leu/chainfire/libsuperuser/Shell$Interactive;)Z

    move-result v7

    if-eqz v7, :cond_8

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3000(Leu/chainfire/libsuperuser/Shell$Interactive;)Z

    move-result v7

    if-eqz v7, :cond_8

    const/4 v7, 0x1

    :goto_0
    move v1, v7

    .line 2097
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v7

    move-object v3, v7

    .line 2098
    move-object v7, v3

    if-eqz v7, :cond_5

    .line 2099
    move-object v7, v3

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2100(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/MarkerInputStream;

    move-result-object v7

    move-object v4, v7

    .line 2100
    move-object v7, v4

    if-eqz v7, :cond_5

    .line 2101
    move-object v7, v4

    invoke-virtual {v7}, Leu/chainfire/libsuperuser/MarkerInputStream;->setEOF()V

    .line 2104
    :cond_5
    move-object v7, v2

    monitor-exit v7
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2106
    move v7, v1

    if-eqz v7, :cond_7

    .line 2107
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3200(Leu/chainfire/libsuperuser/Shell$Interactive;)Z

    move-result v7

    .line 2109
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v13, v7

    move-object v7, v13

    move-object v8, v13

    move-object v2, v8

    monitor-enter v7

    .line 2111
    move-object v7, v0

    :try_start_1
    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v7

    if-eqz v7, :cond_6

    .line 2113
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v8

    const/4 v9, -0x2

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v10}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3300(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;

    move-result-object v10

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v11}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3400(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static/range {v7 .. v12}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3500(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z

    move-result v7

    .line 2114
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    const/4 v8, 0x0

    invoke-static {v7, v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3102(Leu/chainfire/libsuperuser/Shell$Interactive;Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v7

    .line 2116
    :cond_6
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    const/4 v8, 0x1

    iput-boolean v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive;->closed:Z

    .line 2117
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    const/4 v8, 0x0

    invoke-static {v7, v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3602(Leu/chainfire/libsuperuser/Shell$Interactive;Z)Z

    move-result v7

    .line 2118
    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$6;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3700(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    .line 2119
    move-object v7, v2

    monitor-exit v7
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 2122
    :cond_7
    return-void

    .line 2095
    :cond_8
    const/4 v7, 0x0

    goto :goto_0

    .line 2104
    :catchall_0
    move-exception v7

    move-object v5, v7

    move-object v7, v2

    :try_start_2
    monitor-exit v7
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-object v7, v5

    throw v7

    .line 2119
    :catchall_1
    move-exception v7

    move-object v6, v7

    move-object v7, v2

    :try_start_3
    monitor-exit v7
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    move-object v7, v6

    throw v7
.end method
