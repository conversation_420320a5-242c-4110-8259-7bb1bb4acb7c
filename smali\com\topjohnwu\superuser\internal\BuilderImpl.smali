.class public Lcom/topjohnwu/superuser/internal/BuilderImpl;
.super Lcom/topjohnwu/superuser/Shell$Builder;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/topjohnwu/superuser/Shell$Builder;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic build()Lcom/topjohnwu/superuser/Shell;
    .locals 1

    invoke-virtual {p0}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build()Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic build([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell;
    .locals 0

    invoke-virtual {p0, p1}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build([Ljava/lang/String;)Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object p1

    return-object p1
.end method

.method public build()Lcom/topjohnwu/superuser/internal/ShellImpl;
    .locals 6

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->hasFlags(I)Z

    move-result v1

    const-string v2, "su"

    const/4 v3, 0x0

    if-nez v1, :cond_0

    const/4 v1, 0x2

    invoke-virtual {p0, v1}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->hasFlags(I)Z

    move-result v4

    if-eqz v4, :cond_0

    :try_start_0
    const-string v4, "--mount-master"

    filled-new-array {v2, v4}, [Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build([Ljava/lang/String;)Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v4
    :try_end_0
    .catch Lcom/topjohnwu/superuser/NoShellException; {:try_start_0 .. :try_end_0} :catch_1

    :try_start_1
    invoke-virtual {v4}, Lcom/topjohnwu/superuser/internal/ShellImpl;->getStatus()I

    move-result v5
    :try_end_1
    .catch Lcom/topjohnwu/superuser/NoShellException; {:try_start_1 .. :try_end_1} :catch_0

    if-eq v5, v1, :cond_1

    goto :goto_0

    :catch_0
    move-exception v1

    goto :goto_1

    :catch_1
    move-exception v1

    :cond_0
    :goto_0
    move-object v4, v3

    :cond_1
    :goto_1
    if-nez v4, :cond_3

    invoke-virtual {p0, v0}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->hasFlags(I)Z

    move-result v1

    if-nez v1, :cond_3

    :try_start_2
    filled-new-array {v2}, [Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build([Ljava/lang/String;)Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v4

    invoke-virtual {v4}, Lcom/topjohnwu/superuser/internal/ShellImpl;->getStatus()I

    move-result v1
    :try_end_2
    .catch Lcom/topjohnwu/superuser/NoShellException; {:try_start_2 .. :try_end_2} :catch_2

    if-eq v1, v0, :cond_2

    goto :goto_2

    :cond_2
    move-object v3, v4

    :goto_2
    move-object v4, v3

    goto :goto_3

    :catch_2
    move-exception v0

    :cond_3
    :goto_3
    if-nez v4, :cond_4

    const-string v0, "sh"

    filled-new-array {v0}, [Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->build([Ljava/lang/String;)Lcom/topjohnwu/superuser/internal/ShellImpl;

    move-result-object v4

    :cond_4
    return-object v4
.end method

.method public varargs build([Ljava/lang/String;)Lcom/topjohnwu/superuser/internal/ShellImpl;
    .locals 7

    :try_start_0
    new-instance v0, Lcom/topjohnwu/superuser/internal/ShellImpl;

    iget-wide v1, p0, Lcom/topjohnwu/superuser/internal/BuilderImpl;->timeout:J

    const/16 v3, 0x8

    invoke-virtual {p0, v3}, Lcom/topjohnwu/superuser/internal/BuilderImpl;->hasFlags(I)Z

    move-result v3

    invoke-direct {v0, v1, v2, v3, p1}, Lcom/topjohnwu/superuser/internal/ShellImpl;-><init>(JZ[Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1

    invoke-static {v0}, Lcom/topjohnwu/superuser/internal/MainShell;->set(Lcom/topjohnwu/superuser/internal/ShellImpl;)V

    iget-object p1, p0, Lcom/topjohnwu/superuser/internal/BuilderImpl;->initClasses:[Ljava/lang/Class;

    if-eqz p1, :cond_1

    invoke-static {}, Lcom/topjohnwu/superuser/internal/Utils;->getContext()Landroid/content/Context;

    move-result-object p1

    iget-object v1, p0, Lcom/topjohnwu/superuser/internal/BuilderImpl;->initClasses:[Ljava/lang/Class;

    array-length v2, v1

    const/4 v3, 0x0

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v2, :cond_1

    aget-object v5, v1, v4

    :try_start_1
    new-array v6, v3, [Ljava/lang/Class;

    invoke-virtual {v5, v6}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v5

    const/4 v6, 0x1

    invoke-virtual {v5, v6}, Ljava/lang/reflect/Constructor;->setAccessible(Z)V

    new-array v6, v3, [Ljava/lang/Object;

    invoke-virtual {v5, v6}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/topjohnwu/superuser/Shell$Initializer;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    invoke-virtual {v5, p1, v0}, Lcom/topjohnwu/superuser/Shell$Initializer;->onInit(Landroid/content/Context;Lcom/topjohnwu/superuser/Shell;)Z

    move-result v5

    if-eqz v5, :cond_0

    goto :goto_1

    :cond_0
    const/4 p1, 0x0

    invoke-static {p1}, Lcom/topjohnwu/superuser/internal/MainShell;->set(Lcom/topjohnwu/superuser/internal/ShellImpl;)V

    new-instance p1, Lcom/topjohnwu/superuser/NoShellException;

    const-string v0, "Unable to init shell"

    invoke-direct {p1, v0}, Lcom/topjohnwu/superuser/NoShellException;-><init>(Ljava/lang/String;)V

    throw p1

    :catch_0
    move-exception v5

    invoke-static {v5}, Lcom/topjohnwu/superuser/internal/Utils;->err(Ljava/lang/Throwable;)V

    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_1
    return-object v0

    :catch_1
    move-exception p1

    invoke-static {p1}, Lcom/topjohnwu/superuser/internal/Utils;->ex(Ljava/lang/Throwable;)V

    new-instance v0, Lcom/topjohnwu/superuser/NoShellException;

    const-string v1, "Unable to create a shell!"

    invoke-direct {v0, v1, p1}, Lcom/topjohnwu/superuser/NoShellException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    goto :goto_3

    :goto_2
    throw v0

    :goto_3
    goto :goto_2
.end method

.method hasFlags(I)Z
    .locals 1

    iget v0, p0, Lcom/topjohnwu/superuser/internal/BuilderImpl;->flags:I

    and-int/2addr v0, p1

    if-ne v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method
