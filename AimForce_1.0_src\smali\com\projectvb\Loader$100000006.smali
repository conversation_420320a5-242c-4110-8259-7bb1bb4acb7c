.class Lpmm/by/p2077kng/hacker/Loader$100000006;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000006"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000006;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000006;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000006;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 384
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000006;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    const/4 v4, 0x0

    invoke-virtual {v3, v4}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 385
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000006;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    const/16 v4, 0x8

    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    return-void
.end method
