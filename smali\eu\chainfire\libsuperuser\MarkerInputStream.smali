.class public Leu/chainfire/libsuperuser/MarkerInputStream;
.super Ljava/io/InputStream;
.source "MarkerInputStream.java"


# annotations
.annotation build Landroidx/annotation/AnyThread;
.end annotation


# static fields
.field private static final EXCEPTION_EOF:Ljava/lang/String; = "EOF encountered, shell probably died"


# instance fields
.field private final buffer:[B

.field private bufferUsed:I

.field private volatile done:Z

.field private volatile eof:Z

.field private final gobbler:Leu/chainfire/libsuperuser/StreamGobbler;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private final inputStream:Ljava/io/InputStream;

.field private final marker:[B

.field private final markerLength:I

.field private final markerMaxLength:I

.field private final read1:[B


# direct methods
.method public constructor <init>(Leu/chainfire/libsuperuser/StreamGobbler;Ljava/lang/String;)V
    .locals 6
    .param p1    # Leu/chainfire/libsuperuser/StreamGobbler;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/UnsupportedEncodingException;
        }
    .end annotation

    .prologue
    .line 44
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, v0

    invoke-direct {v3}, Ljava/io/InputStream;-><init>()V

    .line 38
    move-object v3, v0

    const/4 v4, 0x1

    new-array v4, v4, [B

    iput-object v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->read1:[B

    .line 39
    move-object v3, v0

    const/high16 v4, 0x10000

    new-array v4, v4, [B

    iput-object v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    .line 40
    move-object v3, v0

    const/4 v4, 0x0

    iput v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    .line 41
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->eof:Z

    .line 42
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->done:Z

    .line 45
    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->gobbler:Leu/chainfire/libsuperuser/StreamGobbler;

    .line 46
    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->gobbler:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v3}, Leu/chainfire/libsuperuser/StreamGobbler;->suspendGobbling()V

    .line 47
    move-object v3, v0

    move-object v4, v1

    invoke-virtual {v4}, Leu/chainfire/libsuperuser/StreamGobbler;->getInputStream()Ljava/io/InputStream;

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->inputStream:Ljava/io/InputStream;

    .line 48
    move-object v3, v0

    move-object v4, v2

    const-string v5, "UTF-8"

    invoke-virtual {v4, v5}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v4

    iput-object v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->marker:[B

    .line 49
    move-object v3, v0

    move-object v4, v2

    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v4

    iput v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->markerLength:I

    .line 50
    move-object v3, v0

    move-object v4, v2

    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v4

    const/4 v5, 0x5

    add-int/lit8 v4, v4, 0x5

    iput v4, v3, Leu/chainfire/libsuperuser/MarkerInputStream;->markerMaxLength:I

    .line 51
    return-void
.end method

.method private fill(I)V
    .locals 12

    .prologue
    .line 78
    move-object v0, p0

    move v1, p1

    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/MarkerInputStream;->isEOF()Z

    move-result v5

    if-eqz v5, :cond_1

    .line 99
    :goto_0
    return-void

    .line 84
    :cond_0
    move-object v5, v0

    :try_start_0
    iget-object v5, v5, Leu/chainfire/libsuperuser/MarkerInputStream;->inputStream:Ljava/io/InputStream;

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    move-object v7, v0

    iget v7, v7, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move v8, v1

    move v9, v2

    move v10, v3

    invoke-static {v9, v10}, Ljava/lang/Math;->min(II)I

    move-result v9

    invoke-static {v8, v9}, Ljava/lang/Math;->max(II)I

    move-result v8

    invoke-virtual {v5, v6, v7, v8}, Ljava/io/InputStream;->read([BII)I

    move-result v5

    move v4, v5

    .line 85
    move v5, v4

    if-ltz v5, :cond_3

    .line 86
    move-object v5, v0

    move-object v11, v5

    move-object v5, v11

    move-object v6, v11

    iget v6, v6, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move v7, v4

    add-int/2addr v6, v7

    iput v6, v5, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    .line 87
    move v5, v1

    move v6, v4

    sub-int/2addr v5, v6

    move v1, v5

    .line 81
    :cond_1
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/MarkerInputStream;->inputStream:Ljava/io/InputStream;

    invoke-virtual {v5}, Ljava/io/InputStream;->available()I

    move-result v5

    move v11, v5

    move v5, v11

    move v6, v11

    move v2, v6

    if-gtz v5, :cond_2

    move v5, v1

    if-lez v5, :cond_4

    .line 82
    :cond_2
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    array-length v5, v5

    move-object v6, v0

    iget v6, v6, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    sub-int/2addr v5, v6

    move v3, v5

    .line 83
    move v5, v3

    if-nez v5, :cond_0

    goto :goto_0

    .line 92
    :cond_3
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/MarkerInputStream;->setEOF()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 99
    :cond_4
    :goto_1
    goto :goto_0

    .line 96
    :catch_0
    move-exception v5

    move-object v2, v5

    .line 97
    move-object v5, v0

    invoke-virtual {v5}, Leu/chainfire/libsuperuser/MarkerInputStream;->setEOF()V

    goto :goto_1
.end method


# virtual methods
.method public declared-synchronized close()V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 171
    move-object v0, p0

    move-object v4, p0

    monitor-enter v4

    move-object v2, v0

    :try_start_0
    invoke-virtual {v2}, Leu/chainfire/libsuperuser/MarkerInputStream;->isEOF()Z

    move-result v2

    if-nez v2, :cond_0

    move-object v2, v0

    iget-boolean v2, v2, Leu/chainfire/libsuperuser/MarkerInputStream;->done:Z

    if-nez v2, :cond_0

    .line 173
    const/16 v2, 0x400

    new-array v2, v2, [B

    move-object v1, v2

    .line 174
    :goto_0
    move-object v2, v0

    move-object v3, v1

    invoke-virtual {v2, v3}, Leu/chainfire/libsuperuser/MarkerInputStream;->read([B)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v2

    if-ltz v2, :cond_0

    goto :goto_0

    .line 177
    :cond_0
    monitor-exit v4

    return-void

    .line 171
    :catchall_0
    move-exception v0

    monitor-exit v4

    throw v0
.end method

.method public declared-synchronized isEOF()Z
    .locals 3

    .prologue
    .line 180
    move-object v0, p0

    move-object v2, p0

    monitor-enter v2

    move-object v1, v0

    :try_start_0
    iget-boolean v1, v1, Leu/chainfire/libsuperuser/MarkerInputStream;->eof:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move v0, v1

    monitor-exit v2

    return v0

    :catchall_0
    move-exception v0

    monitor-exit v2

    throw v0
.end method

.method public read()I
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 56
    move-object v1, p0

    :goto_0
    move-object v4, v1

    move-object v5, v1

    iget-object v5, v5, Leu/chainfire/libsuperuser/MarkerInputStream;->read1:[B

    const/4 v6, 0x0

    const/4 v7, 0x1

    invoke-virtual {v4, v5, v6, v7}, Leu/chainfire/libsuperuser/MarkerInputStream;->read([BII)I

    move-result v4

    move v2, v4

    .line 57
    move v4, v2

    if-gez v4, :cond_0

    const/4 v4, -0x1

    move v1, v4

    .line 67
    :goto_1
    return v1

    .line 58
    :cond_0
    move v4, v2

    if-nez v4, :cond_1

    .line 61
    const-wide/16 v4, 0x10

    :try_start_0
    invoke-static {v4, v5}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 64
    goto :goto_0

    .line 62
    :catch_0
    move-exception v4

    move-object v3, v4

    .line 65
    goto :goto_0

    .line 67
    :cond_1
    move-object v4, v1

    iget-object v4, v4, Leu/chainfire/libsuperuser/MarkerInputStream;->read1:[B

    const/4 v5, 0x0

    aget-byte v4, v4, v5

    const/16 v5, 0xff

    and-int/lit16 v4, v4, 0xff

    move v1, v4

    goto :goto_1
.end method

.method public read([B)I
    .locals 6
    .param p1    # [B
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 73
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    move-object v5, v1

    array-length v5, v5

    invoke-virtual {v2, v3, v4, v5}, Leu/chainfire/libsuperuser/MarkerInputStream;->read([BII)I

    move-result v2

    move v0, v2

    return v0
.end method

.method public declared-synchronized read([BII)I
    .locals 17
    .param p1    # [B
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 103
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object/from16 v15, p0

    monitor-enter v15

    move-object v8, v0

    :try_start_0
    iget-boolean v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->done:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v8, :cond_0

    const/4 v8, -0x1

    move v0, v8

    .line 164
    :goto_0
    monitor-exit v15

    return v0

    .line 105
    :cond_0
    move-object v8, v0

    move-object v9, v0

    :try_start_1
    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->markerLength:I

    move-object v10, v0

    iget v10, v10, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    sub-int/2addr v9, v10

    invoke-direct {v8, v9}, Leu/chainfire/libsuperuser/MarkerInputStream;->fill(I)V

    .line 108
    move-object v8, v0

    iget v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move-object v9, v0

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->markerLength:I

    if-ge v8, v9, :cond_1

    const/4 v8, 0x0

    move v0, v8

    goto :goto_0

    .line 111
    :cond_1
    const/4 v8, -0x1

    move v4, v8

    .line 112
    const/4 v8, 0x0

    move-object v9, v0

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move-object v10, v0

    iget v10, v10, Leu/chainfire/libsuperuser/MarkerInputStream;->markerMaxLength:I

    sub-int/2addr v9, v10

    invoke-static {v8, v9}, Ljava/lang/Math;->max(II)I

    move-result v8

    move v5, v8

    :goto_1
    move v8, v5

    move-object v9, v0

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move-object v10, v0

    iget v10, v10, Leu/chainfire/libsuperuser/MarkerInputStream;->markerLength:I

    sub-int/2addr v9, v10

    if-ge v8, v9, :cond_3

    .line 113
    const/4 v8, 0x1

    move v6, v8

    .line 114
    const/4 v8, 0x0

    move v7, v8

    :goto_2
    move v8, v7

    move-object v9, v0

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->markerLength:I

    if-ge v8, v9, :cond_2

    .line 115
    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    move v9, v5

    move v10, v7

    add-int/2addr v9, v10

    aget-byte v8, v8, v9

    move-object v9, v0

    iget-object v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->marker:[B

    move v10, v7

    aget-byte v9, v9, v10

    if-eq v8, v9, :cond_4

    .line 116
    const/4 v8, 0x0

    move v6, v8

    .line 120
    :cond_2
    move v8, v6

    if-eqz v8, :cond_5

    .line 121
    move v8, v5

    move v4, v8

    .line 126
    :cond_3
    move v8, v4

    if-nez v8, :cond_9

    .line 128
    :goto_3
    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    move-object v9, v0

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    const/4 v10, 0x1

    add-int/lit8 v9, v9, -0x1

    aget-byte v8, v8, v9

    const/16 v9, 0xa

    if-eq v8, v9, :cond_7

    .line 129
    move-object v8, v0

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/MarkerInputStream;->isEOF()Z

    move-result v8

    if-eqz v8, :cond_6

    new-instance v8, Ljava/io/IOException;

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    const-string v10, "EOF encountered, shell probably died"

    invoke-direct {v9, v10}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v8
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 103
    :catchall_0
    move-exception v0

    monitor-exit v15

    throw v0

    .line 114
    :cond_4
    add-int/lit8 v7, v7, 0x1

    goto :goto_2

    .line 112
    :cond_5
    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    .line 130
    :cond_6
    move-object v8, v0

    const/4 v9, 0x1

    :try_start_2
    invoke-direct {v8, v9}, Leu/chainfire/libsuperuser/MarkerInputStream;->fill(I)V

    goto :goto_3

    .line 132
    :cond_7
    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->gobbler:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/StreamGobbler;->getOnLineListener()Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-result-object v8

    if-eqz v8, :cond_8

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->gobbler:Leu/chainfire/libsuperuser/StreamGobbler;

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/StreamGobbler;->getOnLineListener()Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-result-object v8

    new-instance v9, Ljava/lang/String;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    move-object v11, v0

    iget-object v11, v11, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    const/4 v12, 0x0

    move-object v13, v0

    iget v13, v13, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    const/4 v14, 0x1

    add-int/lit8 v13, v13, -0x1

    const-string v14, "UTF-8"

    invoke-direct {v10, v11, v12, v13, v14}, Ljava/lang/String;-><init>([BIILjava/lang/String;)V

    invoke-interface {v8, v9}, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;->onLine(Ljava/lang/String;)V

    .line 133
    :cond_8
    move-object v8, v0

    const/4 v9, 0x1

    iput-boolean v9, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->done:Z

    .line 134
    const/4 v8, -0x1

    move v0, v8

    goto/16 :goto_0

    .line 137
    :cond_9
    move v8, v4

    const/4 v9, -0x1

    if-ne v8, v9, :cond_b

    .line 138
    move-object v8, v0

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/MarkerInputStream;->isEOF()Z

    move-result v8

    if-eqz v8, :cond_a

    new-instance v8, Ljava/io/IOException;

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    const-string v10, "EOF encountered, shell probably died"

    invoke-direct {v9, v10}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v8

    .line 143
    :cond_a
    move v8, v3

    move-object v9, v0

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move-object v10, v0

    iget v10, v10, Leu/chainfire/libsuperuser/MarkerInputStream;->markerMaxLength:I

    sub-int/2addr v9, v10

    invoke-static {v8, v9}, Ljava/lang/Math;->min(II)I

    move-result v8

    move v5, v8

    .line 152
    :goto_4
    move v8, v5

    if-lez v8, :cond_c

    .line 153
    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    const/4 v9, 0x0

    move-object v10, v1

    move v11, v2

    move v12, v5

    invoke-static {v8, v9, v10, v11, v12}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 154
    move-object v8, v0

    move-object/from16 v16, v8

    move-object/from16 v8, v16

    move-object/from16 v9, v16

    iget v9, v9, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    move v10, v5

    sub-int/2addr v9, v10

    iput v9, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    .line 155
    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    move v9, v5

    move-object v10, v0

    iget-object v10, v10, Leu/chainfire/libsuperuser/MarkerInputStream;->buffer:[B

    const/4 v11, 0x0

    move-object v12, v0

    iget v12, v12, Leu/chainfire/libsuperuser/MarkerInputStream;->bufferUsed:I

    invoke-static {v8, v9, v10, v11, v12}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 164
    :goto_5
    move v8, v5

    move v0, v8

    goto/16 :goto_0

    .line 150
    :cond_b
    move v8, v3

    move v9, v4

    invoke-static {v8, v9}, Ljava/lang/Math;->min(II)I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-result v8

    move v5, v8

    goto :goto_4

    .line 159
    :cond_c
    const-wide/16 v8, 0x4

    :try_start_3
    invoke-static {v8, v9}, Ljava/lang/Thread;->sleep(J)V
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 162
    goto :goto_5

    .line 160
    :catch_0
    move-exception v8

    move-object v6, v8

    goto :goto_5
.end method

.method public declared-synchronized setEOF()V
    .locals 4

    .prologue
    .line 184
    move-object v0, p0

    move-object v3, p0

    monitor-enter v3

    move-object v1, v0

    const/4 v2, 0x1

    :try_start_0
    iput-boolean v2, v1, Leu/chainfire/libsuperuser/MarkerInputStream;->eof:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 185
    monitor-exit v3

    return-void

    .line 184
    :catchall_0
    move-exception v0

    monitor-exit v3

    throw v0
.end method
