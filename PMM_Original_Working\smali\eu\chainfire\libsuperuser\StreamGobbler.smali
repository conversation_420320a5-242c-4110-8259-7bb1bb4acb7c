.class public Leu/chainfire/libsuperuser/StreamGobbler;
.super Ljava/lang/Thread;
.source "StreamGobbler.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;,
        Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    }
.end annotation


# static fields
.field private static threadCounter:I


# instance fields
.field private volatile active:Z

.field private final inputStream:Ljava/io/InputStream;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private final lineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final reader:Ljava/io/BufferedReader;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private final shell:Ljava/lang/String;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private final streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final writer:Ljava/util/List;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .prologue
    .line 36
    const/4 v0, 0x0

    sput v0, Leu/chainfire/libsuperuser/StreamGobbler;->threadCounter:I

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/io/InputStream;Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;)V
    .locals 12
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/io/InputStream;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 121
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object/from16 v4, p4

    move-object v5, v0

    new-instance v6, Ljava/lang/StringBuilder;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "Gobbler#"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-static {}, Leu/chainfire/libsuperuser/StreamGobbler;->incThreadCounter()I

    move-result v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v5, v6}, Ljava/lang/Thread;-><init>(Ljava/lang/String;)V

    .line 83
    move-object v5, v0

    const/4 v6, 0x1

    iput-boolean v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    .line 122
    move-object v5, v0

    move-object v6, v1

    iput-object v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->shell:Ljava/lang/String;

    .line 123
    move-object v5, v0

    move-object v6, v2

    iput-object v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->inputStream:Ljava/io/InputStream;

    .line 124
    move-object v5, v0

    new-instance v6, Ljava/io/BufferedReader;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    new-instance v8, Ljava/io/InputStreamReader;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    move-object v10, v2

    invoke-direct {v9, v10}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v7, v8}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    iput-object v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->reader:Ljava/io/BufferedReader;

    .line 125
    move-object v5, v0

    move-object v6, v3

    iput-object v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->lineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 126
    move-object v5, v0

    move-object v6, v4

    iput-object v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;

    .line 127
    move-object v5, v0

    const/4 v6, 0x0

    iput-object v6, v5, Leu/chainfire/libsuperuser/StreamGobbler;->writer:Ljava/util/List;

    .line 128
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/io/InputStream;Ljava/util/List;)V
    .locals 11
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/io/InputStream;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/io/InputStream;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .prologue
    .line 98
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, v0

    new-instance v5, Ljava/lang/StringBuilder;

    move-object v10, v5

    move-object v5, v10

    move-object v6, v10

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Gobbler#"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-static {}, Leu/chainfire/libsuperuser/StreamGobbler;->incThreadCounter()I

    move-result v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/lang/Thread;-><init>(Ljava/lang/String;)V

    .line 83
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    .line 99
    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->shell:Ljava/lang/String;

    .line 100
    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->inputStream:Ljava/io/InputStream;

    .line 101
    move-object v4, v0

    new-instance v5, Ljava/io/BufferedReader;

    move-object v10, v5

    move-object v5, v10

    move-object v6, v10

    new-instance v7, Ljava/io/InputStreamReader;

    move-object v10, v7

    move-object v7, v10

    move-object v8, v10

    move-object v9, v2

    invoke-direct {v8, v9}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v6, v7}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    iput-object v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->reader:Ljava/io/BufferedReader;

    .line 102
    move-object v4, v0

    move-object v5, v3

    iput-object v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->writer:Ljava/util/List;

    .line 103
    move-object v4, v0

    const/4 v5, 0x0

    iput-object v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->lineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 104
    move-object v4, v0

    const/4 v5, 0x0

    iput-object v5, v4, Leu/chainfire/libsuperuser/StreamGobbler;->streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;

    .line 105
    return-void
.end method

.method private static incThreadCounter()I
    .locals 6

    .prologue
    .line 38
    const-class v3, Leu/chainfire/libsuperuser/StreamGobbler;

    move-object v5, v3

    move-object v3, v5

    move-object v4, v5

    move-object v0, v4

    monitor-enter v3

    .line 39
    :try_start_0
    sget v3, Leu/chainfire/libsuperuser/StreamGobbler;->threadCounter:I

    move v1, v3

    .line 40
    sget v3, Leu/chainfire/libsuperuser/StreamGobbler;->threadCounter:I

    const/4 v4, 0x1

    add-int/lit8 v3, v3, 0x1

    sput v3, Leu/chainfire/libsuperuser/StreamGobbler;->threadCounter:I

    .line 41
    move v3, v1

    move-object v4, v0

    monitor-exit v4

    move v0, v3

    return v0

    .line 42
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v0

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method


# virtual methods
.method public getInputStream()Ljava/io/InputStream;
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 237
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/StreamGobbler;->inputStream:Ljava/io/InputStream;

    move-object v0, v1

    return-object v0
.end method

.method public getOnLineListener()Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .prologue
    .line 248
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/StreamGobbler;->lineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v0, v1

    return-object v0
.end method

.method public isSuspended()Z
    .locals 6
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 224
    move-object v0, p0

    move-object v3, v0

    move-object v5, v3

    move-object v3, v5

    move-object v4, v5

    move-object v1, v4

    monitor-enter v3

    .line 225
    move-object v3, v0

    :try_start_0
    iget-boolean v3, v3, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    if-nez v3, :cond_0

    const/4 v3, 0x1

    :goto_0
    move-object v4, v1

    monitor-exit v4

    move v0, v3

    return v0

    :cond_0
    const/4 v3, 0x0

    goto :goto_0

    .line 226
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v1

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method

.method public resumeGobbling()V
    .locals 6
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 178
    move-object v0, p0

    move-object v3, v0

    iget-boolean v3, v3, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    if-nez v3, :cond_0

    .line 179
    move-object v3, v0

    move-object v5, v3

    move-object v3, v5

    move-object v4, v5

    move-object v1, v4

    monitor-enter v3

    .line 180
    move-object v3, v0

    const/4 v4, 0x1

    :try_start_0
    iput-boolean v4, v3, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    .line 181
    move-object v3, v0

    invoke-virtual {v3}, Ljava/lang/Object;->notifyAll()V

    .line 182
    move-object v3, v1

    monitor-exit v3

    .line 184
    :cond_0
    return-void

    .line 182
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v1

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method

.method public run()V
    .locals 14

    .prologue
    .line 134
    move-object v1, p0

    const/4 v7, 0x0

    move v2, v7

    .line 137
    :cond_0
    move-object v7, v1

    :try_start_0
    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->reader:Ljava/io/BufferedReader;

    invoke-virtual {v7}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v7

    move-object v13, v7

    move-object v7, v13

    move-object v8, v13

    move-object v3, v8

    if-eqz v7, :cond_5

    .line 138
    sget-object v7, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v8, "[%s] %s"

    const/4 v9, 0x2

    new-array v9, v9, [Ljava/lang/Object;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    const/4 v11, 0x0

    move-object v12, v1

    iget-object v12, v12, Leu/chainfire/libsuperuser/StreamGobbler;->shell:Ljava/lang/String;

    aput-object v12, v10, v11

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    const/4 v11, 0x1

    move-object v12, v3

    aput-object v12, v10, v11

    invoke-static {v7, v8, v9}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Leu/chainfire/libsuperuser/Debug;->logOutput(Ljava/lang/String;)V

    .line 139
    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->writer:Ljava/util/List;

    if-eqz v7, :cond_1

    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->writer:Ljava/util/List;

    move-object v8, v3

    invoke-interface {v7, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-result v7

    .line 140
    :cond_1
    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->lineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    if-eqz v7, :cond_2

    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->lineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v8, v3

    invoke-interface {v7, v8}, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;->onLine(Ljava/lang/String;)V

    .line 141
    :cond_2
    :goto_0
    move-object v7, v1

    iget-boolean v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    if-nez v7, :cond_0

    .line 142
    move-object v7, v1

    move-object v13, v7

    move-object v7, v13

    move-object v8, v13

    move-object v4, v8

    monitor-enter v7
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1

    .line 144
    move-object v7, v1

    const-wide/16 v8, 0x80

    :try_start_1
    invoke-virtual {v7, v8, v9}, Ljava/lang/Object;->wait(J)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 148
    :goto_1
    move-object v7, v4

    :try_start_2
    monitor-exit v7

    goto :goto_0

    .line 145
    :catch_0
    move-exception v7

    move-object v5, v7

    goto :goto_1

    .line 148
    :catchall_0
    move-exception v7

    move-object v6, v7

    move-object v7, v4

    monitor-exit v7
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-object v7, v6

    :try_start_3
    throw v7
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_1

    .line 151
    :catch_1
    move-exception v7

    move-object v3, v7

    .line 153
    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;

    if-eqz v7, :cond_3

    .line 154
    const/4 v7, 0x1

    move v2, v7

    .line 155
    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;

    invoke-interface {v7}, Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;->onStreamClosed()V

    .line 161
    :cond_3
    :goto_2
    move-object v7, v1

    :try_start_4
    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->reader:Ljava/io/BufferedReader;

    invoke-virtual {v7}, Ljava/io/BufferedReader;->close()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_2

    .line 166
    :goto_3
    move v7, v2

    if-nez v7, :cond_4

    .line 167
    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;

    if-eqz v7, :cond_4

    .line 168
    move-object v7, v1

    iget-object v7, v7, Leu/chainfire/libsuperuser/StreamGobbler;->streamClosedListener:Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;

    invoke-interface {v7}, Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;->onStreamClosed()V

    .line 171
    :cond_4
    return-void

    .line 157
    :cond_5
    goto :goto_2

    .line 162
    :catch_2
    move-exception v7

    move-object v3, v7

    goto :goto_3
.end method

.method public suspendGobbling()V
    .locals 6
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 193
    move-object v0, p0

    move-object v3, v0

    move-object v5, v3

    move-object v3, v5

    move-object v4, v5

    move-object v1, v4

    monitor-enter v3

    .line 194
    move-object v3, v0

    const/4 v4, 0x0

    :try_start_0
    iput-boolean v4, v3, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z

    .line 195
    move-object v3, v0

    invoke-virtual {v3}, Ljava/lang/Object;->notifyAll()V

    .line 196
    move-object v3, v1

    monitor-exit v3

    .line 197
    return-void

    .line 196
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v1

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method

.method public waitForSuspend()V
    .locals 9
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 206
    move-object v1, p0

    move-object v5, v1

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v2, v6

    monitor-enter v5

    .line 207
    :goto_0
    move-object v5, v1

    :try_start_0
    iget-boolean v5, v5, Leu/chainfire/libsuperuser/StreamGobbler;->active:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v5, :cond_0

    .line 209
    move-object v5, v1

    const-wide/16 v6, 0x20

    :try_start_1
    invoke-virtual {v5, v6, v7}, Ljava/lang/Object;->wait(J)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 212
    goto :goto_0

    .line 210
    :catch_0
    move-exception v5

    move-object v3, v5

    .line 212
    goto :goto_0

    .line 214
    :cond_0
    move-object v5, v2

    :try_start_2
    monitor-exit v5

    .line 215
    return-void

    .line 214
    :catchall_0
    move-exception v5

    move-object v4, v5

    move-object v5, v2

    monitor-exit v5
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-object v5, v4

    throw v5
.end method
