.class Lpmm/by/p2077kng/hacker/Loader$100000026;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/content/DialogInterface$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000026"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000026;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000026;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000026;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/content/DialogInterface;I)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/DialogInterface;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 1228
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000026;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/Loader;->stopSelf()V

    .line 1229
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000026;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader$100000026;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader;->frameLayout:Landroid/widget/FrameLayout;

    invoke-interface {v4, v5}, Landroid/view/WindowManager;->removeView(Landroid/view/View;)V

    return-void
.end method
