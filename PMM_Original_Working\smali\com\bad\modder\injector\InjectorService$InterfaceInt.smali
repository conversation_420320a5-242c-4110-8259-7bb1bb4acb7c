.class interface Lcom/bad/modder/injector/InjectorService$InterfaceInt;
.super Ljava/lang/Object;
.source "InjectorService.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20a
    name = "InterfaceInt"
.end annotation


# virtual methods
.method public abstract OnWrite(I)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation
.end method
