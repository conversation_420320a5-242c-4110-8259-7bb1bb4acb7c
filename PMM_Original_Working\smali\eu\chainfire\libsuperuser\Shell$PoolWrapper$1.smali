.class Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;Z)Ljava/util/List;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

.field final synthetic val$exitCode:[I

.field final synthetic val$output:Ljava/util/List;

.field final synthetic val$wantSTDERR:Z


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$PoolWrapper;[ILjava/util/List;Z)V
    .locals 7

    .prologue
    .line 3023
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v4, p4

    move-object v5, v0

    move-object v6, v1

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->this$0:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-object v5, v0

    move-object v6, v2

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$exitCode:[I

    move-object v5, v0

    move-object v6, v3

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$output:Ljava/util/List;

    move-object v5, v0

    move v6, v4

    iput-boolean v6, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$wantSTDERR:Z

    move-object v5, v0

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCommandResult(IILjava/util/List;Ljava/util/List;)V
    .locals 8
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .prologue
    .line 3026
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$exitCode:[I

    const/4 v6, 0x0

    move v7, v2

    aput v7, v5, v6

    .line 3027
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$output:Ljava/util/List;

    move-object v6, v3

    invoke-interface {v5, v6}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    move-result v5

    .line 3028
    move-object v5, v0

    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$wantSTDERR:Z

    if-eqz v5, :cond_0

    .line 3029
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;->val$output:Ljava/util/List;

    move-object v6, v4

    invoke-interface {v5, v6}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    move-result v5

    .line 3031
    :cond_0
    return-void
.end method
