.class public abstract Leu/chainfire/libsuperuser/Policy;
.super Ljava/lang/Object;
.source "Policy.java"


# static fields
.field private static final MAX_POLICY_LENGTH:I = 0xfe0

.field private static volatile canInject:Ljava/lang/Boolean;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private static volatile injected:Z

.field private static final synchronizer:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .prologue
    .line 61
    new-instance v0, Ljava/lang/Object;

    move-object v2, v0

    move-object v0, v2

    move-object v1, v2

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    sput-object v0, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    .line 63
    const/4 v0, 0x0

    sput-object v0, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    .line 64
    const/4 v0, 0x0

    sput-boolean v0, Leu/chainfire/libsuperuser/Policy;->injected:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .prologue
    .line 54
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static canInject()Z
    .locals 11
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 105
    sget-object v5, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    move-object v10, v5

    move-object v5, v10

    move-object v6, v10

    move-object v0, v6

    monitor-enter v5

    .line 106
    :try_start_0
    sget-object v5, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    if-eqz v5, :cond_0

    sget-object v5, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    move-object v6, v0

    monitor-exit v6

    move v0, v5

    .line 124
    :goto_0
    return v0

    .line 108
    :cond_0
    const/4 v5, 0x0

    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    sput-object v5, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    .line 114
    const-string v5, "sh"

    const/4 v6, 0x1

    new-array v6, v6, [Ljava/lang/String;

    move-object v10, v6

    move-object v6, v10

    move-object v7, v10

    const/4 v8, 0x0

    const-string v9, "supolicy"

    aput-object v9, v7, v8

    const/4 v7, 0x0

    const/4 v8, 0x0

    invoke-static {v5, v6, v7, v8}, Leu/chainfire/libsuperuser/Shell;->run(Ljava/lang/String;[Ljava/lang/String;[Ljava/lang/String;Z)Ljava/util/List;

    move-result-object v5

    move-object v1, v5

    .line 115
    move-object v5, v1

    if-eqz v5, :cond_1

    .line 116
    move-object v5, v1

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    move-object v2, v5

    :goto_1
    move-object v5, v2

    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    move-object v5, v2

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    move-object v3, v5

    .line 117
    move-object v5, v3

    const-string v6, "supolicy"

    invoke-virtual {v5, v6}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_2

    .line 118
    const/4 v5, 0x1

    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v5

    sput-object v5, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    .line 124
    :cond_1
    sget-object v5, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    move-object v6, v0

    monitor-exit v6

    move v0, v5

    goto :goto_0

    .line 121
    :cond_2
    goto :goto_1

    .line 125
    :catchall_0
    move-exception v5

    move-object v4, v5

    move-object v5, v0

    monitor-exit v5
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v5, v4

    throw v5
.end method

.method public static haveInjected()Z
    .locals 1
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 71
    sget-boolean v0, Leu/chainfire/libsuperuser/Policy;->injected:Z

    return v0
.end method

.method public static resetCanInject()V
    .locals 5
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 133
    sget-object v2, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    move-object v4, v2

    move-object v2, v4

    move-object v3, v4

    move-object v0, v3

    monitor-enter v2

    .line 134
    const/4 v2, 0x0

    :try_start_0
    sput-object v2, Leu/chainfire/libsuperuser/Policy;->canInject:Ljava/lang/Boolean;

    .line 135
    move-object v2, v0

    monitor-exit v2

    .line 136
    return-void

    .line 135
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v2, v1

    throw v2
.end method

.method public static resetInjected()V
    .locals 5
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 80
    sget-object v2, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    move-object v4, v2

    move-object v2, v4

    move-object v3, v4

    move-object v0, v3

    monitor-enter v2

    .line 81
    const/4 v2, 0x0

    :try_start_0
    sput-boolean v2, Leu/chainfire/libsuperuser/Policy;->injected:Z

    .line 82
    move-object v2, v0

    monitor-exit v2

    .line 83
    return-void

    .line 82
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v2, v1

    throw v2
.end method


# virtual methods
.method protected getInjectCommands()Ljava/util/List;
    .locals 3
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .prologue
    .line 145
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Policy;->getInjectCommands(Z)Ljava/util/List;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method protected getInjectCommands(Z)Ljava/util/List;
    .locals 15
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .prologue
    .line 158
    move-object v0, p0

    move/from16 v1, p1

    sget-object v11, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    move-object v14, v11

    move-object v11, v14

    move-object v12, v14

    move-object v2, v12

    monitor-enter v11

    .line 160
    :try_start_0
    invoke-static {}, Leu/chainfire/libsuperuser/Shell$SU;->isSELinuxEnforcing()Z

    move-result v11

    if-nez v11, :cond_0

    const/4 v11, 0x0

    move-object v12, v2

    monitor-exit v12

    move-object v0, v11

    .line 191
    :goto_0
    return-object v0

    .line 163
    :cond_0
    move v11, v1

    if-eqz v11, :cond_1

    invoke-static {}, Leu/chainfire/libsuperuser/Policy;->canInject()Z

    move-result v11

    if-nez v11, :cond_1

    const/4 v11, 0x0

    move-object v12, v2

    monitor-exit v12

    move-object v0, v11

    goto :goto_0

    .line 166
    :cond_1
    sget-boolean v11, Leu/chainfire/libsuperuser/Policy;->injected:Z

    if-eqz v11, :cond_2

    const/4 v11, 0x0

    move-object v12, v2

    monitor-exit v12

    move-object v0, v11

    goto :goto_0

    .line 169
    :cond_2
    move-object v11, v0

    invoke-virtual {v11}, Leu/chainfire/libsuperuser/Policy;->getPolicies()[Ljava/lang/String;

    move-result-object v11

    move-object v3, v11

    .line 170
    move-object v11, v3

    if-eqz v11, :cond_7

    move-object v11, v3

    array-length v11, v11

    if-lez v11, :cond_7

    .line 171
    new-instance v11, Ljava/util/ArrayList;

    move-object v14, v11

    move-object v11, v14

    move-object v12, v14

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    move-object v4, v11

    .line 174
    const-string v11, ""

    move-object v5, v11

    .line 175
    move-object v11, v3

    move-object v6, v11

    move-object v11, v6

    array-length v11, v11

    move v7, v11

    const/4 v11, 0x0

    move v8, v11

    :goto_1
    move v11, v8

    move v12, v7

    if-ge v11, v12, :cond_5

    move-object v11, v6

    move v12, v8

    aget-object v11, v11, v12

    move-object v9, v11

    .line 176
    move-object v11, v5

    invoke-virtual {v11}, Ljava/lang/String;->length()I

    move-result v11

    if-eqz v11, :cond_3

    move-object v11, v5

    invoke-virtual {v11}, Ljava/lang/String;->length()I

    move-result v11

    move-object v12, v9

    invoke-virtual {v12}, Ljava/lang/String;->length()I

    move-result v12

    add-int/2addr v11, v12

    const/4 v12, 0x3

    add-int/lit8 v11, v11, 0x3

    const/16 v12, 0xfe0

    if-ge v11, v12, :cond_4

    .line 177
    :cond_3
    new-instance v11, Ljava/lang/StringBuilder;

    move-object v14, v11

    move-object v11, v14

    move-object v12, v14

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    move-object v12, v5

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    const-string v12, " \""

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    move-object v12, v9

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    const-string v12, "\""

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    move-object v5, v11

    .line 175
    :goto_2
    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    .line 179
    :cond_4
    move-object v11, v4

    new-instance v12, Ljava/lang/StringBuilder;

    move-object v14, v12

    move-object v12, v14

    move-object v13, v14

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    const-string v13, "supolicy --live"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    move-object v13, v5

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-result v11

    .line 180
    const-string v11, ""

    move-object v5, v11

    goto :goto_2

    .line 183
    :cond_5
    move-object v11, v5

    invoke-virtual {v11}, Ljava/lang/String;->length()I

    move-result v11

    if-lez v11, :cond_6

    .line 184
    move-object v11, v4

    new-instance v12, Ljava/lang/StringBuilder;

    move-object v14, v12

    move-object v12, v14

    move-object v13, v14

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    const-string v13, "supolicy --live"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    move-object v13, v5

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-result v11

    .line 187
    :cond_6
    move-object v11, v4

    move-object v12, v2

    monitor-exit v12

    move-object v0, v11

    goto/16 :goto_0

    .line 191
    :cond_7
    const/4 v11, 0x0

    move-object v12, v2

    monitor-exit v12

    move-object v0, v11

    goto/16 :goto_0

    .line 192
    :catchall_0
    move-exception v11

    move-object v10, v11

    move-object v11, v2

    monitor-exit v11
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v11, v10

    throw v11
.end method

.method protected abstract getPolicies()[Ljava/lang/String;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method

.method public inject()V
    .locals 7
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 202
    move-object v0, p0

    sget-object v4, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    move-object v6, v4

    move-object v4, v6

    move-object v5, v6

    move-object v1, v5

    monitor-enter v4

    .line 204
    move-object v4, v0

    :try_start_0
    invoke-virtual {v4}, Leu/chainfire/libsuperuser/Policy;->getInjectCommands()Ljava/util/List;

    move-result-object v4

    move-object v2, v4

    .line 207
    move-object v4, v2

    if-eqz v4, :cond_0

    move-object v4, v2

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-lez v4, :cond_0

    .line 208
    move-object v4, v2

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$SU;->run(Ljava/util/List;)Ljava/util/List;

    move-result-object v4

    .line 212
    :cond_0
    const/4 v4, 0x1

    sput-boolean v4, Leu/chainfire/libsuperuser/Policy;->injected:Z

    .line 213
    move-object v4, v1

    monitor-exit v4

    .line 214
    return-void

    .line 213
    :catchall_0
    move-exception v4

    move-object v3, v4

    move-object v4, v1

    monitor-exit v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v4, v3

    throw v4
.end method

.method public inject(Leu/chainfire/libsuperuser/Shell$Interactive;Z)V
    .locals 9
    .param p1    # Leu/chainfire/libsuperuser/Shell$Interactive;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .prologue
    .line 227
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    sget-object v6, Leu/chainfire/libsuperuser/Policy;->synchronizer:Ljava/lang/Object;

    move-object v8, v6

    move-object v6, v8

    move-object v7, v8

    move-object v3, v7

    monitor-enter v6

    .line 229
    move-object v6, v0

    move v7, v2

    :try_start_0
    invoke-virtual {v6, v7}, Leu/chainfire/libsuperuser/Policy;->getInjectCommands(Z)Ljava/util/List;

    move-result-object v6

    move-object v4, v6

    .line 232
    move-object v6, v4

    if-eqz v6, :cond_0

    move-object v6, v4

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-lez v6, :cond_0

    .line 233
    move-object v6, v1

    move-object v7, v4

    invoke-virtual {v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->addCommand(Ljava/lang/Object;)V

    .line 234
    move v6, v2

    if-eqz v6, :cond_0

    .line 235
    move-object v6, v1

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->waitForIdle()Z

    move-result v6

    .line 240
    :cond_0
    const/4 v6, 0x1

    sput-boolean v6, Leu/chainfire/libsuperuser/Policy;->injected:Z

    .line 241
    move-object v6, v3

    monitor-exit v6

    .line 242
    return-void

    .line 241
    :catchall_0
    move-exception v6

    move-object v5, v6

    move-object v6, v3

    monitor-exit v6
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v6, v5

    throw v6
.end method
