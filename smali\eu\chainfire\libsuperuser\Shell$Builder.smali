.class public Leu/chainfire/libsuperuser/Shell$Builder;
.super Ljava/lang/Object;
.source "Shell.java"


# annotations
.annotation build Landroidx/annotation/AnyThread;
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Builder"
.end annotation


# instance fields
.field private autoHandler:Z

.field private commands:Ljava/util/List;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List",
            "<",
            "Leu/chainfire/libsuperuser/Shell$Command;",
            ">;"
        }
    .end annotation
.end field

.field private detectOpen:Z

.field private environment:Ljava/util/Map;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map",
            "<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private handler:Landroid/os/Handler;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private shell:Ljava/lang/String;

.field private shellDiesOnSTDOUTERRClose:Z

.field private wantSTDERR:Z

.field private watchdogTimeout:I


# direct methods
.method public constructor <init>()V
    .locals 5

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    .line 970
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->handler:Landroid/os/Handler;

    .line 972
    move-object v1, v0

    const/4 v2, 0x1

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->autoHandler:Z

    .line 973
    move-object v1, v0

    const-string v2, "sh"

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->shell:Ljava/lang/String;

    .line 974
    move-object v1, v0

    const/4 v2, 0x0

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->wantSTDERR:Z

    .line 975
    move-object v1, v0

    const/4 v2, 0x1

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->shellDiesOnSTDOUTERRClose:Z

    .line 976
    move-object v1, v0

    const/4 v2, 0x1

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->detectOpen:Z

    .line 977
    move-object v1, v0

    new-instance v2, Ljava/util/LinkedList;

    move-object v4, v2

    move-object v2, v4

    move-object v3, v4

    invoke-direct {v3}, Ljava/util/LinkedList;-><init>()V

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->commands:Ljava/util/List;

    .line 979
    move-object v1, v0

    new-instance v2, Ljava/util/HashMap;

    move-object v4, v2

    move-object v2, v4

    move-object v3, v4

    invoke-direct {v3}, Ljava/util/HashMap;-><init>()V

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->environment:Ljava/util/Map;

    .line 981
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 983
    move-object v1, v0

    const/4 v2, 0x0

    iput-object v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 985
    move-object v1, v0

    const/4 v2, 0x0

    iput v2, v1, Leu/chainfire/libsuperuser/Shell$Builder;->watchdogTimeout:I

    return-void
.end method

.method static synthetic access$100(Leu/chainfire/libsuperuser/Shell$Builder;)Z
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->autoHandler:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$1000(Leu/chainfire/libsuperuser/Shell$Builder;)Landroid/os/Handler;
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->handler:Landroid/os/Handler;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$1100(Leu/chainfire/libsuperuser/Shell$Builder;)Z
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->detectOpen:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$200(Leu/chainfire/libsuperuser/Shell$Builder;)Ljava/lang/String;
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->shell:Ljava/lang/String;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$300(Leu/chainfire/libsuperuser/Shell$Builder;)Z
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->shellDiesOnSTDOUTERRClose:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$400(Leu/chainfire/libsuperuser/Shell$Builder;)Z
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->wantSTDERR:Z

    move v0, v1

    return v0
.end method

.method static synthetic access$4900(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 6

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, v0

    move-object v4, v1

    move v5, v2

    invoke-direct {v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$Builder;->openThreadedEx(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method static synthetic access$500(Leu/chainfire/libsuperuser/Shell$Builder;)Ljava/util/List;
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->commands:Ljava/util/List;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$600(Leu/chainfire/libsuperuser/Shell$Builder;)Ljava/util/Map;
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->environment:Ljava/util/Map;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$700(Leu/chainfire/libsuperuser/Shell$Builder;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$800(Leu/chainfire/libsuperuser/Shell$Builder;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$900(Leu/chainfire/libsuperuser/Shell$Builder;)I
    .locals 2

    .prologue
    .line 969
    move-object v0, p0

    move-object v1, v0

    iget v1, v1, Leu/chainfire/libsuperuser/Shell$Builder;->watchdogTimeout:I

    move v0, v1

    return v0
.end method

.method private openThreadedEx(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 9

    .prologue
    .line 1347
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v4, 0x13

    if-lt v3, v4, :cond_0

    .line 1348
    new-instance v3, Leu/chainfire/libsuperuser/Shell$ThreadedAutoCloseable;

    move-object v8, v3

    move-object v3, v8

    move-object v4, v8

    move-object v5, v0

    move-object v6, v1

    move v7, v2

    invoke-direct {v4, v5, v6, v7}, Leu/chainfire/libsuperuser/Shell$ThreadedAutoCloseable;-><init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)V

    move-object v0, v3

    .line 1350
    :goto_0
    return-object v0

    :cond_0
    new-instance v3, Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v8, v3

    move-object v3, v8

    move-object v4, v8

    move-object v5, v0

    move-object v6, v1

    move v7, v2

    invoke-direct {v4, v5, v6, v7}, Leu/chainfire/libsuperuser/Shell$Threaded;-><init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)V

    move-object v0, v3

    goto :goto_0
.end method


# virtual methods
.method public addCommand(Ljava/lang/Object;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 6
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1163
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    const/4 v5, 0x0

    invoke-virtual {v2, v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$Builder;->addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v2

    move-object v0, v2

    return-object v0
.end method

.method public addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 11
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Leu/chainfire/libsuperuser/Shell$OnResult;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1197
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Builder;->commands:Ljava/util/List;

    new-instance v5, Leu/chainfire/libsuperuser/Shell$Command;

    move-object v10, v5

    move-object v5, v10

    move-object v6, v10

    move-object v7, v1

    move v8, v2

    move-object v9, v3

    invoke-direct {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Command;-><init>(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-result v4

    .line 1198
    move-object v4, v0

    move-object v0, v4

    return-object v0
.end method

.method public addEnvironment(Ljava/lang/String;Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1139
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Builder;->environment:Ljava/util/Map;

    move-object v4, v1

    move-object v5, v2

    invoke-interface {v3, v4, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    .line 1140
    move-object v3, v0

    move-object v0, v3

    return-object v0
.end method

.method public addEnvironment(Ljava/util/Map;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .param p1    # Ljava/util/Map;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map",
            "<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Leu/chainfire/libsuperuser/Shell$Builder;"
        }
    .end annotation

    .prologue
    .line 1151
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Builder;->environment:Ljava/util/Map;

    move-object v3, v1

    invoke-interface {v2, v3}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 1152
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public open()Leu/chainfire/libsuperuser/Shell$Interactive;
    .locals 6
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1285
    move-object v0, p0

    new-instance v1, Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v5, v1

    move-object v1, v5

    move-object v2, v5

    move-object v3, v0

    const/4 v4, 0x0

    invoke-direct {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$Interactive;-><init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V

    move-object v0, v1

    return-object v0
.end method

.method public open(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Interactive;
    .locals 7
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1297
    move-object v0, p0

    move-object v1, p1

    new-instance v2, Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v6, v2

    move-object v2, v6

    move-object v3, v6

    move-object v4, v0

    move-object v5, v1

    invoke-direct {v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$Interactive;-><init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V

    move-object v0, v2

    return-object v0
.end method

.method public openThreaded()Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1319
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-direct {v1, v2, v3}, Leu/chainfire/libsuperuser/Shell$Builder;->openThreadedEx(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method public openThreaded(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 5
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1343
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    invoke-direct {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$Builder;->openThreadedEx(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v2

    move-object v0, v2

    return-object v0
.end method

.method public setAutoHandler(Z)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1019
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->autoHandler:Z

    .line 1020
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setDetectOpen(Z)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 1083
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->detectOpen:Z

    .line 1084
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setHandler(Landroid/os/Handler;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .param p1    # Landroid/os/Handler;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1001
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->handler:Landroid/os/Handler;

    .line 1002
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setMinimalLogging(Z)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1274
    move-object v0, p0

    move v1, p1

    const/4 v2, 0x6

    move v3, v1

    if-nez v3, :cond_0

    const/4 v3, 0x1

    :goto_0
    invoke-static {v2, v3}, Leu/chainfire/libsuperuser/Debug;->setLogTypeEnabled(IZ)V

    .line 1275
    move-object v2, v0

    move-object v0, v2

    return-object v0

    .line 1274
    :cond_0
    const/4 v3, 0x0

    goto :goto_0
.end method

.method public setOnSTDERRLineListener(Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .param p1    # Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1233
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->onSTDERRLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 1234
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setOnSTDOUTLineListener(Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .param p1    # Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1215
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->onSTDOUTLineListener:Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    .line 1216
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setShell(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1032
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->shell:Ljava/lang/String;

    .line 1033
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setShellDiesOnSTDOUTERRClose(Z)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 1106
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->shellDiesOnSTDOUTERRClose:Z

    .line 1107
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setWantSTDERR(Z)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 1126
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->wantSTDERR:Z

    .line 1127
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public setWatchdogTimeout(I)Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1257
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    iput v3, v2, Leu/chainfire/libsuperuser/Shell$Builder;->watchdogTimeout:I

    .line 1258
    move-object v2, v0

    move-object v0, v2

    return-object v0
.end method

.method public useSH()Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 3
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1043
    move-object v0, p0

    move-object v1, v0

    const-string v2, "sh"

    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Builder;->setShell(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method public useSU()Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 3
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 1053
    move-object v0, p0

    move-object v1, v0

    const-string v2, "su"

    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Builder;->setShell(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method
