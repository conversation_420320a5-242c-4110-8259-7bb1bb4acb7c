.class Lcom/bad/modder/injector/MainActivity$100000000;
.super Ljava/lang/Object;
.source "MainActivity.java"

# interfaces
.implements Ljava/lang/Thread$UncaughtExceptionHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/MainActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000000"
.end annotation


# instance fields
.field private final this$0:Lcom/bad/modder/injector/MainActivity;


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/MainActivity;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/bad/modder/injector/MainActivity$100000000;->this$0:Lcom/bad/modder/injector/MainActivity;

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/MainActivity$100000000;)Lcom/bad/modder/injector/MainActivity;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/MainActivity$100000000;->this$0:Lcom/bad/modder/injector/MainActivity;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public uncaughtException(Ljava/lang/Thread;Ljava/lang/Throwable;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Thread;",
            "Ljava/lang/Throwable;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 31
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    new-instance v9, Ljava/io/StringWriter;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    invoke-direct {v10}, Ljava/io/StringWriter;-><init>()V

    move-object v4, v9

    .line 32
    new-instance v9, Ljava/io/PrintWriter;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    move-object v11, v4

    invoke-direct {v10, v11}, Ljava/io/PrintWriter;-><init>(Ljava/io/Writer;)V

    move-object v5, v9

    .line 33
    move-object v9, v2

    move-object v10, v5

    invoke-virtual {v9, v10}, Ljava/lang/Throwable;->printStackTrace(Ljava/io/PrintWriter;)V

    .line 36
    move-object v9, v0

    iget-object v9, v9, Lcom/bad/modder/injector/MainActivity$100000000;->this$0:Lcom/bad/modder/injector/MainActivity;

    const-string v10, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CIHxxDTAqHHIQBQdYbzgdAmtqMScnQHkGES4ZdRgWAB5wHRkVaHAcEyRXXzEVK0FIDBcXeV0NZS1oayVjFW9pfhZ3VU8dBi17WR0VIGxRBDInen0zFRFOaBEWBFRcAzsva2oTOBNqeRYSERRIHBYcH2JlOxNheRt/EXppBxUrWWoSLDF5cGZkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvaQA7HmRCEx0kV1M7BCt4dBcscm5eIjMiYHs9Yx5vaSAyd3tdFBMxfmE7OyRnaxd7FVp1MxYOFFwQLAdVXQM/Ik96MTMXb0whHhFaehoGA0h+DRETYXAPIid8eWIcK397EGYDQVkDGitoeyFgIm99IRx2a10PORdrYgMWIXRAYCInR18CMBRFahEjH3leOBFxYkEHOidVTGIcdVVQFywPYn4QFSJvUGwYJGlLAh8Be2ofAHZ1WmUgMmVgAAEieXltFXZFXxQQC0hhDTskYFEHNSV1VyIRE3tXED0+H2o4PxVlb2QmJ1R+Mh0rFAEcBj4fWWRkFmJqMSYfSnUCHR5/eQwXD0NqZGwFYHoHEidFfT8cdUFdGhYAVWE+EQ5iUTECEmpxNjAQe2kTLTF9YTtgEmRAMTwjeX0WHChVCTA8dll+FGAfb2oxHCRKaQMyK0VfFC4He14SZCtrYA8SA1V9EhV1QVARBhNKaxARPmVPAyUleWEzMBR8aBAWJQZwZztydGtkJR9vdTwdd38TGDx2X34TOyB9UQMyIFV2Ph4eHF4cBgNLXiIzcWBWA3sjfE8NEHZjSQ8TMW9hHTgubGoTEgVvdTYwKkFoEBAxbG8DPxVhexM9F29LPB0oFFoYFjFcXWY/Imh8E20feVAyFXV/Dx0tD2tuBBURZXs9AgV5eW0wLWddHDkHRGEDDiF3ahMSJ3x1Zh92e04TFwt9WWcncnd7ISUnRVsCEhEUYB88EB5dZBUXa1AYEx9/dQcEK3t6FGYPQV0CETxgez0SBUVxIRkTWVEUPSV+XB07Ak9pAxwTV08xMBR8chcGH3lpO2ATd3sXbSNXVD0cdV1IMDwPX2JlPx9vQGx7HnlfYh90ew8PFg9HXmY/aWhwDz4ef1cgMj4UYhA+fkhZZBUkSEEXfyB6YQwfKFlbED4xTm4SbAJiez06Eh96IRJ0FHUMPANfYAAVHn15Gyclbwh6HStFABBmdktdACM/aFATYxV/XxQZE3dZDzMDYWIDbBJrUR8iJ3oILB8+f1sRLXIcazk7ImtqD2cnRXUGHShBdBkWfx9dZGASawsfOB56ajISHkkPHAcTQ2oDJwVleyVjFW9pbRUEGFwPExd4WmURAktAE2wifFs2H3Z7SRA5H31pA2QFaGwtMyNXCAYWE3tAHAYtfF1lbBd9Uh8BHkp5AxUrZw8XEA9DXQNsDmVBPTElRXl+GR4cSh0DB3leABEeT2sXJSB+TwccKhxyFwAxfWBnHip9QGwiF2pPDB53SXQTLB9/XWU/C2V8AxweRXomFwFFTxEuckheZDchYFFgAhNvCBIRdBhiFCwfRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-static {v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Lcom/bad/modder/injector/MainActivity;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Landroid/content/ClipboardManager;

    move-object v6, v9

    .line 37
    const-string v9, "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"

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    move-object v10, v4

    invoke-virtual {v10}, Ljava/io/StringWriter;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-static {v9, v10}, Landroid/content/ClipData;->newPlainText(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Landroid/content/ClipData;

    move-result-object v9

    move-object v7, v9

    .line 38
    move-object v9, v6

    move-object v10, v7

    invoke-virtual {v9, v10}, Landroid/content/ClipboardManager;->setPrimaryClip(Landroid/content/ClipData;)V

    .line 39
    const/4 v9, 0x1

    invoke-static {v9}, Ljava/lang/System;->exit(I)V

    return-void
.end method
