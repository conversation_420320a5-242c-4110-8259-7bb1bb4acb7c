.class Leu/chainfire/libsuperuser/Shell$Interactive$5;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->postCallback(Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;Ljava/io/InputStream;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

.field final synthetic val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

.field final synthetic val$fExitCode:I

.field final synthetic val$fSTDERR:Ljava/util/List;

.field final synthetic val$fSTDOUT:Ljava/util/List;

.field final synthetic val$inputStream:Ljava/io/InputStream;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/io/InputStream;Leu/chainfire/libsuperuser/Shell$Command;ILjava/util/List;Ljava/util/List;)V
    .locals 9

    .prologue
    .line 2017
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v4, p4

    move-object v5, p5

    move-object v6, p6

    move-object v7, v0

    move-object v8, v1

    iput-object v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v0

    move-object v8, v2

    iput-object v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$inputStream:Ljava/io/InputStream;

    move-object v7, v0

    move-object v8, v3

    iput-object v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    move-object v7, v0

    move v8, v4

    iput v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fExitCode:I

    move-object v7, v0

    move-object v8, v5

    iput-object v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDOUT:Ljava/util/List;

    move-object v7, v0

    move-object v8, v6

    iput-object v8, v7, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDERR:Ljava/util/List;

    move-object v7, v0

    invoke-direct {v7}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 7

    .prologue
    .line 2021
    move-object v0, p0

    move-object v2, v0

    :try_start_0
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$inputStream:Ljava/io/InputStream;

    if-nez v2, :cond_7

    .line 2022
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 2023
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1700(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;

    move-result-object v2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v3}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v3

    move-object v4, v0

    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fExitCode:I

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDOUT:Ljava/util/List;

    if-eqz v5, :cond_4

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDOUT:Ljava/util/List;

    :goto_0
    invoke-interface {v2, v3, v4, v5}, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;->onCommandResult(IILjava/util/List;)V

    .line 2024
    :cond_0
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 2025
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1800(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;

    move-result-object v2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v3}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v3

    move-object v4, v0

    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fExitCode:I

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDOUT:Ljava/util/List;

    if-eqz v5, :cond_5

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDOUT:Ljava/util/List;

    :goto_1
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDERR:Ljava/util/List;

    if-eqz v6, :cond_6

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fSTDERR:Ljava/util/List;

    :goto_2
    invoke-interface {v2, v3, v4, v5, v6}, Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;->onCommandResult(IILjava/util/List;Ljava/util/List;)V

    .line 2026
    :cond_1
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v2

    if-eqz v2, :cond_2

    .line 2027
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v3}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v3

    move-object v4, v0

    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fExitCode:I

    invoke-interface {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;->onCommandResult(II)V

    .line 2028
    :cond_2
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 2029
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v3}, Leu/chainfire/libsuperuser/Shell$Command;->access$2300(Leu/chainfire/libsuperuser/Shell$Command;)I

    move-result v3

    move-object v4, v0

    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fExitCode:I

    invoke-interface {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;->onCommandResult(II)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2034
    :cond_3
    :goto_3
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->endCallback()V

    .line 2036
    return-void

    .line 2023
    :cond_4
    move-object v5, v0

    :try_start_1
    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$5;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2400(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;

    move-result-object v5

    goto :goto_0

    .line 2025
    :cond_5
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$5;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v5}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2400(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;

    move-result-object v5

    goto :goto_1

    :cond_6
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$5;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$2400(Leu/chainfire/libsuperuser/Shell$Interactive;)Ljava/util/List;

    move-result-object v6

    goto :goto_2

    .line 2030
    :cond_7
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 2031
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$fCommand:Leu/chainfire/libsuperuser/Shell$Command;

    invoke-static {v2}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v2

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$5;->val$inputStream:Ljava/io/InputStream;

    invoke-interface {v2, v3}, Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;->onInputStream(Ljava/io/InputStream;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_3

    .line 2034
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$5;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->endCallback()V

    .line 2035
    move-object v2, v1

    throw v2
.end method
