.class Lpmm/by/p2077kng/hacker/Loader$100000011;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000011"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;

.field private final val$feature:I


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;I)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Loader$100000011;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v4, v0

    move v5, v2

    iput v5, v4, Lpmm/by/p2077kng/hacker/Loader$100000011;->val$feature:I

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000011;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000011;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public OnWrite(I)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .prologue
    .line 722
    move-object v0, p0

    move v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000011;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v4, v0

    iget v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000011;->val$feature:I

    move v5, v1

    invoke-virtual {v3, v4, v5}, Lpmm/by/p2077kng/hacker/Loader;->Changes(II)V

    return-void
.end method
