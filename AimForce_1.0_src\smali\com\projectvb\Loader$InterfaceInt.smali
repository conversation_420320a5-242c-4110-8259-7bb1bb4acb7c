.class interface Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;
.super Ljava/lang/Object;
.source "Loader.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20a
    name = "InterfaceInt"
.end annotation


# virtual methods
.method public abstract OnWrite(I)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation
.end method
