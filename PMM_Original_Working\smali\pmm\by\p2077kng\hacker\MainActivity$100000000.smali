.class Lpmm/by/p2077kng/hacker/MainActivity$100000000;
.super Ljava/lang/Object;
.source "MainActivity.java"

# interfaces
.implements Landroid/media/MediaPlayer$OnPreparedListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/MainActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000000"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/MainActivity;

.field private final val$videoView:Lpmm/by/p2077kng/hacker/FullScreenVideoView;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/MainActivity;Lpmm/by/p2077kng/hacker/FullScreenVideoView;)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000000;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000000;->val$videoView:Lpmm/by/p2077kng/hacker/FullScreenVideoView;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/MainActivity$100000000;)Lpmm/by/p2077kng/hacker/MainActivity;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/MainActivity$100000000;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onPrepared(Landroid/media/MediaPlayer;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/media/MediaPlayer;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 91
    move-object v0, p0

    move-object v1, p1

    move-object v3, v1

    const/4 v4, 0x1

    invoke-virtual {v3, v4}, Landroid/media/MediaPlayer;->setLooping(Z)V

    .line 93
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/MainActivity$100000000;->val$videoView:Lpmm/by/p2077kng/hacker/FullScreenVideoView;

    const/16 v4, -0x270f

    const/16 v5, -0x270f

    invoke-virtual {v3, v4, v5}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->setVideoSize(II)V

    return-void
.end method
