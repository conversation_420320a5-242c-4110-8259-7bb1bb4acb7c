.class interface Lcom/bad/modder/injector/InjectorService$InterfaceBtn;
.super Ljava/lang/Object;
.source "InjectorService.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20a
    name = "InterfaceBtn"
.end annotation


# virtual methods
.method public abstract OnWrite()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method
