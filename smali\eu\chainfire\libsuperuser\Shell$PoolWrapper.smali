.class public Leu/chainfire/libsuperuser/Shell$PoolWrapper;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$DeprecatedSyncCommands;
.implements Leu/chainfire/libsuperuser/Shell$SyncCommands;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "PoolWrapper"
.end annotation


# instance fields
.field private final shellCommand:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2939
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    .line 2940
    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->shellCommand:Ljava/lang/String;

    .line 2941
    return-void
.end method


# virtual methods
.method public get()Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 2961
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->shellCommand:Ljava/lang/String;

    invoke-static {v1}, Leu/chainfire/libsuperuser/Shell$Pool;->get(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method public get(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 4
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 2983
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->shellCommand:Ljava/lang/String;

    move-object v3, v1

    invoke-static {v2, v3}, Leu/chainfire/libsuperuser/Shell$Pool;->get(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v2

    move-object v0, v2

    return-object v0
.end method

.method public getUnpooled()Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 2996
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->shellCommand:Ljava/lang/String;

    invoke-static {v1}, Leu/chainfire/libsuperuser/Shell$Pool;->getUnpooled(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v1

    move-object v0, v1

    return-object v0
.end method

.method public getUnpooled(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    .locals 4
    .param p1    # Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 3010
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->shellCommand:Ljava/lang/String;

    move-object v3, v1

    invoke-static {v2, v3}, Leu/chainfire/libsuperuser/Shell$Pool;->getUnpooled(Ljava/lang/String;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v2

    move-object v0, v2

    return-object v0
.end method

.method public run(Ljava/lang/Object;)I
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 3092
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    invoke-virtual {v2, v3, v4, v5, v6}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I

    move-result v2

    move v0, v2

    return v0
.end method

.method public run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;)I
    .locals 9
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 3123
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v6, v0

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->get()Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v6

    move-object v3, v6

    .line 3125
    move-object v6, v3

    move-object v7, v1

    move-object v8, v2

    :try_start_0
    invoke-virtual {v6, v7, v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v6

    move v4, v6

    .line 3127
    move-object v6, v3

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3125
    move v6, v4

    move v0, v6

    return v0

    .line 3127
    :catchall_0
    move-exception v6

    move-object v5, v6

    move-object v6, v3

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3128
    move-object v6, v5

    throw v6
.end method

.method public run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;)I
    .locals 9
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 3111
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v6, v0

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->get()Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v6

    move-object v3, v6

    .line 3113
    move-object v6, v3

    move-object v7, v1

    move-object v8, v2

    :try_start_0
    invoke-virtual {v6, v7, v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->run(Ljava/lang/Object;Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v6

    move v4, v6

    .line 3115
    move-object v6, v3

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3113
    move v6, v4

    move v0, v6

    return v0

    .line 3115
    :catchall_0
    move-exception v6

    move-object v5, v6

    move-object v6, v3

    invoke-virtual {v6}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3116
    move-object v6, v5

    throw v6
.end method

.method public run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I
    .locals 13
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;Z)I"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Leu/chainfire/libsuperuser/Shell$ShellDiedException;
        }
    .end annotation

    .prologue
    .line 3099
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object/from16 v3, p3

    move/from16 v4, p4

    move-object v8, v0

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->get()Leu/chainfire/libsuperuser/Shell$Threaded;

    move-result-object v8

    move-object v5, v8

    .line 3101
    move-object v8, v5

    move-object v9, v1

    move-object v10, v2

    move-object v11, v3

    move v12, v4

    :try_start_0
    invoke-virtual {v8, v9, v10, v11, v12}, Leu/chainfire/libsuperuser/Shell$Threaded;->run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v8

    move v6, v8

    .line 3103
    move-object v8, v5

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3101
    move v8, v6

    move v0, v8

    return v0

    .line 3103
    :catchall_0
    move-exception v8

    move-object v7, v8

    move-object v8, v5

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3104
    move-object v8, v7

    throw v8
.end method

.method public run(Ljava/lang/Object;Z)Ljava/util/List;
    .locals 18
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Z)",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 3019
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object v8, v0

    :try_start_0
    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->get()Leu/chainfire/libsuperuser/Shell$Threaded;
    :try_end_0
    .catch Leu/chainfire/libsuperuser/Shell$ShellDiedException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v8

    move-object v3, v8

    .line 3021
    const/4 v8, 0x1

    :try_start_1
    new-array v8, v8, [I

    move-object v4, v8

    .line 3022
    new-instance v8, Ljava/util/ArrayList;

    move-object/from16 v17, v8

    move-object/from16 v8, v17

    move-object/from16 v9, v17

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    move-object v5, v8

    .line 3023
    move-object v8, v3

    move-object v9, v1

    const/4 v10, 0x0

    new-instance v11, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    move-object v13, v0

    move-object v14, v4

    move-object v15, v5

    move/from16 v16, v2

    invoke-direct/range {v12 .. v16}, Leu/chainfire/libsuperuser/Shell$PoolWrapper$1;-><init>(Leu/chainfire/libsuperuser/Shell$PoolWrapper;[ILjava/util/List;Z)V

    invoke-virtual {v8, v9, v10, v11}, Leu/chainfire/libsuperuser/Shell$Threaded;->addCommand(Ljava/lang/Object;ILeu/chainfire/libsuperuser/Shell$OnResult;)V

    .line 3033
    move-object v8, v3

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->waitForIdle()Z

    move-result v8

    .line 3034
    move-object v8, v4

    const/4 v9, 0x0

    aget v8, v8, v9
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-gez v8, :cond_0

    const/4 v8, 0x0

    move-object v6, v8

    .line 3037
    move-object v8, v3

    :try_start_2
    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3034
    move-object v8, v6

    move-object v0, v8

    .line 3040
    :goto_0
    return-object v0

    .line 3035
    :cond_0
    move-object v8, v5

    move-object v6, v8

    .line 3037
    move-object v8, v3

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3035
    move-object v8, v6

    move-object v0, v8

    goto :goto_0

    .line 3037
    :catchall_0
    move-exception v8

    move-object v7, v8

    move-object v8, v3

    invoke-virtual {v8}, Leu/chainfire/libsuperuser/Shell$Threaded;->close()V

    .line 3038
    move-object v8, v7

    throw v8
    :try_end_2
    .catch Leu/chainfire/libsuperuser/Shell$ShellDiedException; {:try_start_2 .. :try_end_2} :catch_0

    .line 3039
    :catch_0
    move-exception v8

    move-object v3, v8

    .line 3040
    const/4 v8, 0x0

    move-object v0, v8

    goto :goto_0
.end method

.method public run(Ljava/lang/Object;[Ljava/lang/String;Z)Ljava/util/List;
    .locals 20
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # [Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "[",
            "Ljava/lang/String;",
            "Z)",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 3050
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    move/from16 v3, p3

    move-object v12, v2

    if-nez v12, :cond_0

    .line 3051
    move-object v12, v0

    move-object v13, v1

    move v14, v3

    invoke-virtual {v12, v13, v14}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v12

    move-object v0, v12

    .line 3081
    :goto_0
    return-object v0

    .line 3054
    :cond_0
    move-object v12, v1

    instance-of v12, v12, Ljava/lang/String;

    if-eqz v12, :cond_2

    .line 3055
    const/4 v12, 0x1

    new-array v12, v12, [Ljava/lang/String;

    move-object/from16 v19, v12

    move-object/from16 v12, v19

    move-object/from16 v13, v19

    const/4 v14, 0x0

    move-object v15, v1

    check-cast v15, Ljava/lang/String;

    aput-object v15, v13, v14

    move-object v4, v12

    .line 3064
    :goto_1
    new-instance v12, Ljava/lang/StringBuilder;

    move-object/from16 v19, v12

    move-object/from16 v12, v19

    move-object/from16 v13, v19

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    move-object v5, v12

    .line 3065
    move-object v12, v2

    move-object v6, v12

    move-object v12, v6

    array-length v12, v12

    move v7, v12

    const/4 v12, 0x0

    move v8, v12

    :goto_2
    move v12, v8

    move v13, v7

    if-ge v12, v13, :cond_7

    move-object v12, v6

    move v13, v8

    aget-object v12, v12, v13

    move-object v9, v12

    .line 3067
    move-object v12, v9

    const-string v13, "="

    invoke-virtual {v12, v13}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v12

    move/from16 v19, v12

    move/from16 v12, v19

    move/from16 v13, v19

    move v10, v13

    if-ltz v12, :cond_1

    .line 3068
    move-object v12, v9

    move v13, v10

    const/4 v14, 0x1

    add-int/lit8 v13, v13, 0x1

    move v14, v10

    const/4 v15, 0x2

    add-int/lit8 v14, v14, 0x2

    invoke-virtual {v12, v13, v14}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v12

    const-string v13, "\""

    invoke-virtual {v12, v13}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v12

    move v11, v12

    .line 3069
    move-object v12, v5

    move-object v13, v9

    const/4 v14, 0x0

    move v15, v10

    invoke-virtual {v12, v13, v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/CharSequence;II)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3070
    move-object v12, v5

    move v13, v11

    if-eqz v13, :cond_5

    const-string v13, "="

    :goto_3
    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3071
    move-object v12, v5

    move-object v13, v9

    move v14, v10

    const/4 v15, 0x1

    add-int/lit8 v14, v14, 0x1

    invoke-virtual {v13, v14}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3072
    move-object v12, v5

    move v13, v11

    if-eqz v13, :cond_6

    const-string v13, " "

    :goto_4
    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3065
    :cond_1
    add-int/lit8 v8, v8, 0x1

    goto :goto_2

    .line 3056
    :cond_2
    move-object v12, v1

    instance-of v12, v12, Ljava/util/List;

    if-eqz v12, :cond_3

    .line 3057
    move-object v12, v1

    check-cast v12, Ljava/util/List;

    const/4 v13, 0x0

    new-array v13, v13, [Ljava/lang/String;

    invoke-interface {v12, v13}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v12

    check-cast v12, [Ljava/lang/String;

    move-object v4, v12

    goto :goto_1

    .line 3058
    :cond_3
    move-object v12, v1

    instance-of v12, v12, [Ljava/lang/String;

    if-eqz v12, :cond_4

    .line 3059
    move-object v12, v1

    check-cast v12, [Ljava/lang/String;

    check-cast v12, [Ljava/lang/String;

    move-object v4, v12

    goto/16 :goto_1

    .line 3061
    :cond_4
    new-instance v12, Ljava/lang/IllegalArgumentException;

    move-object/from16 v19, v12

    move-object/from16 v12, v19

    move-object/from16 v13, v19

    const-string v14, "commands parameter must be of type String, List<String> or String[]"

    invoke-direct {v13, v14}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v12

    .line 3070
    :cond_5
    const-string v13, "=\""

    goto :goto_3

    .line 3072
    :cond_6
    const-string v13, "\" "

    goto :goto_4

    .line 3075
    :cond_7
    move-object v12, v5

    const-string v13, "sh -c \"\n"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3076
    move-object v12, v4

    move-object v6, v12

    move-object v12, v6

    array-length v12, v12

    move v7, v12

    const/4 v12, 0x0

    move v8, v12

    :goto_5
    move v12, v8

    move v13, v7

    if-ge v12, v13, :cond_8

    move-object v12, v6

    move v13, v8

    aget-object v12, v12, v13

    move-object v9, v12

    .line 3077
    move-object v12, v5

    move-object v13, v9

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3078
    move-object v12, v5

    const-string v13, "\n"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3076
    add-int/lit8 v8, v8, 0x1

    goto :goto_5

    .line 3080
    :cond_8
    move-object v12, v5

    const-string v13, "\""

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    .line 3081
    move-object v12, v0

    const/4 v13, 0x1

    new-array v13, v13, [Ljava/lang/String;

    move-object/from16 v19, v13

    move-object/from16 v13, v19

    move-object/from16 v14, v19

    const/4 v15, 0x0

    move-object/from16 v16, v5

    invoke-virtual/range {v16 .. v16}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v16

    const-string v17, "\\"

    const-string v18, "\\\\"

    .line 3082
    invoke-virtual/range {v16 .. v18}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v16

    const-string v17, "$"

    const-string v18, "\\$"

    .line 3083
    invoke-virtual/range {v16 .. v18}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v16

    aput-object v16, v14, v15

    move v14, v3

    .line 3081
    invoke-virtual {v12, v13, v14}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v12

    move-object v0, v12

    goto/16 :goto_0
.end method
