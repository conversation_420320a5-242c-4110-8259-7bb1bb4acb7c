.class public Leu/chainfire/libsuperuser/Shell$Threaded;
.super Leu/chainfire/libsuperuser/Shell$Interactive;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Threaded"
.end annotation


# static fields
.field private static threadCounter:I


# instance fields
.field private volatile closeEvenIfPooled:Z

.field private final handlerThread:Landroid/os/HandlerThread;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field private final onCloseCalledSync:Ljava/lang/Object;

.field private volatile onClosedCalled:Z

.field private volatile onPoolRemoveCalled:Z

.field private final onPoolRemoveCalledSync:Ljava/lang/Object;

.field private final pooled:Z

.field private volatile reserved:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .prologue
    .line 2690
    const/4 v0, 0x0

    sput v0, Leu/chainfire/libsuperuser/Shell$Threaded;->threadCounter:I

    return-void
.end method

.method protected constructor <init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;Z)V
    .locals 8

    .prologue
    .line 2728
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    move-object v4, v0

    move-object v5, v1

    invoke-static {}, Leu/chainfire/libsuperuser/Shell$Threaded;->createHandlerThread()Landroid/os/Handler;

    move-result-object v6

    invoke-virtual {v5, v6}, Leu/chainfire/libsuperuser/Shell$Builder;->setHandler(Landroid/os/Handler;)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v5

    const/4 v6, 0x1

    .line 2729
    invoke-virtual {v5, v6}, Leu/chainfire/libsuperuser/Shell$Builder;->setDetectOpen(Z)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v5

    const/4 v6, 0x1

    .line 2730
    invoke-virtual {v5, v6}, Leu/chainfire/libsuperuser/Shell$Builder;->setShellDiesOnSTDOUTERRClose(Z)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v5

    move-object v6, v2

    .line 2728
    invoke-direct {v4, v5, v6}, Leu/chainfire/libsuperuser/Shell$Interactive;-><init>(Leu/chainfire/libsuperuser/Shell$Builder;Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)V

    .line 2702
    move-object v4, v0

    new-instance v5, Ljava/lang/Object;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    invoke-direct {v6}, Ljava/lang/Object;-><init>()V

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onCloseCalledSync:Ljava/lang/Object;

    .line 2703
    move-object v4, v0

    const/4 v5, 0x0

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onClosedCalled:Z

    .line 2704
    move-object v4, v0

    new-instance v5, Ljava/lang/Object;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    invoke-direct {v6}, Ljava/lang/Object;-><init>()V

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalledSync:Ljava/lang/Object;

    .line 2705
    move-object v4, v0

    const/4 v5, 0x0

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    .line 2706
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->reserved:Z

    .line 2707
    move-object v4, v0

    const/4 v5, 0x0

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->closeEvenIfPooled:Z

    .line 2735
    move-object v4, v0

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->handler:Landroid/os/Handler;

    invoke-virtual {v5}, Landroid/os/Handler;->getLooper()Landroid/os/Looper;

    move-result-object v5

    invoke-virtual {v5}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v5

    check-cast v5, Landroid/os/HandlerThread;

    iput-object v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->handlerThread:Landroid/os/HandlerThread;

    .line 2738
    move-object v4, v0

    move v5, v3

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->pooled:Z

    .line 2739
    return-void
.end method

.method static synthetic access$4800(Leu/chainfire/libsuperuser/Shell$Threaded;)Landroid/os/HandlerThread;
    .locals 2

    .prologue
    .line 2689
    move-object v0, p0

    move-object v1, v0

    iget-object v1, v1, Leu/chainfire/libsuperuser/Shell$Threaded;->handlerThread:Landroid/os/HandlerThread;

    move-object v0, v1

    return-object v0
.end method

.method static synthetic access$5000(Leu/chainfire/libsuperuser/Shell$Threaded;)Z
    .locals 2

    .prologue
    .line 2689
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Leu/chainfire/libsuperuser/Shell$Threaded;->isReserved()Z

    move-result v1

    move v0, v1

    return v0
.end method

.method static synthetic access$5100(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V
    .locals 4

    .prologue
    .line 2689
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    invoke-direct {v2, v3}, Leu/chainfire/libsuperuser/Shell$Threaded;->closeWhenIdle(Z)V

    return-void
.end method

.method static synthetic access$5200(Leu/chainfire/libsuperuser/Shell$Threaded;Z)V
    .locals 4

    .prologue
    .line 2689
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    invoke-direct {v2, v3}, Leu/chainfire/libsuperuser/Shell$Threaded;->setReserved(Z)V

    return-void
.end method

.method private closeWhenIdle(Z)V
    .locals 7

    .prologue
    .line 2800
    move-object v0, p0

    move v1, p1

    move-object v4, v0

    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->pooled:Z

    if-eqz v4, :cond_1

    .line 2801
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalledSync:Ljava/lang/Object;

    move-object v6, v4

    move-object v4, v6

    move-object v5, v6

    move-object v2, v5

    monitor-enter v4

    .line 2802
    move-object v4, v0

    :try_start_0
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    if-nez v4, :cond_0

    .line 2803
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    .line 2804
    move-object v4, v0

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Pool;->access$4700(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    .line 2806
    :cond_0
    move-object v4, v2

    monitor-exit v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2807
    move v4, v1

    if-eqz v4, :cond_1

    .line 2808
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->closeEvenIfPooled:Z

    .line 2811
    :cond_1
    move-object v4, v0

    invoke-super {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeWhenIdle()V

    .line 2812
    return-void

    .line 2806
    :catchall_0
    move-exception v4

    move-object v3, v4

    move-object v4, v2

    :try_start_1
    monitor-exit v4
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    move-object v4, v3

    throw v4
.end method

.method private static createHandlerThread()Landroid/os/Handler;
    .locals 6

    .prologue
    .line 2712
    new-instance v1, Landroid/os/HandlerThread;

    move-object v5, v1

    move-object v1, v5

    move-object v2, v5

    new-instance v3, Ljava/lang/StringBuilder;

    move-object v5, v3

    move-object v3, v5

    move-object v4, v5

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Shell.Threaded#"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {}, Leu/chainfire/libsuperuser/Shell$Threaded;->incThreadCounter()I

    move-result v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v2, v3}, Landroid/os/HandlerThread;-><init>(Ljava/lang/String;)V

    move-object v0, v1

    .line 2713
    move-object v1, v0

    invoke-virtual {v1}, Landroid/os/HandlerThread;->start()V

    .line 2714
    new-instance v1, Landroid/os/Handler;

    move-object v5, v1

    move-object v1, v5

    move-object v2, v5

    move-object v3, v0

    invoke-virtual {v3}, Landroid/os/HandlerThread;->getLooper()Landroid/os/Looper;

    move-result-object v3

    invoke-direct {v2, v3}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    move-object v0, v1

    return-object v0
.end method

.method private static incThreadCounter()I
    .locals 6

    .prologue
    .line 2692
    const-class v3, Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v5, v3

    move-object v3, v5

    move-object v4, v5

    move-object v0, v4

    monitor-enter v3

    .line 2693
    :try_start_0
    sget v3, Leu/chainfire/libsuperuser/Shell$Threaded;->threadCounter:I

    move v1, v3

    .line 2694
    sget v3, Leu/chainfire/libsuperuser/Shell$Threaded;->threadCounter:I

    const/4 v4, 0x1

    add-int/lit8 v3, v3, 0x1

    sput v3, Leu/chainfire/libsuperuser/Shell$Threaded;->threadCounter:I

    .line 2695
    move v3, v1

    move-object v4, v0

    monitor-exit v4

    move v0, v3

    return v0

    .line 2696
    :catchall_0
    move-exception v3

    move-object v2, v3

    move-object v3, v0

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v3, v2

    throw v3
.end method

.method private isReserved()Z
    .locals 2

    .prologue
    .line 2889
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Threaded;->reserved:Z

    move v0, v1

    return v0
.end method

.method private setReserved(Z)V
    .locals 4

    .prologue
    .line 2893
    move-object v0, p0

    move v1, p1

    move-object v2, v0

    move v3, v1

    iput-boolean v3, v2, Leu/chainfire/libsuperuser/Shell$Threaded;->reserved:Z

    .line 2894
    return-void
.end method


# virtual methods
.method public ac()Leu/chainfire/libsuperuser/Shell$ThreadedAutoCloseable;
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .prologue
    .line 2881
    move-object v0, p0

    move-object v1, v0

    instance-of v1, v1, Leu/chainfire/libsuperuser/Shell$ThreadedAutoCloseable;

    if-eqz v1, :cond_0

    .line 2882
    move-object v1, v0

    check-cast v1, Leu/chainfire/libsuperuser/Shell$ThreadedAutoCloseable;

    move-object v0, v1

    .line 2884
    :goto_0
    return-object v0

    :cond_0
    const/4 v1, 0x0

    move-object v0, v1

    goto :goto_0
.end method

.method public close()V
    .locals 2
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2766
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Threaded;->pooled:Z

    if-eqz v1, :cond_0

    .line 2767
    move-object v1, v0

    invoke-super {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeWhenIdle()V

    .line 2771
    :goto_0
    return-void

    .line 2769
    :cond_0
    move-object v1, v0

    invoke-virtual {v1}, Leu/chainfire/libsuperuser/Shell$Threaded;->closeWhenIdle()V

    goto :goto_0
.end method

.method protected closeImmediately(Z)V
    .locals 8

    .prologue
    .line 2775
    move-object v0, p0

    move v1, p1

    move-object v5, v0

    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->pooled:Z

    if-eqz v5, :cond_4

    .line 2776
    move v5, v1

    if-eqz v5, :cond_2

    .line 2777
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalledSync:Ljava/lang/Object;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    move-object v2, v6

    monitor-enter v5

    .line 2778
    move-object v5, v0

    :try_start_0
    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    if-nez v5, :cond_0

    .line 2779
    move-object v5, v0

    invoke-static {v5}, Leu/chainfire/libsuperuser/Shell$Pool;->access$4600(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    .line 2781
    :cond_0
    move-object v5, v0

    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->closeEvenIfPooled:Z

    if-eqz v5, :cond_1

    .line 2782
    move-object v5, v0

    const/4 v6, 0x1

    invoke-super {v5, v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately(Z)V

    .line 2784
    :cond_1
    move-object v5, v2

    monitor-exit v5

    .line 2797
    :goto_0
    return-void

    .line 2784
    :catchall_0
    move-exception v5

    move-object v3, v5

    move-object v5, v2

    monitor-exit v5
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v5, v3

    throw v5

    .line 2786
    :cond_2
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalledSync:Ljava/lang/Object;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    move-object v2, v6

    monitor-enter v5

    .line 2787
    move-object v5, v0

    :try_start_1
    iget-boolean v5, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    if-nez v5, :cond_3

    .line 2788
    move-object v5, v0

    const/4 v6, 0x1

    iput-boolean v6, v5, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    .line 2789
    move-object v5, v0

    invoke-static {v5}, Leu/chainfire/libsuperuser/Shell$Pool;->access$4700(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    .line 2791
    :cond_3
    move-object v5, v2

    monitor-exit v5
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 2792
    move-object v5, v0

    const/4 v6, 0x0

    invoke-super {v5, v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately(Z)V

    goto :goto_0

    .line 2791
    :catchall_1
    move-exception v5

    move-object v4, v5

    move-object v5, v2

    :try_start_2
    monitor-exit v5
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    move-object v5, v4

    throw v5

    .line 2795
    :cond_4
    move-object v5, v0

    move v6, v1

    invoke-super {v5, v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->closeImmediately(Z)V

    goto :goto_0
.end method

.method public closeWhenIdle()V
    .locals 3
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 2817
    move-object v0, p0

    move-object v1, v0

    const/4 v2, 0x0

    invoke-direct {v1, v2}, Leu/chainfire/libsuperuser/Shell$Threaded;->closeWhenIdle(Z)V

    .line 2818
    return-void
.end method

.method protected finalize()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .prologue
    .line 2743
    move-object v0, p0

    move-object v1, v0

    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Threaded;->pooled:Z

    if-eqz v1, :cond_0

    move-object v1, v0

    const/4 v2, 0x1

    iput-boolean v2, v1, Leu/chainfire/libsuperuser/Shell$Threaded;->closed:Z

    .line 2744
    :cond_0
    move-object v1, v0

    invoke-super {v1}, Leu/chainfire/libsuperuser/Shell$Interactive;->finalize()V

    .line 2745
    return-void
.end method

.method protected onClosed()V
    .locals 9

    .prologue
    .line 2829
    move-object v0, p0

    move-object v4, v0

    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->pooled:Z

    if-eqz v4, :cond_1

    .line 2830
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalledSync:Ljava/lang/Object;

    move-object v8, v4

    move-object v4, v8

    move-object v5, v8

    move-object v1, v5

    monitor-enter v4

    .line 2831
    move-object v4, v0

    :try_start_0
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    if-nez v4, :cond_0

    .line 2832
    move-object v4, v0

    const/4 v5, 0x1

    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z

    .line 2833
    move-object v4, v0

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Pool;->access$4700(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    .line 2835
    :cond_0
    move-object v4, v1

    monitor-exit v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 2838
    :cond_1
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onCloseCalledSync:Ljava/lang/Object;

    move-object v8, v4

    move-object v4, v8

    move-object v5, v8

    move-object v1, v5

    monitor-enter v4

    .line 2839
    move-object v4, v0

    :try_start_1
    iget-boolean v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onClosedCalled:Z

    if-eqz v4, :cond_2

    move-object v4, v1

    monitor-exit v4
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 2864
    :goto_0
    return-void

    .line 2835
    :catchall_0
    move-exception v4

    move-object v2, v4

    move-object v4, v1

    :try_start_2
    monitor-exit v4
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-object v4, v2

    throw v4

    .line 2840
    :cond_2
    move-object v4, v0

    const/4 v5, 0x1

    :try_start_3
    iput-boolean v5, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->onClosedCalled:Z

    .line 2841
    move-object v4, v1

    monitor-exit v4
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 2843
    move-object v4, v0

    invoke-super {v4}, Leu/chainfire/libsuperuser/Shell$Interactive;->onClosed()V

    .line 2845
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->handlerThread:Landroid/os/HandlerThread;

    invoke-virtual {v4}, Landroid/os/HandlerThread;->isAlive()Z

    move-result v4

    if-nez v4, :cond_3

    goto :goto_0

    .line 2841
    :catchall_1
    move-exception v4

    move-object v3, v4

    move-object v4, v1

    :try_start_4
    monitor-exit v4
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    move-object v4, v3

    throw v4

    .line 2846
    :cond_3
    move-object v4, v0

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->handler:Landroid/os/Handler;

    new-instance v5, Leu/chainfire/libsuperuser/Shell$Threaded$1;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Leu/chainfire/libsuperuser/Shell$Threaded$1;-><init>(Leu/chainfire/libsuperuser/Shell$Threaded;)V

    invoke-virtual {v4, v5}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v4

    .line 2864
    goto :goto_0
.end method

.method declared-synchronized wasPoolRemoveCalled()Z
    .locals 3

    .prologue
    .line 2821
    move-object v0, p0

    move-object v2, p0

    monitor-enter v2

    move-object v1, v0

    :try_start_0
    iget-boolean v1, v1, Leu/chainfire/libsuperuser/Shell$Threaded;->onPoolRemoveCalled:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move v0, v1

    monitor-exit v2

    return v0

    :catchall_0
    move-exception v0

    monitor-exit v2

    throw v0
.end method
