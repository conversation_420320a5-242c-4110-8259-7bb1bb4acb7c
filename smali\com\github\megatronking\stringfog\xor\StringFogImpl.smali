.class public final Lcom/github/megatronking/stringfog/xor/StringFogImpl;
.super Ljava/lang/Object;
.source "StringFogImpl.java"

# interfaces
.implements Lcom/github/megatronking/stringfog/IStringFog;


# static fields
.field public static final CHARSET_NAME_UTF_8:Ljava/lang/String; = "UTF-8"


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 77
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static decrypt(Ljava/lang/String;)Ljava/lang/String;
    .locals 7

    .prologue
    .line 33
    move-object v0, p0

    new-instance v3, Lcom/github/megatronking/stringfog/xor/StringFogImpl;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    invoke-direct {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;-><init>()V

    move-object v4, v0

    const-string v5, "UTF-8"

    invoke-virtual {v3, v4, v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method private static xor([BLjava/lang/String;)[B
    .locals 13

    .prologue
    .line 64
    move-object v0, p0

    move-object v1, p1

    move-object v8, v0

    array-length v8, v8

    move v3, v8

    .line 65
    move-object v8, v1

    invoke-virtual {v8}, Ljava/lang/String;->length()I

    move-result v8

    move v4, v8

    .line 66
    const/4 v8, 0x0

    move v5, v8

    .line 67
    const/4 v8, 0x0

    move v6, v8

    .line 68
    :goto_0
    move v8, v5

    move v9, v3

    if-lt v8, v9, :cond_0

    .line 76
    move-object v8, v0

    move-object v0, v8

    return-object v0

    .line 69
    :cond_0
    move v8, v6

    move v9, v4

    if-lt v8, v9, :cond_1

    .line 70
    const/4 v8, 0x0

    move v6, v8

    .line 72
    :cond_1
    move-object v8, v0

    move v9, v5

    move-object v10, v0

    move v11, v5

    aget-byte v10, v10, v11

    move-object v11, v1

    move v12, v6

    invoke-virtual {v11, v12}, Ljava/lang/String;->charAt(I)C

    move-result v11

    xor-int/2addr v10, v11

    int-to-byte v10, v10

    aput-byte v10, v8, v9

    .line 73
    add-int/lit8 v5, v5, 0x1

    .line 74
    add-int/lit8 v6, v6, 0x1

    goto :goto_0
.end method


# virtual methods
.method public decrypt(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 12

    .prologue
    .line 49
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    :try_start_0
    new-instance v7, Ljava/lang/String;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v1

    const/4 v10, 0x2

    invoke-static {v9, v10}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v9

    move-object v10, v2

    invoke-static {v9, v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->xor([BLjava/lang/String;)[B

    move-result-object v9

    const-string v10, "UTF-8"

    invoke-direct {v8, v9, v10}, Ljava/lang/String;-><init>([BLjava/lang/String;)V
    :try_end_0
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    move-object v4, v7

    .line 55
    :goto_0
    move-object v7, v4

    move-object v0, v7

    return-object v0

    .line 49
    :catch_0
    move-exception v7

    move-object v5, v7

    .line 53
    new-instance v7, Ljava/lang/String;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v1

    const/4 v10, 0x2

    invoke-static {v9, v10}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v9

    move-object v10, v2

    invoke-static {v9, v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->xor([BLjava/lang/String;)[B

    move-result-object v9

    invoke-direct {v8, v9}, Ljava/lang/String;-><init>([B)V

    move-object v4, v7

    goto :goto_0
.end method

.method public encrypt(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 12

    .prologue
    .line 38
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    :try_start_0
    new-instance v7, Ljava/lang/String;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v1

    const-string v10, "UTF-8"

    invoke-virtual {v9, v10}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v9

    move-object v10, v2

    invoke-static {v9, v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->xor([BLjava/lang/String;)[B

    move-result-object v9

    const/4 v10, 0x2

    invoke-static {v9, v10}, Landroid/util/Base64;->encode([BI)[B

    move-result-object v9

    invoke-direct {v8, v9}, Ljava/lang/String;-><init>([B)V
    :try_end_0
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    move-object v4, v7

    .line 44
    :goto_0
    move-object v7, v4

    move-object v0, v7

    return-object v0

    .line 38
    :catch_0
    move-exception v7

    move-object v5, v7

    .line 42
    new-instance v7, Ljava/lang/String;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v1

    invoke-virtual {v9}, Ljava/lang/String;->getBytes()[B

    move-result-object v9

    move-object v10, v2

    invoke-static {v9, v10}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->xor([BLjava/lang/String;)[B

    move-result-object v9

    const/4 v10, 0x2

    invoke-static {v9, v10}, Landroid/util/Base64;->encode([BI)[B

    move-result-object v9

    invoke-direct {v8, v9}, Ljava/lang/String;-><init>([B)V

    move-object v4, v7

    goto :goto_0
.end method

.method public overflow(Ljava/lang/String;Ljava/lang/String;)Z
    .locals 6

    .prologue
    .line 60
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v1

    if-eqz v4, :cond_0

    move-object v4, v1

    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v4

    const/4 v5, 0x4

    mul-int/lit8 v4, v4, 0x4

    const/4 v5, 0x3

    div-int/lit8 v4, v4, 0x3

    const v5, 0xffff

    if-ge v4, v5, :cond_1

    :cond_0
    const/4 v4, 0x0

    :goto_0
    move v0, v4

    return v0

    :cond_1
    const/4 v4, 0x1

    goto :goto_0
.end method
