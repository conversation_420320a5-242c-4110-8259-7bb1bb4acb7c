.class final Lcom/projectvb/Menu$4;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Menu;->addSwitch(Ljava/lang/String;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$ID:I


# direct methods
.method constructor <init>(I)V
    .locals 0

    .line 324
    iput p1, p0, Lcom/projectvb/Menu$4;->val$ID:I

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 2
    .param p1, "buttonView"    # Landroid/widget/CompoundButton;
    .param p2, "isChecked"    # Z

    .line 327
    iget v0, p0, Lcom/projectvb/Menu$4;->val$ID:I

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/projectvb/Menu;->ChangesID(II)V

    .line 328
    return-void
.end method
