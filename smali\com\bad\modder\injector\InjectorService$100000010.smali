.class Lcom/bad/modder/injector/InjectorService$100000010;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/widget/SeekBar$OnSeekBarChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000010"
.end annotation


# instance fields
.field private itv:Ljava/lang/String;

.field private final this$0:Lcom/bad/modder/injector/InjectorService;

.field private final val$i5:I

.field private final val$sb3:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

.field private final val$seekBar2:Landroid/widget/SeekBar;

.field private final val$str3:Ljava/lang/String;

.field private final val$textView:Landroid/widget/TextView;


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/InjectorService;ILandroid/widget/SeekBar;Lcom/bad/modder/injector/InjectorService$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;)V
    .locals 10

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object/from16 v6, p6

    move-object v8, v0

    invoke-direct {v8}, Ljava/lang/Object;-><init>()V

    move-object v8, v0

    move-object v9, v1

    iput-object v9, v8, Lcom/bad/modder/injector/InjectorService$100000010;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v8, v0

    move v9, v2

    iput v9, v8, Lcom/bad/modder/injector/InjectorService$100000010;->val$i5:I

    move-object v8, v0

    move-object v9, v3

    iput-object v9, v8, Lcom/bad/modder/injector/InjectorService$100000010;->val$seekBar2:Landroid/widget/SeekBar;

    move-object v8, v0

    move-object v9, v4

    iput-object v9, v8, Lcom/bad/modder/injector/InjectorService$100000010;->val$sb3:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move-object v8, v0

    move-object v9, v5

    iput-object v9, v8, Lcom/bad/modder/injector/InjectorService$100000010;->val$textView:Landroid/widget/TextView;

    move-object v8, v0

    move-object v9, v6

    iput-object v9, v8, Lcom/bad/modder/injector/InjectorService$100000010;->val$str3:Ljava/lang/String;

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/InjectorService$100000010;)Lcom/bad/modder/injector/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000010;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onProgressChanged(Landroid/widget/SeekBar;IZ)V
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/SeekBar;",
            "IZ)V"
        }
    .end annotation

    .prologue
    .line 717
    move-object v0, p0

    move-object v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object v7, v0

    iget v7, v7, Lcom/bad/modder/injector/InjectorService$100000010;->val$i5:I

    move v5, v7

    .line 718
    move v7, v2

    move v8, v5

    if-ge v7, v8, :cond_0

    .line 719
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000010;->val$seekBar2:Landroid/widget/SeekBar;

    move v8, v5

    invoke-virtual {v7, v8}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 720
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000010;->val$sb3:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move-object v8, v0

    iget v8, v8, Lcom/bad/modder/injector/InjectorService$100000010;->val$i5:I

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 721
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000010;->val$textView:Landroid/widget/TextView;

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000010;->val$str3:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    const-string v12, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "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"

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    move-object v10, v0

    iget v10, v10, Lcom/bad/modder/injector/InjectorService$100000010;->val$i5:I

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/L2dPGycnWlsWHh5VSBgWABxrDRkVfXkxDSRXU3oYdEloGABydVoiFRFlUGACFW9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaH8QbCJvUWRjEX8IAx0BVWoXEA9DXTtkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9xHRYfTnA4PwJkQA8mElR9BxEeFXEULHJ/ag07HmRCE2ElaVNmECt4dBEtH0FeZmQFZVBgYx5vajgWdRhRDwYTfmJkFSRnaxtsJXl9MwQoFXEQLAdVXQM/Ik96MQQjVQA8EQNCejA8A0h+DRETYXAPIidKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcoHGgdLn51WhQVcmhWAwIFSm0mGRBBSBosA2xeDSdzSEExNSdHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 725
    :goto_0
    return-void

    .line 724
    :cond_0
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000010;->val$sb3:Lcom/bad/modder/injector/InjectorService$InterfaceInt;

    move v8, v2

    invoke-interface {v7, v8}, Lcom/bad/modder/injector/InjectorService$InterfaceInt;->OnWrite(I)V

    .line 725
    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/InjectorService$100000010;->val$textView:Landroid/widget/TextView;

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v13, v9

    move-object v9, v13

    move-object v10, v13

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v13, v11

    move-object v11, v13

    move-object v12, v13

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    move-object v12, v0

    iget-object v12, v12, Lcom/bad/modder/injector/InjectorService$100000010;->val$str3:Ljava/lang/String;

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    const-string v12, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWAB5wHRkVaHAfAiQfWzEVK0FIDBApeVoiFRFoUAQOIn9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9ZEywfTnA4YAdgQBttJWwBOREeFXEULHZvagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE7OyRnaxt7H3xhBxIOFEAdBh9LbDsWKGVRJT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "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"

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    move v10, v2

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/L2dPGycnWlsWHh5VSBgWABxrDRkVfXkxDSRXU3oYdEloGABydVoiFRFlUGACFW9fAhUEGE8dBi17WR0VIGxRBDIRV30DH3ZrcREWB11dAzsva2oTOxNsaRodKBReFDwTaH8QbCJvUWRjEX8IAx0BVWoXEA9DXTtkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9xHRYfTnA4PwJkQA8mElR9BxEeFXEULHJ/ag07HmRCE2ElaVNmECt4dBEtH0FeZmQFZVBgYx5vajgWdRhRDwYTfmJkFSRnaxtsJXl9MwQoFXEQLAdVXQM/Ik96MQQjVQA8EQNCejA8A0h+DRETYXAPIidKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcoHGgdLn51WhQVcmhWAwIFSm0mGRBBSBosA2xeDSdzSEExNSdHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-static {v9}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0
.end method

.method public onStartTrackingTouch(Landroid/widget/SeekBar;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/SeekBar;",
            ")V"
        }
    .end annotation

    return-void
.end method

.method public onStopTrackingTouch(Landroid/widget/SeekBar;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/SeekBar;",
            ")V"
        }
    .end annotation

    return-void
.end method
