.class Leu/chainfire/libsuperuser/Shell$Interactive$8;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->open()Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;)V
    .locals 4

    .prologue
    .line 2178
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onLine(Ljava/lang/String;)V
    .locals 11
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .prologue
    .line 2182
    move-object v0, p0

    move-object v1, p1

    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v10, v6

    move-object v6, v10

    move-object v7, v10

    move-object v2, v7

    monitor-enter v6

    .line 2183
    move-object v6, v0

    :try_start_0
    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v6

    if-nez v6, :cond_0

    .line 2184
    move-object v6, v2

    monitor-exit v6

    .line 2208
    :goto_0
    return-void

    .line 2187
    :cond_0
    move-object v6, v1

    move-object v3, v6

    .line 2189
    move-object v6, v1

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v7

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v6

    move v4, v6

    .line 2190
    move v6, v4

    if-nez v6, :cond_4

    .line 2191
    const/4 v6, 0x0

    move-object v3, v6

    .line 2196
    :cond_1
    :goto_1
    move-object v6, v3

    if-eqz v6, :cond_2

    .line 2197
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v3

    const/4 v8, 0x1

    invoke-static {v6, v7, v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3800(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Z)V

    .line 2198
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v3

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4400(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-result-object v8

    const/4 v9, 0x1

    invoke-static {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4000(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Ljava/lang/Object;Z)V

    .line 2199
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v3

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v8

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Command;->access$2200(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;

    move-result-object v8

    const/4 v9, 0x1

    invoke-static {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4000(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Ljava/lang/Object;Z)V

    .line 2200
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v3

    move-object v8, v0

    iget-object v8, v8, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v8

    invoke-static {v8}, Leu/chainfire/libsuperuser/Shell$Command;->access$1900(Leu/chainfire/libsuperuser/Shell$Command;)Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;

    move-result-object v8

    const/4 v9, 0x1

    invoke-static {v6, v7, v8, v9}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4000(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;Ljava/lang/Object;Z)V

    .line 2203
    :cond_2
    move v6, v4

    if-ltz v6, :cond_3

    .line 2204
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v7, v0

    iget-object v7, v7, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$3100(Leu/chainfire/libsuperuser/Shell$Interactive;)Leu/chainfire/libsuperuser/Shell$Command;

    move-result-object v7

    invoke-static {v7}, Leu/chainfire/libsuperuser/Shell$Command;->access$2000(Leu/chainfire/libsuperuser/Shell$Command;)Ljava/lang/String;

    move-result-object v7

    invoke-static {v6, v7}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4502(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    .line 2205
    move-object v6, v0

    iget-object v6, v6, Leu/chainfire/libsuperuser/Shell$Interactive$8;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-static {v6}, Leu/chainfire/libsuperuser/Shell$Interactive;->access$4300(Leu/chainfire/libsuperuser/Shell$Interactive;)V

    .line 2207
    :cond_3
    move-object v6, v2

    monitor-exit v6

    .line 2208
    goto :goto_0

    .line 2192
    :cond_4
    move v6, v4

    if-lez v6, :cond_1

    .line 2193
    move-object v6, v1

    const/4 v7, 0x0

    move v8, v4

    invoke-virtual {v6, v7, v8}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v6

    move-object v3, v6

    goto :goto_1

    .line 2207
    :catchall_0
    move-exception v6

    move-object v5, v6

    move-object v6, v2

    monitor-exit v6
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v6, v5

    throw v6
.end method
