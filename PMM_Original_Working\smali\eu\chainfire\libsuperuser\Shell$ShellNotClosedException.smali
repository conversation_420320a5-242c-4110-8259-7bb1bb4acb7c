.class public Leu/chainfire/libsuperuser/Shell$ShellNotClosedException;
.super Ljava/lang/RuntimeException;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ShellNotClosedException"
.end annotation


# static fields
.field public static final EXCEPTION_NOT_CLOSED:Ljava/lang/String; = "Application did not close() interactive shell"


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 81
    move-object v0, p0

    move-object v1, v0

    const-string v2, "Application did not close() interactive shell"

    invoke-direct {v1, v2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    .line 82
    return-void
.end method
