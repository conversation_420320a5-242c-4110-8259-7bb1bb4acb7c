.class Lcom/bad/modder/injector/InjectorService$100000003;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/view/View$OnTouchListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000003"
.end annotation


# instance fields
.field private initialTouchX:F

.field private initialTouchY:F

.field private initialX:I

.field private initialY:I

.field private final this$0:Lcom/bad/modder/injector/InjectorService;


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/InjectorService;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/InjectorService$100000003;)Lcom/bad/modder/injector/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 12

    .prologue
    .line 598
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v8, v2

    invoke-virtual {v8}, Landroid/view/MotionEvent;->getAction()I

    move-result v8

    move v4, v8

    .line 599
    move v8, v4

    const/4 v9, 0x0

    if-ne v8, v9, :cond_0

    .line 600
    move-object v8, v0

    move-object v9, v0

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService;->menuparams:Landroid/view/WindowManager$LayoutParams;

    iget v9, v9, Landroid/view/WindowManager$LayoutParams;->x:I

    iput v9, v8, Lcom/bad/modder/injector/InjectorService$100000003;->initialX:I

    .line 601
    move-object v8, v0

    move-object v9, v0

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService;->menuparams:Landroid/view/WindowManager$LayoutParams;

    iget v9, v9, Landroid/view/WindowManager$LayoutParams;->y:I

    iput v9, v8, Lcom/bad/modder/injector/InjectorService$100000003;->initialY:I

    .line 602
    move-object v8, v0

    move-object v9, v2

    invoke-virtual {v9}, Landroid/view/MotionEvent;->getRawX()F

    move-result v9

    iput v9, v8, Lcom/bad/modder/injector/InjectorService$100000003;->initialTouchX:F

    .line 603
    move-object v8, v0

    move-object v9, v2

    invoke-virtual {v9}, Landroid/view/MotionEvent;->getRawY()F

    move-result v9

    iput v9, v8, Lcom/bad/modder/injector/InjectorService$100000003;->initialTouchY:F

    .line 605
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->collapse_view:Landroid/widget/RelativeLayout;

    const v9, 0x3f4ccccd    # 0.8f

    invoke-virtual {v8, v9}, Landroid/widget/RelativeLayout;->setAlpha(F)V

    .line 607
    const/4 v8, 0x1

    move v0, v8

    .line 624
    :goto_0
    return v0

    .line 608
    :cond_0
    move v8, v4

    const/4 v9, 0x1

    if-ne v8, v9, :cond_2

    .line 609
    move-object v8, v2

    invoke-virtual {v8}, Landroid/view/MotionEvent;->getRawX()F

    move-result v8

    move-object v9, v0

    iget v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->initialTouchX:F

    sub-float/2addr v8, v9

    float-to-int v8, v8

    move v5, v8

    .line 610
    move-object v8, v2

    invoke-virtual {v8}, Landroid/view/MotionEvent;->getRawY()F

    move-result v8

    move-object v9, v0

    iget v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->initialTouchY:F

    sub-float/2addr v8, v9

    float-to-int v8, v8

    move v6, v8

    .line 611
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->collapse_view:Landroid/widget/RelativeLayout;

    const v9, 0x3f4ccccd    # 0.8f

    invoke-virtual {v8, v9}, Landroid/widget/RelativeLayout;->setAlpha(F)V

    .line 613
    move v8, v5

    const/16 v9, 0xa

    if-ge v8, v9, :cond_1

    move v8, v6

    const/16 v9, 0xa

    if-ge v8, v9, :cond_1

    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    invoke-virtual {v8}, Lcom/bad/modder/injector/InjectorService;->isViewCollapsed()Z

    move-result v8

    if-eqz v8, :cond_1

    .line 614
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->collapse_view:Landroid/widget/RelativeLayout;

    const/16 v9, 0x8

    invoke-virtual {v8, v9}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 615
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->mExpanded:Landroid/widget/LinearLayout;

    const/4 v9, 0x0

    invoke-virtual {v8, v9}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 617
    :cond_1
    const/4 v8, 0x1

    move v0, v8

    goto :goto_0

    .line 618
    :cond_2
    move v8, v4

    const/4 v9, 0x2

    if-eq v8, v9, :cond_3

    .line 619
    const/4 v8, 0x0

    move v0, v8

    goto :goto_0

    .line 621
    :cond_3
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->menuparams:Landroid/view/WindowManager$LayoutParams;

    move-object v9, v0

    iget v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->initialX:I

    move-object v10, v2

    invoke-virtual {v10}, Landroid/view/MotionEvent;->getRawX()F

    move-result v10

    move-object v11, v0

    iget v11, v11, Lcom/bad/modder/injector/InjectorService$100000003;->initialTouchX:F

    sub-float/2addr v10, v11

    float-to-int v10, v10

    add-int/2addr v9, v10

    iput v9, v8, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 622
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->menuparams:Landroid/view/WindowManager$LayoutParams;

    move-object v9, v0

    iget v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->initialY:I

    move-object v10, v2

    invoke-virtual {v10}, Landroid/view/MotionEvent;->getRawY()F

    move-result v10

    move-object v11, v0

    iget v11, v11, Lcom/bad/modder/injector/InjectorService$100000003;->initialTouchY:F

    sub-float/2addr v10, v11

    float-to-int v10, v10

    add-int/2addr v9, v10

    iput v9, v8, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 623
    move-object v8, v0

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v8, v8, Lcom/bad/modder/injector/InjectorService;->mWindowManager:Landroid/view/WindowManager;

    move-object v9, v0

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v9, v9, Lcom/bad/modder/injector/InjectorService;->frameLayout:Landroid/widget/FrameLayout;

    move-object v10, v0

    iget-object v10, v10, Lcom/bad/modder/injector/InjectorService$100000003;->this$0:Lcom/bad/modder/injector/InjectorService;

    iget-object v10, v10, Lcom/bad/modder/injector/InjectorService;->menuparams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v8, v9, v10}, Landroid/view/WindowManager;->updateViewLayout(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 624
    const/4 v8, 0x1

    move v0, v8

    goto/16 :goto_0
.end method
