.class Lcom/projectvb/Menu$3;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnTouchListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;

.field private x:I

.field private y:I


# direct methods
.method constructor <init>(Lcom/projectvb/Menu;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Menu;

    .line 249
    iput-object p1, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 7
    .param p1, "v"    # Landroid/view/View;
    .param p2, "event"    # Landroid/view/MotionEvent;

    .line 257
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getAction()I

    move-result v0

    if-eqz v0, :cond_2

    const/4 v1, 0x1

    if-eq v0, v1, :cond_1

    const/4 v1, 0x2

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 265
    :cond_0
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawX()F

    move-result v0

    float-to-int v0, v0

    .line 266
    .local v0, "nowX":I
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawY()F

    move-result v1

    float-to-int v1, v1

    .line 267
    .local v1, "nowY":I
    iget v2, p0, Lcom/projectvb/Menu$3;->x:I

    sub-int v2, v0, v2

    .line 268
    .local v2, "movedX":I
    iget v3, p0, Lcom/projectvb/Menu$3;->y:I

    sub-int v3, v1, v3

    .line 269
    .local v3, "movedY":I
    iput v0, p0, Lcom/projectvb/Menu$3;->x:I

    .line 270
    iput v1, p0, Lcom/projectvb/Menu$3;->y:I

    .line 271
    iget-object v4, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v4}, Lcom/projectvb/Menu;->access$400(Lcom/projectvb/Menu;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v4

    iget-object v5, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v5}, Lcom/projectvb/Menu;->access$400(Lcom/projectvb/Menu;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v5

    iget v5, v5, Landroid/view/WindowManager$LayoutParams;->x:I

    add-int/2addr v5, v2

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 272
    iget-object v4, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v4}, Lcom/projectvb/Menu;->access$400(Lcom/projectvb/Menu;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v4

    iget-object v5, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v5}, Lcom/projectvb/Menu;->access$400(Lcom/projectvb/Menu;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v5

    iget v5, v5, Landroid/view/WindowManager$LayoutParams;->y:I

    add-int/2addr v5, v3

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 273
    iget-object v4, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v4}, Lcom/projectvb/Menu;->access$500(Lcom/projectvb/Menu;)Landroid/view/WindowManager;

    move-result-object v4

    iget-object v5, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v5}, Lcom/projectvb/Menu;->access$300(Lcom/projectvb/Menu;)Landroid/widget/FrameLayout;

    move-result-object v5

    iget-object v6, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v6}, Lcom/projectvb/Menu;->access$400(Lcom/projectvb/Menu;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v6

    invoke-interface {v4, v5, v6}, Landroid/view/WindowManager;->updateViewLayout(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 274
    goto :goto_0

    .line 277
    .end local v0    # "nowX":I
    .end local v1    # "nowY":I
    .end local v2    # "movedX":I
    .end local v3    # "movedY":I
    :cond_1
    iget-object v0, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$300(Lcom/projectvb/Menu;)Landroid/widget/FrameLayout;

    move-result-object v0

    const v1, 0x3f666666    # 0.9f

    invoke-virtual {v0, v1}, Landroid/widget/FrameLayout;->setAlpha(F)V

    .line 278
    goto :goto_0

    .line 259
    :cond_2
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawX()F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lcom/projectvb/Menu$3;->x:I

    .line 260
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawY()F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lcom/projectvb/Menu$3;->y:I

    .line 261
    iget-object v0, p0, Lcom/projectvb/Menu$3;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$300(Lcom/projectvb/Menu;)Landroid/widget/FrameLayout;

    move-result-object v0

    const v1, 0x3f4ccccd    # 0.8f

    invoke-virtual {v0, v1}, Landroid/widget/FrameLayout;->setAlpha(F)V

    .line 262
    nop

    .line 282
    :goto_0
    const/4 v0, 0x0

    return v0
.end method
