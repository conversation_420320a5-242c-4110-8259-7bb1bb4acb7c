.class Lcom/projectvb/InjectorService$100000014;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000014"
.end annotation


# instance fields
.field private final this$0:Lcom/projectvb/InjectorService;

.field private final val$sw:Lcom/projectvb/InjectorService$InterfaceBool;


# direct methods
.method constructor <init>(Lcom/projectvb/InjectorService;Lcom/projectvb/InjectorService$InterfaceBool;)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lcom/projectvb/InjectorService$100000014;->this$0:Lcom/projectvb/InjectorService;

    move-object v4, v0

    move-object v5, v2

    iput-object v5, v4, Lcom/projectvb/InjectorService$100000014;->val$sw:Lcom/projectvb/InjectorService$InterfaceBool;

    return-void
.end method

.method static access$0(Lcom/projectvb/InjectorService$100000014;)Lcom/projectvb/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000014;->this$0:Lcom/projectvb/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/CompoundButton;",
            "Z)V"
        }
    .end annotation

    .prologue
    .line 964
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lcom/projectvb/InjectorService$100000014;->val$sw:Lcom/projectvb/InjectorService$InterfaceBool;

    move v5, v2

    invoke-interface {v4, v5}, Lcom/projectvb/InjectorService$InterfaceBool;->OnWrite(Z)V

    .line 965
    move v4, v2

    if-eqz v4, :cond_0

    .line 967
    :goto_0
    return-void

    :cond_0
    goto :goto_0
.end method
