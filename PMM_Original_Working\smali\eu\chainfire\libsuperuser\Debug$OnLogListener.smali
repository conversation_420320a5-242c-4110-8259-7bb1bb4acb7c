.class public interface abstract Leu/chainfire/libsuperuser/Debug$OnLogListener;
.super Ljava/lang/Object;
.source "Debug.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Debug;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnLogListener"
.end annotation


# virtual methods
.method public abstract onLog(ILjava/lang/String;Ljava/lang/String;)V
.end method
