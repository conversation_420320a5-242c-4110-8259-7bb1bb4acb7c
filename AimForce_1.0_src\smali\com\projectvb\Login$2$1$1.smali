.class Lcom/projectvb/Login$2$1$1;
.super Ljava/lang/Object;
.source "Login.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Login$2$1;->run()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$2:Lcom/projectvb/Login$2$1;


# direct methods
.method constructor <init>(Lcom/projectvb/Login$2$1;)V
    .locals 0
    .param p1, "this$2"    # Lcom/projectvb/Login$2$1;

    .line 181
    iput-object p1, p0, Lcom/projectvb/Login$2$1$1;->this$2:Lcom/projectvb/Login$2$1;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 184
    new-instance v0, Lcom/projectvb/Menu;

    iget-object v1, p0, Lcom/projectvb/Login$2$1$1;->this$2:Lcom/projectvb/Login$2$1;

    iget-object v1, v1, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v1, v1, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v1}, Lcom/projectvb/Login;->access$700(Lcom/projectvb/Login;)Landroid/content/Context;

    move-result-object v1

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/projectvb/Menu;-><init>(Landroid/content/Context;I)V

    .line 185
    return-void
.end method
