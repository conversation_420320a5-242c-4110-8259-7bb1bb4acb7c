.class public Lpmm/by/p2077kng/hacker/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lpmm/by/p2077kng/hacker/MainActivity$100000000;,
        Lpmm/by/p2077kng/hacker/MainActivity$100000001;,
        Lpmm/by/p2077kng/hacker/MainActivity$100000002;,
        Lpmm/by/p2077kng/hacker/MainActivity$100000003;,
        Lpmm/by/p2077kng/hacker/MainActivity$100000004;,
        Lpmm/by/p2077kng/hacker/MainActivity$100000005;
    }
.end annotation


# static fields
.field public static FF:Z

.field public static StruckBase:Landroid/graphics/Typeface;

.field public static Struck_:Ljava/lang/String;


# instance fields
.field private open:Ljava/io/InputStream;

.field prefs:Lpmm/by/p2077kng/hacker/Prefs;

.field public final sGameActivity:Ljava/lang/String;

.field private tempVideoFile:Ljava/io/File;

.field private videoView:Landroid/widget/VideoView;


# direct methods
.method static final constructor <clinit>()V
    .locals 3

    const-string v2, "font.ttf"

    sput-object v2, Lpmm/by/p2077kng/hacker/MainActivity;->Struck_:Ljava/lang/String;

    const/4 v2, 0x0

    sput-boolean v2, Lpmm/by/p2077kng/hacker/MainActivity;->FF:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 4

    .prologue
    .line 409
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Landroid/app/Activity;-><init>()V

    move-object v2, v0

    const-string v3, "com.dts.freefireth.FFLoginActivity"

    iput-object v3, v2, Lpmm/by/p2077kng/hacker/MainActivity;->sGameActivity:Ljava/lang/String;

    return-void
.end method

.method private native StartGame()Ljava/lang/String;
.end method

.method private native TG()Ljava/lang/String;
.end method

.method private native TitleP()Ljava/lang/String;
.end method

.method private native TitleR()Ljava/lang/String;
.end method

.method private native YT()Ljava/lang/String;
.end method

.method public static checkVPN(Landroid/content/Context;)Z
    .locals 6

    .prologue
    .line 164
    move-object v0, p0

    move-object v4, v0

    const-string v5, "connectivity"

    invoke-virtual {v4, v5}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroid/net/ConnectivityManager;

    move-object v2, v4

    .line 165
    move-object v4, v2

    const/16 v5, 0x11

    invoke-virtual {v4, v5}, Landroid/net/ConnectivityManager;->getNetworkInfo(I)Landroid/net/NetworkInfo;

    move-result-object v4

    invoke-virtual {v4}, Landroid/net/NetworkInfo;->isConnectedOrConnecting()Z

    move-result v4

    move v0, v4

    return v0
.end method

.method private native colourLoad()Ljava/lang/String;
.end method

.method private native colourMain()Ljava/lang/String;
.end method

.method private copyAssetToFile(Ljava/lang/String;Ljava/io/File;)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/io/File;",
            ")V"
        }
    .end annotation

    .prologue
    .line 107
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    const/4 v14, 0x0

    check-cast v14, Ljava/io/InputStream;

    move-object v4, v14

    .line 108
    const/4 v14, 0x0

    check-cast v14, Ljava/io/FileOutputStream;

    move-object v5, v14

    .line 110
    move-object v14, v0

    :try_start_0
    invoke-virtual {v14}, Lpmm/by/p2077kng/hacker/MainActivity;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v14

    move-object v15, v1

    invoke-virtual {v14, v15}, Landroid/content/res/AssetManager;->open(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v14

    move-object v4, v14

    .line 111
    new-instance v14, Ljava/io/FileOutputStream;

    move-object/from16 v18, v14

    move-object/from16 v14, v18

    move-object/from16 v15, v18

    move-object/from16 v16, v2

    invoke-direct/range {v15 .. v16}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V

    move-object v5, v14

    .line 112
    const/16 v14, 0x1000

    new-array v14, v14, [B

    move-object v10, v14

    .line 114
    :goto_0
    move-object v14, v4

    move-object v15, v10

    invoke-virtual {v14, v15}, Ljava/io/InputStream;->read([B)I
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-result v14

    move/from16 v18, v14

    move/from16 v14, v18

    move/from16 v15, v18

    move v11, v15

    const/4 v15, -0x1

    if-ne v14, v15, :cond_2

    .line 121
    :goto_1
    move-object v14, v4

    if-eqz v14, :cond_0

    move-object v14, v4

    :try_start_1
    invoke-virtual {v14}, Ljava/io/InputStream;->close()V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_3

    .line 124
    :cond_0
    :goto_2
    move-object v14, v5

    if-eqz v14, :cond_1

    move-object v14, v5

    :try_start_2
    invoke-virtual {v14}, Ljava/io/FileOutputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_4

    :cond_1
    :goto_3
    return-void

    .line 115
    :cond_2
    move-object v14, v5

    move-object v15, v10

    const/16 v16, 0x0

    move/from16 v17, v11

    :try_start_3
    invoke-virtual/range {v14 .. v17}, Ljava/io/FileOutputStream;->write([BII)V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    goto :goto_0

    .line 114
    :catch_0
    move-exception v14

    move-object v10, v14

    .line 118
    move-object v14, v10

    :try_start_4
    invoke-virtual {v14}, Ljava/io/IOException;->printStackTrace()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v14

    move-object v6, v14

    .line 121
    move-object v14, v4

    if-eqz v14, :cond_3

    move-object v14, v4

    :try_start_5
    invoke-virtual {v14}, Ljava/io/InputStream;->close()V
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_1

    .line 124
    :cond_3
    :goto_4
    move-object v14, v5

    if-eqz v14, :cond_4

    move-object v14, v5

    :try_start_6
    invoke-virtual {v14}, Ljava/io/FileOutputStream;->close()V
    :try_end_6
    .catch Ljava/io/IOException; {:try_start_6 .. :try_end_6} :catch_2

    :cond_4
    :goto_5
    move-object v14, v6

    throw v14

    .line 121
    :catch_1
    move-exception v14

    move-object v12, v14

    goto :goto_4

    .line 124
    :catch_2
    move-exception v14

    move-object v12, v14

    goto :goto_5

    .line 121
    :catch_3
    move-exception v14

    move-object v12, v14

    goto :goto_2

    .line 124
    :catch_4
    move-exception v14

    move-object v12, v14

    goto :goto_3
.end method

.method private dialog_login_nexus()Landroid/view/View;
    .locals 28

    .prologue
    .line 177
    move-object/from16 v2, p0

    new-instance v21, Landroid/widget/FrameLayout;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v4, v21

    .line 178
    move-object/from16 v21, v4

    new-instance v22, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v22

    move-object/from16 v22, v27

    move-object/from16 v23, v27

    const/16 v24, -0x1

    const/16 v25, -0x2

    invoke-direct/range {v23 .. v25}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v21 .. v22}, Landroid/widget/FrameLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 179
    new-instance v21, Landroid/widget/LinearLayout;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v5, v21

    .line 180
    move-object/from16 v21, v5

    new-instance v22, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v22

    move-object/from16 v22, v27

    move-object/from16 v23, v27

    const/16 v24, -0x1

    const/16 v25, -0x2

    invoke-direct/range {v23 .. v25}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 181
    move-object/from16 v21, v5

    const-string v22, "#292828"

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 182
    move-object/from16 v21, v5

    const/16 v22, 0x1

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 184
    move-object/from16 v21, v2

    move-object/from16 v22, v2

    :try_start_0
    invoke-virtual/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v22

    const-string v23, "script.jfif"

    invoke-virtual/range {v22 .. v23}, Landroid/content/res/AssetManager;->open(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v22

    move-object/from16 v0, v22

    move-object/from16 v1, v21

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/MainActivity;->open:Ljava/io/InputStream;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 189
    :goto_0
    move-object/from16 v21, v5

    move-object/from16 v22, v2

    move-object/from16 v0, v22

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/MainActivity;->open:Ljava/io/InputStream;

    move-object/from16 v22, v0

    const/16 v23, 0x0

    check-cast v23, Ljava/lang/String;

    invoke-static/range {v22 .. v23}, Landroid/graphics/drawable/Drawable;->createFromStream(Ljava/io/InputStream;Ljava/lang/String;)Landroid/graphics/drawable/Drawable;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 190
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    const/16 v24, 0x28

    invoke-direct/range {v23 .. v24}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v23

    move-object/from16 v24, v2

    const/16 v25, 0x28

    invoke-direct/range {v24 .. v25}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v24

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v6, v21

    .line 191
    move-object/from16 v21, v6

    const/16 v22, 0x5

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 192
    new-instance v21, Landroid/widget/Button;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    move-object/from16 v7, v21

    .line 193
    move-object/from16 v21, v7

    const-string v22, "btnFechar"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setTag(Ljava/lang/Object;)V

    .line 194
    move-object/from16 v21, v7

    move-object/from16 v22, v6

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 195
    move-object/from16 v21, v7

    const-string v22, "X"

    check-cast v22, Ljava/lang/CharSequence;

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 196
    move-object/from16 v21, v7

    const-string v22, "#ff0018"

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 197
    move-object/from16 v21, v7

    const-string v22, "#ffffff"

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setTextColor(I)V

    .line 198
    move-object/from16 v21, v7

    const/16 v22, 0x0

    check-cast v22, Landroid/graphics/Typeface;

    const/16 v23, 0x1

    invoke-virtual/range {v21 .. v23}, Landroid/widget/Button;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 199
    move-object/from16 v21, v7

    const/16 v22, 0x2

    const/high16 v23, 0x41a00000    # 20.0f

    invoke-virtual/range {v21 .. v23}, Landroid/widget/Button;->setTextSize(IF)V

    .line 200
    move-object/from16 v21, v7

    new-instance v22, Lpmm/by/p2077kng/hacker/MainActivity$100000002;

    move-object/from16 v27, v22

    move-object/from16 v22, v27

    move-object/from16 v23, v27

    move-object/from16 v24, v2

    invoke-direct/range {v23 .. v24}, Lpmm/by/p2077kng/hacker/MainActivity$100000002;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;)V

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 206
    move-object/from16 v21, v2

    invoke-virtual/range {v21 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->getBaseContext()Landroid/content/Context;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Landroid/content/Context;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v21

    check-cast v21, Landroid/content/res/AssetManager;

    sget-object v22, Lpmm/by/p2077kng/hacker/MainActivity;->Struck_:Ljava/lang/String;

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v21 .. v22}, Landroid/graphics/Typeface;->createFromAsset(Landroid/content/res/AssetManager;Ljava/lang/String;)Landroid/graphics/Typeface;

    move-result-object v21

    sput-object v21, Lpmm/by/p2077kng/hacker/MainActivity;->StruckBase:Landroid/graphics/Typeface;

    .line 207
    new-instance v21, Landroid/widget/TextView;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v8, v21

    .line 208
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x2

    const/16 v24, -0x2

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v21

    .line 209
    move-object/from16 v21, v9

    const/16 v22, 0x11

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 210
    move-object/from16 v21, v8

    move-object/from16 v22, v9

    invoke-virtual/range {v21 .. v22}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 211
    move-object/from16 v21, v8

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->StartGame()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/CharSequence;

    invoke-virtual/range {v21 .. v22}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 212
    move-object/from16 v21, v8

    const/16 v22, 0x0

    const/16 v23, 0x5

    const/16 v24, 0x0

    const/16 v25, 0x5

    invoke-virtual/range {v21 .. v25}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 213
    move-object/from16 v21, v8

    const/16 v22, 0x1

    invoke-virtual/range {v21 .. v22}, Landroid/widget/TextView;->setGravity(I)V

    .line 214
    move-object/from16 v21, v8

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->colourLoad()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/TextView;->setTextColor(I)V

    .line 215
    move-object/from16 v21, v8

    const/high16 v22, 0x42200000    # 40.0f

    invoke-virtual/range {v21 .. v22}, Landroid/widget/TextView;->setTextSize(F)V

    .line 216
    move-object/from16 v21, v8

    sget-object v22, Lpmm/by/p2077kng/hacker/MainActivity;->StruckBase:Landroid/graphics/Typeface;

    invoke-virtual/range {v21 .. v22}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 217
    new-instance v21, Landroid/widget/EditText;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/EditText;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v21

    .line 218
    move-object/from16 v21, v10

    const-string v22, "edtUsuario"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setTag(Ljava/lang/Object;)V

    .line 219
    move-object/from16 v21, v10

    const-string v22, "Enter user"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setHint(Ljava/lang/CharSequence;)V

    .line 220
    move-object/from16 v21, v10

    const-string v22, "#ffffff"

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setTextColor(I)V

    .line 221
    move-object/from16 v21, v10

    const-string v22, "#ffffff"

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setHintTextColor(I)V

    .line 222
    move-object/from16 v21, v10

    invoke-virtual/range {v21 .. v21}, Landroid/widget/EditText;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v21

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->colourLoad()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    sget-object v23, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual/range {v21 .. v23}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 223
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x1

    const/16 v24, -0x2

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v21

    .line 224
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout$LayoutParams;->setMarginStart(I)V

    .line 225
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x10

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 226
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout$LayoutParams;->setMarginEnd(I)V

    .line 227
    move-object/from16 v21, v10

    move-object/from16 v22, v9

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 228
    new-instance v21, Landroid/widget/EditText;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/EditText;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v21

    .line 229
    move-object/from16 v21, v11

    const-string v22, "edtSenha"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setTag(Ljava/lang/Object;)V

    .line 230
    move-object/from16 v21, v11

    const-string v22, "Enter pass"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setHint(Ljava/lang/CharSequence;)V

    .line 231
    move-object/from16 v21, v11

    const-string v22, "#ffffff"

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setTextColor(I)V

    .line 232
    move-object/from16 v21, v11

    const-string v22, "#ffffff"

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setHintTextColor(I)V

    .line 233
    move-object/from16 v21, v11

    invoke-virtual/range {v21 .. v21}, Landroid/widget/EditText;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v21

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->colourLoad()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    sget-object v23, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual/range {v21 .. v23}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 234
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout$LayoutParams;->setMarginStart(I)V

    .line 235
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x10

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 236
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout$LayoutParams;->setMarginEnd(I)V

    .line 237
    move-object/from16 v21, v11

    move-object/from16 v22, v9

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 238
    new-instance v21, Landroid/widget/LinearLayout;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v12, v21

    .line 239
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x2

    const/16 v24, -0x2

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v13, v21

    .line 240
    move-object/from16 v21, v13

    const/high16 v22, 0x3f800000    # 1.0f

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->weight:F

    .line 241
    move-object/from16 v21, v12

    move-object/from16 v22, v13

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 242
    new-instance v21, Landroid/widget/CheckBox;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/CheckBox;-><init>(Landroid/content/Context;)V

    move-object/from16 v14, v21

    .line 243
    move-object/from16 v21, v14

    const-string v22, "cbLembrar"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setTag(Ljava/lang/Object;)V

    .line 244
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x2

    const/16 v24, -0x2

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v21

    .line 245
    move-object/from16 v21, v9

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move-object/from16 v23, v2

    const/16 v24, 0x8

    invoke-direct/range {v23 .. v24}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v23

    const/16 v24, 0x0

    move-object/from16 v25, v2

    const/16 v26, 0x8

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v25

    invoke-virtual/range {v21 .. v25}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 246
    move-object/from16 v21, v14

    move-object/from16 v22, v9

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 247
    move-object/from16 v21, v14

    const-string v22, "Remember"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setText(Ljava/lang/CharSequence;)V

    .line 248
    move-object/from16 v21, v14

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->colourLoad()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setTextColor(I)V

    .line 249
    move-object/from16 v21, v14

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->colourLoad()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-static/range {v22 .. v22}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setButtonTintList(Landroid/content/res/ColorStateList;)V

    .line 250
    move-object/from16 v21, v2

    invoke-static/range {v21 .. v21}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v21

    const-string v22, "save"

    invoke-virtual/range {v21 .. v22}, Lpmm/by/p2077kng/hacker/Prefs;->read(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v21

    const-string v22, "1"

    invoke-virtual/range {v21 .. v22}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v21

    if-eqz v21, :cond_0

    .line 251
    move-object/from16 v21, v10

    move-object/from16 v22, v2

    invoke-static/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v22

    const-string v23, "USER_Sv"

    invoke-virtual/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/Prefs;->read(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 252
    move-object/from16 v21, v11

    move-object/from16 v22, v2

    invoke-static/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v22

    const-string v23, "PASS_Sv"

    invoke-virtual/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/Prefs;->read(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 253
    move-object/from16 v21, v14

    const/16 v22, 0x1

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setChecked(Z)V

    .line 257
    :goto_1
    move-object/from16 v21, v14

    new-instance v22, Lpmm/by/p2077kng/hacker/MainActivity$100000003;

    move-object/from16 v27, v22

    move-object/from16 v22, v27

    move-object/from16 v23, v27

    move-object/from16 v24, v2

    move-object/from16 v25, v10

    move-object/from16 v26, v11

    invoke-direct/range {v23 .. v26}, Lpmm/by/p2077kng/hacker/MainActivity$100000003;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;Landroid/widget/EditText;Landroid/widget/EditText;)V

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 272
    move-object/from16 v21, v12

    move-object/from16 v22, v14

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 273
    new-instance v21, Landroid/widget/LinearLayout;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v15, v21

    .line 274
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x1

    const/16 v24, -0x2

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v16, v21

    .line 275
    move-object/from16 v21, v16

    const/high16 v22, 0x3f800000    # 1.0f

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->weight:F

    .line 276
    move-object/from16 v21, v15

    move-object/from16 v22, v16

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 277
    move-object/from16 v21, v15

    const/16 v22, 0x1

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 278
    new-instance v21, Landroid/widget/Button;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    move-object/from16 v17, v21

    .line 279
    move-object/from16 v21, v17

    const-string v22, "btnEntrar"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setTag(Ljava/lang/Object;)V

    .line 280
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x2

    move-object/from16 v24, v2

    const/16 v25, 0x28

    invoke-direct/range {v24 .. v25}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v24

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v18, v21

    .line 281
    move-object/from16 v21, v18

    const/16 v22, 0x5

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 282
    move-object/from16 v21, v18

    const/high16 v22, 0x3f800000    # 1.0f

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->weight:F

    .line 283
    move-object/from16 v21, v18

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 284
    move-object/from16 v21, v18

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->rightMargin:I

    .line 285
    move-object/from16 v21, v18

    move-object/from16 v22, v2

    const/16 v23, 0x8

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move/from16 v0, v22

    move-object/from16 v1, v21

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->bottomMargin:I

    .line 286
    move-object/from16 v21, v17

    move-object/from16 v22, v18

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 287
    move-object/from16 v21, v17

    const-string v22, "Enter"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 288
    move-object/from16 v21, v17

    move-object/from16 v22, v2

    invoke-direct/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->colourLoad()Ljava/lang/String;

    move-result-object v22

    check-cast v22, Ljava/lang/String;

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setTextColor(I)V

    .line 289
    move-object/from16 v21, v17

    const/16 v22, 0x2

    const/high16 v23, 0x41400000    # 12.0f

    invoke-virtual/range {v21 .. v23}, Landroid/widget/Button;->setTextSize(IF)V

    .line 290
    move-object/from16 v21, v17

    move-object/from16 v22, v2

    invoke-static/range {v22 .. v22}, Lpmm/by/p2077kng/hacker/P2077KNG;->nosize(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 291
    move-object/from16 v21, v17

    const/16 v22, 0x0

    check-cast v22, Landroid/graphics/Typeface;

    sget-object v23, Landroid/graphics/Typeface;->MONOSPACE:Landroid/graphics/Typeface;

    invoke-virtual/range {v23 .. v23}, Landroid/graphics/Typeface;->getStyle()I

    move-result v23

    invoke-virtual/range {v21 .. v23}, Landroid/widget/Button;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 292
    move-object/from16 v21, v17

    new-instance v22, Lpmm/by/p2077kng/hacker/MainActivity$100000004;

    move-object/from16 v27, v22

    move-object/from16 v22, v27

    move-object/from16 v23, v27

    move-object/from16 v24, v2

    move-object/from16 v25, v10

    move-object/from16 v26, v11

    invoke-direct/range {v23 .. v26}, Lpmm/by/p2077kng/hacker/MainActivity$100000004;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;Landroid/widget/EditText;Landroid/widget/EditText;)V

    invoke-virtual/range {v21 .. v22}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 303
    move-object/from16 v21, v15

    move-object/from16 v22, v17

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 304
    move-object/from16 v21, v5

    move-object/from16 v22, v7

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 305
    move-object/from16 v21, v5

    move-object/from16 v22, v8

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 306
    move-object/from16 v21, v5

    move-object/from16 v22, v10

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 307
    move-object/from16 v21, v5

    move-object/from16 v22, v11

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 308
    move-object/from16 v21, v5

    move-object/from16 v22, v12

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 309
    move-object/from16 v21, v5

    move-object/from16 v22, v15

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 310
    new-instance v21, Landroid/widget/LinearLayout;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v15, v21

    .line 311
    move-object/from16 v21, v15

    const-string v22, "ll_progress"

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setTag(Ljava/lang/Object;)V

    .line 312
    new-instance v21, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    const/16 v23, -0x1

    const/16 v24, -0x1

    invoke-direct/range {v22 .. v24}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v16, v21

    .line 313
    move-object/from16 v21, v15

    const/16 v22, 0x8

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 314
    move-object/from16 v21, v15

    const-string v22, "#99FFFFFF"

    invoke-static/range {v22 .. v22}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v22

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 315
    move-object/from16 v21, v15

    const/16 v22, 0x1

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 316
    move-object/from16 v21, v15

    const/16 v22, 0x11

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 317
    move-object/from16 v21, v15

    const/16 v22, 0x1

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setClickable(Z)V

    .line 318
    move-object/from16 v21, v15

    move-object/from16 v22, v16

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 319
    new-instance v21, Landroid/widget/ProgressBar;

    move-object/from16 v27, v21

    move-object/from16 v21, v27

    move-object/from16 v22, v27

    move-object/from16 v23, v2

    invoke-direct/range {v22 .. v23}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;)V

    move-object/from16 v19, v21

    .line 320
    move-object/from16 v21, v19

    new-instance v22, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v27, v22

    move-object/from16 v22, v27

    move-object/from16 v23, v27

    const/16 v24, -0x2

    const/16 v25, -0x2

    invoke-direct/range {v23 .. v25}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v21 .. v22}, Landroid/widget/ProgressBar;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 321
    move-object/from16 v21, v15

    move-object/from16 v22, v19

    invoke-virtual/range {v21 .. v22}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 322
    move-object/from16 v21, v4

    move-object/from16 v22, v5

    invoke-virtual/range {v21 .. v22}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 323
    move-object/from16 v21, v4

    move-object/from16 v22, v15

    invoke-virtual/range {v21 .. v22}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 324
    move-object/from16 v21, v4

    move-object/from16 v2, v21

    return-object v2

    .line 184
    :catch_0
    move-exception v21

    move-object/from16 v6, v21

    .line 186
    move-object/from16 v21, v6

    invoke-virtual/range {v21 .. v21}, Ljava/io/IOException;->printStackTrace()V

    .line 187
    move-object/from16 v21, v2

    const/16 v22, 0x0

    check-cast v22, Ljava/io/InputStream;

    move-object/from16 v0, v22

    move-object/from16 v1, v21

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/MainActivity;->open:Ljava/io/InputStream;

    goto/16 :goto_0

    .line 255
    :cond_0
    move-object/from16 v21, v14

    const/16 v22, 0x0

    invoke-virtual/range {v21 .. v22}, Landroid/widget/CheckBox;->setChecked(Z)V

    goto/16 :goto_1
.end method

.method private dp(I)I
    .locals 6

    .prologue
    .line 404
    move-object v0, p0

    move v1, p1

    const/4 v3, 0x1

    move v4, v1

    int-to-float v4, v4

    move-object v5, v0

    invoke-virtual {v5}, Lpmm/by/p2077kng/hacker/MainActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    invoke-virtual {v5}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v5

    invoke-static {v3, v4, v5}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v3

    float-to-int v3, v3

    move v0, v3

    return v0
.end method

.method private getResID(Ljava/lang/String;Ljava/lang/String;)I
    .locals 8

    .prologue
    .line 408
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/MainActivity;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    move-object v5, v1

    move-object v6, v2

    move-object v7, v0

    invoke-virtual {v7}, Lpmm/by/p2077kng/hacker/MainActivity;->getPackageName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v4, v5, v6, v7}, Landroid/content/res/Resources;->getIdentifier(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I

    move-result v4

    move v0, v4

    return v0
.end method

.method public static isNativeLibLoaded(Landroid/content/Context;Ljava/lang/String;)Z
    .locals 10

    .prologue
    .line 71
    move-object v0, p0

    move-object v1, p1

    move-object v6, v1

    invoke-static {v6}, Ljava/lang/System;->mapLibraryName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    move-object v3, v6

    .line 72
    new-instance v6, Ljava/lang/StringBuffer;

    move-object v9, v6

    move-object v6, v9

    move-object v7, v9

    invoke-direct {v7}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v7, Ljava/lang/StringBuffer;

    move-object v9, v7

    move-object v7, v9

    move-object v8, v9

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    move-object v8, v0

    invoke-virtual {v8}, Landroid/content/Context;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v8

    iget-object v8, v8, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "/"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    move-object v7, v3

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    move-object v4, v6

    .line 73
    new-instance v6, Ljava/io/File;

    move-object v9, v6

    move-object v6, v9

    move-object v7, v9

    move-object v8, v4

    invoke-direct {v7, v8}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v6}, Ljava/io/File;->exists()Z

    move-result v6

    move v0, v6

    return v0
.end method

.method private revendedores()Landroid/view/View;
    .locals 26

    .prologue
    .line 328
    move-object/from16 v2, p0

    new-instance v19, Landroid/widget/ScrollView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    move-object/from16 v4, v19

    .line 329
    move-object/from16 v19, v4

    new-instance v20, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v20

    move-object/from16 v20, v25

    move-object/from16 v21, v25

    const/16 v22, -0x1

    const/16 v23, -0x1

    invoke-direct/range {v21 .. v23}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/ScrollView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 330
    new-instance v19, Landroid/widget/LinearLayout;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v5, v19

    .line 331
    new-instance v19, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    const/16 v21, -0x1

    const/16 v22, -0x1

    invoke-direct/range {v20 .. v22}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v6, v19

    .line 332
    move-object/from16 v19, v6

    move-object/from16 v20, v2

    const/16 v21, 0xf

    invoke-direct/range {v20 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v20

    move-object/from16 v21, v2

    const/16 v22, 0xf

    invoke-direct/range {v21 .. v22}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v21

    move-object/from16 v22, v2

    const/16 v23, 0xf

    invoke-direct/range {v22 .. v23}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v22

    move-object/from16 v23, v2

    const/16 v24, 0xf

    invoke-direct/range {v23 .. v24}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v23

    invoke-virtual/range {v19 .. v23}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 333
    move-object/from16 v19, v5

    move-object/from16 v20, v6

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 334
    move-object/from16 v19, v5

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->setVerticalScrollBarEnabled(Z)V

    .line 335
    move-object/from16 v19, v5

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->setHorizontalScrollBarEnabled(Z)V

    .line 336
    move-object/from16 v19, v5

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 337
    move-object/from16 v19, v5

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 338
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v7, v19

    .line 339
    new-instance v19, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    const/16 v21, -0x2

    const/16 v22, -0x2

    invoke-direct/range {v20 .. v22}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v8, v19

    .line 340
    move-object/from16 v19, v8

    move-object/from16 v20, v2

    const/16 v21, 0xa

    invoke-direct/range {v20 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v20

    move/from16 v0, v20

    move-object/from16 v1, v19

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 341
    move-object/from16 v19, v7

    move-object/from16 v20, v8

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 342
    move-object/from16 v19, v7

    const/16 v20, 0x0

    check-cast v20, Landroid/graphics/Typeface;

    const/16 v21, 0x1

    invoke-virtual/range {v19 .. v21}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 343
    move-object/from16 v19, v7

    const-string v20, "OFFICIAL OWNERS-\u0e0a\u0e48\u0e2d\u0e07\u0e17\u0e32\u0e07\u0e15\u0e34\u0e14\u0e15\u0e32\u0e21\u0e40\u0e08\u0e49\u0e32\u0e02\u0e2d\u0e07"

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 344
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v9, v19

    .line 345
    new-instance v19, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    const/16 v21, -0x2

    const/16 v22, -0x2

    invoke-direct/range {v20 .. v22}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v10, v19

    .line 346
    move-object/from16 v19, v10

    move-object/from16 v20, v2

    const/16 v21, 0xa

    invoke-direct/range {v20 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v20

    move/from16 v0, v20

    move-object/from16 v1, v19

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 347
    move-object/from16 v19, v9

    move-object/from16 v20, v10

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 348
    move-object/from16 v19, v9

    const-string v20, "Telegram Channel:"

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 349
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v19

    .line 350
    move-object/from16 v19, v11

    new-instance v20, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v20

    move-object/from16 v20, v25

    move-object/from16 v21, v25

    const/16 v22, -0x2

    const/16 v23, -0x2

    invoke-direct/range {v21 .. v23}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 351
    move-object/from16 v19, v11

    move-object/from16 v20, v2

    invoke-direct/range {v20 .. v20}, Lpmm/by/p2077kng/hacker/MainActivity;->TG()Ljava/lang/String;

    move-result-object v20

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 352
    move-object/from16 v19, v11

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setClickable(Z)V

    .line 353
    move-object/from16 v19, v11

    const-string v20, "txtTelegramCanal"

    check-cast v20, Ljava/lang/Object;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setTag(Ljava/lang/Object;)V

    .line 354
    move-object/from16 v19, v11

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLinksClickable(Z)V

    .line 355
    move-object/from16 v19, v11

    move-object/from16 v20, v2

    invoke-direct/range {v20 .. v20}, Lpmm/by/p2077kng/hacker/MainActivity;->colourMain()Ljava/lang/String;

    move-result-object v20

    check-cast v20, Ljava/lang/String;

    invoke-static/range {v20 .. v20}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v20

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setTextColor(I)V

    .line 356
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v12, v19

    .line 357
    new-instance v19, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    const/16 v21, -0x2

    const/16 v22, -0x2

    invoke-direct/range {v20 .. v22}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v20, v2

    const/16 v21, 0xa

    invoke-direct/range {v20 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v20

    move/from16 v0, v20

    move-object/from16 v1, v19

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 358
    move-object/from16 v19, v12

    move-object/from16 v20, v10

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 359
    move-object/from16 v19, v12

    const-string v20, "YouTube Channel:"

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 360
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v13, v19

    .line 361
    move-object/from16 v19, v13

    new-instance v20, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v20

    move-object/from16 v20, v25

    move-object/from16 v21, v25

    const/16 v22, -0x2

    const/16 v23, -0x2

    invoke-direct/range {v21 .. v23}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 362
    move-object/from16 v19, v13

    move-object/from16 v20, v2

    invoke-direct/range {v20 .. v20}, Lpmm/by/p2077kng/hacker/MainActivity;->YT()Ljava/lang/String;

    move-result-object v20

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 363
    move-object/from16 v19, v13

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setClickable(Z)V

    .line 364
    move-object/from16 v19, v13

    const-string v20, "txtDiscordCanal"

    check-cast v20, Ljava/lang/Object;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setTag(Ljava/lang/Object;)V

    .line 365
    move-object/from16 v19, v13

    const/16 v20, 0x1

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLinksClickable(Z)V

    .line 366
    move-object/from16 v19, v13

    move-object/from16 v20, v2

    invoke-direct/range {v20 .. v20}, Lpmm/by/p2077kng/hacker/MainActivity;->colourMain()Ljava/lang/String;

    move-result-object v20

    check-cast v20, Ljava/lang/String;

    invoke-static/range {v20 .. v20}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v20

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setTextColor(I)V

    .line 367
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v14, v19

    .line 368
    new-instance v19, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    const/16 v21, -0x2

    const/16 v22, -0x2

    invoke-direct/range {v20 .. v22}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v15, v19

    .line 369
    move-object/from16 v19, v15

    move-object/from16 v20, v2

    const/16 v21, 0x5

    invoke-direct/range {v20 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v20

    move/from16 v0, v20

    move-object/from16 v1, v19

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 370
    move-object/from16 v19, v14

    move-object/from16 v20, v15

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 371
    move-object/from16 v19, v14

    move-object/from16 v20, v2

    invoke-direct/range {v20 .. v20}, Lpmm/by/p2077kng/hacker/MainActivity;->TitleP()Ljava/lang/String;

    move-result-object v20

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 372
    new-instance v19, Landroid/widget/TextView;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    check-cast v21, Landroid/content/Context;

    invoke-direct/range {v20 .. v21}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v16, v19

    .line 373
    new-instance v19, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    const/16 v21, -0x2

    const/16 v22, -0x2

    invoke-direct/range {v20 .. v22}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v17, v19

    .line 374
    move-object/from16 v19, v17

    move-object/from16 v20, v2

    const/16 v21, 0xa

    invoke-direct/range {v20 .. v21}, Lpmm/by/p2077kng/hacker/MainActivity;->dp(I)I

    move-result v20

    move/from16 v0, v20

    move-object/from16 v1, v19

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 375
    move-object/from16 v19, v16

    move-object/from16 v20, v17

    check-cast v20, Landroid/view/ViewGroup$LayoutParams;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 376
    move-object/from16 v19, v16

    move-object/from16 v20, v2

    invoke-direct/range {v20 .. v20}, Lpmm/by/p2077kng/hacker/MainActivity;->TitleR()Ljava/lang/String;

    move-result-object v20

    check-cast v20, Ljava/lang/CharSequence;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 377
    move-object/from16 v19, v5

    move-object/from16 v20, v7

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 378
    move-object/from16 v19, v5

    move-object/from16 v20, v14

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 379
    move-object/from16 v19, v5

    move-object/from16 v20, v9

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 380
    move-object/from16 v19, v5

    move-object/from16 v20, v11

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 381
    move-object/from16 v19, v5

    move-object/from16 v20, v12

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 382
    move-object/from16 v19, v5

    move-object/from16 v20, v13

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 383
    move-object/from16 v19, v5

    move-object/from16 v20, v16

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 384
    move-object/from16 v19, v4

    move-object/from16 v20, v5

    check-cast v20, Landroid/view/View;

    invoke-virtual/range {v19 .. v20}, Landroid/widget/ScrollView;->addView(Landroid/view/View;)V

    .line 385
    move-object/from16 v19, v4

    move-object/from16 v2, v19

    return-object v2
.end method


# virtual methods
.method public Init()V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 150
    move-object v0, p0

    new-instance v4, Landroid/app/AlertDialog$Builder;

    move-object v9, v4

    move-object v4, v9

    move-object v5, v9

    move-object v6, v0

    invoke-direct {v5, v6}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    move-object v2, v4

    .line 151
    move-object v4, v2

    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Landroid/app/AlertDialog$Builder;->setCancelable(Z)Landroid/app/AlertDialog$Builder;

    move-result-object v4

    .line 152
    move-object v4, v2

    move-object v5, v0

    invoke-direct {v5}, Lpmm/by/p2077kng/hacker/MainActivity;->revendedores()Landroid/view/View;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/app/AlertDialog$Builder;->setView(Landroid/view/View;)Landroid/app/AlertDialog$Builder;

    move-result-object v4

    .line 153
    move-object v4, v2

    const-string v5, "Warning! Don\'t Fall for Scams!!!"

    invoke-virtual {v4, v5}, Landroid/app/AlertDialog$Builder;->setTitle(Ljava/lang/CharSequence;)Landroid/app/AlertDialog$Builder;

    move-result-object v4

    .line 154
    move-object v4, v2

    const-string v5, "OK"

    new-instance v6, Lpmm/by/p2077kng/hacker/MainActivity$100000001;

    move-object v9, v6

    move-object v6, v9

    move-object v7, v9

    move-object v8, v0

    invoke-direct {v7, v8}, Lpmm/by/p2077kng/hacker/MainActivity$100000001;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;)V

    invoke-virtual {v4, v5, v6}, Landroid/app/AlertDialog$Builder;->setPositiveButton(Ljava/lang/CharSequence;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v4

    .line 160
    move-object v4, v2

    invoke-virtual {v4}, Landroid/app/AlertDialog$Builder;->create()Landroid/app/AlertDialog;

    move-result-object v4

    invoke-virtual {v4}, Landroid/app/AlertDialog;->show()V

    return-void
.end method

.method public Login()V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 169
    move-object v0, p0

    new-instance v4, Landroid/app/AlertDialog$Builder;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v6, v0

    invoke-direct {v5, v6}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    move-object v2, v4

    .line 170
    move-object v4, v2

    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Landroid/app/AlertDialog$Builder;->setCancelable(Z)Landroid/app/AlertDialog$Builder;

    move-result-object v4

    .line 172
    move-object v4, v2

    move-object v5, v0

    invoke-direct {v5}, Lpmm/by/p2077kng/hacker/MainActivity;->dialog_login_nexus()Landroid/view/View;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/app/AlertDialog$Builder;->setView(Landroid/view/View;)Landroid/app/AlertDialog$Builder;

    move-result-object v4

    .line 173
    move-object v4, v2

    invoke-virtual {v4}, Landroid/app/AlertDialog$Builder;->create()Landroid/app/AlertDialog;

    move-result-object v4

    invoke-virtual {v4}, Landroid/app/AlertDialog;->show()V

    return-void
.end method

.method public clickvendedores(Ljava/lang/String;)Landroid/view/View$OnClickListener;
    .locals 11

    .prologue
    .line 390
    move-object v0, p0

    move-object v1, p1

    move-object v6, v1

    move-object v3, v6

    .line 392
    new-instance v6, Lpmm/by/p2077kng/hacker/MainActivity$100000005;

    move-object v10, v6

    move-object v6, v10

    move-object v7, v10

    move-object v8, v0

    move-object v9, v3

    invoke-direct {v7, v8, v9}, Lpmm/by/p2077kng/hacker/MainActivity$100000005;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;Ljava/lang/String;)V

    move-object v4, v6

    .line 400
    move-object v6, v4

    move-object v0, v6

    return-object v0
.end method

.method protected onCreate(Landroid/os/Bundle;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/os/Bundle;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    move-object v0, p0

    move-object v1, p1

    move-object v6, v0

    const-string v7, "com.aide.ui.crustacean"

    invoke-static {v6, v7}, Ladrt/ADRTLogCatReader;->onContext(Landroid/content/Context;Ljava/lang/String;)V

    .line 78
    move-object v6, v0

    move-object v7, v1

    invoke-super {v6, v7}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 79
    new-instance v6, Landroid/widget/FrameLayout;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    move-object v8, v0

    invoke-direct {v7, v8}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    move-object v3, v6

    .line 80
    new-instance v6, Lpmm/by/p2077kng/hacker/FullScreenVideoView;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    move-object v8, v0

    invoke-direct {v7, v8}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;-><init>(Landroid/content/Context;)V

    move-object v4, v6

    .line 81
    move-object v6, v3

    move-object v7, v4

    invoke-virtual {v6, v7}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 82
    move-object v6, v0

    move-object v7, v3

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/MainActivity;->setContentView(Landroid/view/View;)V

    .line 83
    move-object v6, v0

    new-instance v7, Ljava/io/File;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v0

    invoke-virtual {v9}, Lpmm/by/p2077kng/hacker/MainActivity;->getCacheDir()Ljava/io/File;

    move-result-object v9

    const-string v10, "libgame.so"

    invoke-direct {v8, v9, v10}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    .line 84
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    invoke-virtual {v6}, Ljava/io/File;->exists()Z

    move-result v6

    if-nez v6, :cond_0

    .line 85
    move-object v6, v0

    const-string v7, "libgame.so"

    move-object v8, v0

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    invoke-direct {v6, v7, v8}, Lpmm/by/p2077kng/hacker/MainActivity;->copyAssetToFile(Ljava/lang/String;Ljava/io/File;)V

    .line 87
    :cond_0
    move-object v6, v4

    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    invoke-static {v7}, Landroid/net/Uri;->fromFile(Ljava/io/File;)Landroid/net/Uri;

    move-result-object v7

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->setVideoURI(Landroid/net/Uri;)V

    .line 88
    move-object v6, v4

    new-instance v7, Lpmm/by/p2077kng/hacker/MainActivity$100000000;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v0

    move-object v10, v4

    invoke-direct {v8, v9, v10}, Lpmm/by/p2077kng/hacker/MainActivity$100000000;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;Lpmm/by/p2077kng/hacker/FullScreenVideoView;)V

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->setOnPreparedListener(Landroid/media/MediaPlayer$OnPreparedListener;)V

    .line 96
    move-object v6, v4

    invoke-virtual {v6}, Lpmm/by/p2077kng/hacker/FullScreenVideoView;->start()V

    .line 97
    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/LoaderBad;->Start(Landroid/content/Context;)V

    .line 98
    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/FFMainActivity;->FFMainActivity(Landroid/content/Context;)V

    .line 99
    move-object v6, v0

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/MainActivity;->checkVPN(Landroid/content/Context;)Z

    move-result v6

    if-nez v6, :cond_1

    .line 100
    move-object v6, v0

    invoke-virtual {v6}, Lpmm/by/p2077kng/hacker/MainActivity;->Init()V

    .line 102
    :goto_0
    return-void

    :cond_1
    move-object v6, v0

    const-string v7, "no vpn"

    const/4 v8, 0x1

    invoke-static {v6, v7, v8}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v6

    invoke-virtual {v6}, Landroid/widget/Toast;->show()V

    goto :goto_0
.end method

.method protected onDestroy()V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 143
    move-object v0, p0

    move-object v2, v0

    invoke-super {v2}, Landroid/app/Activity;->onDestroy()V

    .line 144
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    if-eqz v2, :cond_0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    invoke-virtual {v2}, Ljava/io/File;->exists()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 145
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->tempVideoFile:Ljava/io/File;

    invoke-virtual {v2}, Ljava/io/File;->delete()Z

    move-result v2

    :cond_0
    return-void
.end method

.method protected onPause()V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 137
    move-object v0, p0

    move-object v2, v0

    invoke-super {v2}, Landroid/app/Activity;->onPause()V

    .line 138
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->videoView:Landroid/widget/VideoView;

    if-eqz v2, :cond_0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->videoView:Landroid/widget/VideoView;

    invoke-virtual {v2}, Landroid/widget/VideoView;->pause()V

    :cond_0
    return-void
.end method

.method protected onResume()V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 131
    move-object v0, p0

    move-object v2, v0

    invoke-super {v2}, Landroid/app/Activity;->onResume()V

    .line 132
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->videoView:Landroid/widget/VideoView;

    if-eqz v2, :cond_0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/MainActivity;->videoView:Landroid/widget/VideoView;

    invoke-virtual {v2}, Landroid/widget/VideoView;->start()V

    :cond_0
    return-void
.end method
