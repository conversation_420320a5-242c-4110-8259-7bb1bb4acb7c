.class Lpmm/by/p2077kng/hacker/Loader$100000004;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000004"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000004;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000004;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000004;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 372
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000004;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-virtual {v3}, Lpmm/by/p2077kng/hacker/Loader;->Inject()V

    .line 373
    invoke-static {}, Lpmm/by/p2077kng/hacker/Loader;->access$L1000019()Landroid/widget/Button;

    move-result-object v3

    invoke-virtual {v3}, Landroid/widget/Button;->getContext()Landroid/content/Context;

    move-result-object v3

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000004;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/Loader;->access$1000009(Lpmm/by/p2077kng/hacker/Loader;)Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    invoke-static {v3, v4, v5}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v3

    invoke-virtual {v3}, Landroid/widget/Toast;->show()V

    return-void
.end method
