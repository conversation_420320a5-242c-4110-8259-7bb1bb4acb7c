.class Lpmm/by/p2077kng/hacker/Utils;
.super Ljava/lang/Object;
.source "Utils.java"


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 131
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static SHA256(Ljava/lang/String;)Ljava/lang/String;
    .locals 7

    .prologue
    .line 63
    move-object v0, p0

    :try_start_0
    const-string v4, "SHA-256"

    invoke-static {v4}, Ljava/security/MessageDigest;->getInstance(Ljava/lang/String;)Ljava/security/MessageDigest;

    move-result-object v4

    move-object v2, v4

    .line 64
    move-object v4, v2

    invoke-virtual {v4}, Ljava/security/MessageDigest;->reset()V

    .line 65
    move-object v4, v2

    move-object v5, v0

    sget-object v6, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-virtual {v5, v6}, Ljava/lang/String;->getBytes(Ljava/nio/charset/Charset;)[B

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/security/MessageDigest;->update([B)V

    .line 66
    move-object v4, v2

    invoke-virtual {v4}, Ljava/security/MessageDigest;->digest()[B

    move-result-object v4

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/Utils;->bytesToHex([B)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;
    :try_end_0
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v4

    move-object v0, v4

    .line 68
    :goto_0
    return-object v0

    .line 66
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 68
    const/4 v4, 0x0

    check-cast v4, Ljava/lang/String;

    move-object v0, v4

    goto :goto_0
.end method

.method private static bytesToHex([B)Ljava/lang/String;
    .locals 13

    .prologue
    .line 28
    move-object v0, p0

    const-string v7, "0123456789abcdef"

    invoke-virtual {v7}, Ljava/lang/String;->toCharArray()[C

    move-result-object v7

    move-object v2, v7

    .line 29
    move-object v7, v0

    array-length v7, v7

    const/4 v8, 0x2

    mul-int/lit8 v7, v7, 0x2

    new-array v7, v7, [C

    move-object v3, v7

    .line 30
    const/4 v7, 0x0

    move v4, v7

    :goto_0
    move v7, v4

    move-object v8, v0

    array-length v8, v8

    if-lt v7, v8, :cond_0

    .line 35
    new-instance v7, Ljava/lang/String;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v3

    invoke-direct {v8, v9}, Ljava/lang/String;-><init>([C)V

    move-object v0, v7

    return-object v0

    .line 31
    :cond_0
    move-object v7, v0

    move v8, v4

    aget-byte v7, v7, v8

    const/16 v8, 0xff

    and-int/lit16 v7, v7, 0xff

    move v5, v7

    .line 32
    move-object v7, v3

    move v8, v4

    const/4 v9, 0x2

    mul-int/lit8 v8, v8, 0x2

    move-object v9, v2

    move v10, v5

    const/4 v11, 0x4

    ushr-int/lit8 v10, v10, 0x4

    aget-char v9, v9, v10

    aput-char v9, v7, v8

    .line 33
    move-object v7, v3

    move v8, v4

    const/4 v9, 0x2

    mul-int/lit8 v8, v8, 0x2

    const/4 v9, 0x1

    add-int/lit8 v8, v8, 0x1

    move-object v9, v2

    move v10, v5

    const/16 v11, 0xf

    and-int/lit8 v10, v10, 0xf

    aget-char v9, v9, v10

    aput-char v9, v7, v8

    .line 30
    add-int/lit8 v4, v4, 0x1

    goto :goto_0
.end method

.method static clearCache(Landroid/content/Context;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 74
    move-object v0, p0

    move-object v4, v0

    :try_start_0
    invoke-virtual {v4}, Landroid/content/Context;->getCacheDir()Ljava/io/File;

    move-result-object v4

    move-object v2, v4

    .line 75
    move-object v4, v2

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/Utils;->deleteFilesInDir(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :goto_0
    return-void

    :catch_0
    move-exception v4

    move-object v2, v4

    goto :goto_0
.end method

.method private static deleteFilesInDir(Ljava/io/File;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/io/File;",
            ")V"
        }
    .end annotation

    .prologue
    .line 80
    move-object v0, p0

    move-object v6, v0

    invoke-virtual {v6}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object v6

    move-object v2, v6

    const/4 v6, 0x0

    move v3, v6

    .line 84
    :goto_0
    move v6, v3

    move-object v7, v2

    array-length v7, v7

    if-lt v6, v7, :cond_0

    return-void

    .line 80
    :cond_0
    move-object v6, v2

    move v7, v3

    aget-object v6, v6, v7

    move-object v4, v6

    .line 81
    move-object v6, v4

    invoke-virtual {v6}, Ljava/io/File;->isDirectory()Z

    move-result v6

    if-eqz v6, :cond_1

    .line 82
    move-object v6, v4

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/Utils;->deleteFilesInDir(Ljava/io/File;)V

    .line 84
    :cond_1
    move-object v6, v4

    invoke-virtual {v6}, Ljava/io/File;->delete()Z

    move-result v6

    add-int/lit8 v3, v3, 0x1

    goto :goto_0
.end method

.method static fromBase64(Ljava/lang/String;)[B
    .locals 5

    .prologue
    .line 126
    move-object v0, p0

    move-object v3, v0

    const/4 v4, 0x2

    invoke-static {v3, v4}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method static fromBase64String(Ljava/lang/String;)Ljava/lang/String;
    .locals 8

    .prologue
    .line 130
    move-object v0, p0

    new-instance v3, Ljava/lang/String;

    move-object v7, v3

    move-object v3, v7

    move-object v4, v7

    move-object v5, v0

    const/4 v6, 0x2

    invoke-static {v5, v6}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v5

    sget-object v6, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-direct {v4, v5, v6}, Ljava/lang/String;-><init>([BLjava/nio/charset/Charset;)V

    move-object v0, v3

    return-object v0
.end method

.method static loaderDecrypt([B)[B
    .locals 10

    .prologue
    .line 90
    move-object v0, p0

    :try_start_0
    new-instance v5, Ljavax/crypto/spec/SecretKeySpec;

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    const-string v7, "22P9ULFDKPJ70G46"

    invoke-virtual {v7}, Ljava/lang/String;->getBytes()[B

    move-result-object v7

    const-string v8, "AES"

    invoke-direct {v6, v7, v8}, Ljavax/crypto/spec/SecretKeySpec;-><init>([BLjava/lang/String;)V

    move-object v2, v5

    .line 91
    const-string v5, "AES/ECB/PKCS5Padding"

    invoke-static {v5}, Ljavax/crypto/Cipher;->getInstance(Ljava/lang/String;)Ljavax/crypto/Cipher;

    move-result-object v5

    move-object v3, v5

    .line 92
    move-object v5, v3

    const/4 v6, 0x2

    move-object v7, v2

    invoke-virtual {v5, v6, v7}, Ljavax/crypto/Cipher;->init(ILjava/security/Key;)V

    .line 93
    move-object v5, v3

    move-object v6, v0

    invoke-virtual {v5, v6}, Ljavax/crypto/Cipher;->doFinal([B)[B
    :try_end_0
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/security/InvalidKeyException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljavax/crypto/NoSuchPaddingException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljavax/crypto/BadPaddingException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljavax/crypto/IllegalBlockSizeException; {:try_start_0 .. :try_end_0} :catch_4

    move-result-object v5

    move-object v0, v5

    .line 105
    :goto_0
    return-object v0

    .line 93
    :catch_0
    move-exception v5

    move-object v2, v5

    .line 95
    move-object v5, v2

    invoke-virtual {v5}, Ljava/security/NoSuchAlgorithmException;->printStackTrace()V

    .line 105
    :goto_1
    const/4 v5, 0x0

    check-cast v5, [B

    move-object v0, v5

    goto :goto_0

    .line 95
    :catch_1
    move-exception v5

    move-object v2, v5

    .line 97
    move-object v5, v2

    invoke-virtual {v5}, Ljava/security/InvalidKeyException;->printStackTrace()V

    goto :goto_1

    :catch_2
    move-exception v5

    move-object v2, v5

    .line 99
    move-object v5, v2

    invoke-virtual {v5}, Ljavax/crypto/NoSuchPaddingException;->printStackTrace()V

    goto :goto_1

    :catch_3
    move-exception v5

    move-object v2, v5

    .line 101
    move-object v5, v2

    invoke-virtual {v5}, Ljavax/crypto/BadPaddingException;->printStackTrace()V

    goto :goto_1

    :catch_4
    move-exception v5

    move-object v2, v5

    .line 103
    move-object v5, v2

    invoke-virtual {v5}, Ljavax/crypto/IllegalBlockSizeException;->printStackTrace()V

    goto :goto_1
.end method

.method static profileDecrypt(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 13

    .prologue
    .line 109
    move-object v0, p0

    move-object v1, p1

    move-object v7, v1

    invoke-virtual {v7}, Ljava/lang/String;->toCharArray()[C

    move-result-object v7

    move-object v3, v7

    .line 110
    move-object v7, v0

    invoke-static {v7}, Lpmm/by/p2077kng/hacker/Utils;->fromBase64String(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/String;->toCharArray()[C

    move-result-object v7

    move-object v4, v7

    .line 111
    const/4 v7, 0x0

    move v5, v7

    :goto_0
    move v7, v5

    move-object v8, v4

    array-length v8, v8

    if-lt v7, v8, :cond_0

    .line 114
    new-instance v7, Ljava/lang/String;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v4

    invoke-direct {v8, v9}, Ljava/lang/String;-><init>([C)V

    move-object v0, v7

    return-object v0

    .line 112
    :cond_0
    move-object v7, v4

    move v8, v5

    move-object v9, v3

    move v10, v5

    move-object v11, v3

    array-length v11, v11

    rem-int/2addr v10, v11

    aget-char v9, v9, v10

    move-object v10, v4

    move v11, v5

    aget-char v10, v10, v11

    xor-int/2addr v9, v10

    int-to-char v9, v9

    aput-char v9, v7, v8

    .line 111
    add-int/lit8 v5, v5, 0x1

    goto :goto_0
.end method

.method static readStream(Ljava/io/InputStream;)Ljava/lang/String;
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .prologue
    .line 43
    move-object v0, p0

    new-instance v8, Ljava/lang/StringBuilder;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    move-object v2, v8

    const/4 v8, 0x0

    move-object v3, v8

    const/4 v8, 0x0

    move-object v5, v8

    .line 44
    :try_start_0
    new-instance v8, Ljava/io/BufferedReader;

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    new-instance v10, Ljava/io/InputStreamReader;

    move-object v13, v10

    move-object v10, v13

    move-object v11, v13

    move-object v12, v0

    invoke-direct {v11, v12}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v9, v10}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    move-object v5, v8

    .line 45
    :try_start_1
    const-string v8, ""

    move-object v6, v8

    .line 48
    :goto_0
    move-object v8, v5

    invoke-virtual {v8}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v8

    move-object v13, v8

    move-object v8, v13

    move-object v9, v13

    move-object v6, v9

    if-nez v8, :cond_1

    move-object v8, v5

    if-eqz v8, :cond_0

    move-object v8, v5

    invoke-interface {v8}, Ljava/lang/AutoCloseable;->close()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 55
    :cond_0
    move-object v8, v2

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    move-object v0, v8

    return-object v0

    .line 49
    :cond_1
    move-object v8, v2

    move-object v9, v6

    :try_start_2
    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    move-result-object v8

    goto :goto_0

    .line 48
    :catchall_0
    move-exception v8

    move-object v3, v8

    move-object v8, v5

    if-eqz v8, :cond_2

    move-object v8, v5

    :try_start_3
    invoke-interface {v8}, Ljava/lang/AutoCloseable;->close()V

    :cond_2
    move-object v8, v3

    throw v8
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :catchall_1
    move-exception v8

    move-object v4, v8

    move-object v8, v3

    if-nez v8, :cond_4

    move-object v8, v4

    move-object v3, v8

    :cond_3
    :goto_1
    move-object v8, v3

    throw v8

    :cond_4
    move-object v8, v3

    move-object v9, v4

    if-eq v8, v9, :cond_3

    move-object v8, v3

    move-object v9, v4

    invoke-virtual {v8, v9}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    goto :goto_1
.end method

.method static toBase64(Ljava/lang/String;)Ljava/lang/String;
    .locals 5

    .prologue
    .line 118
    move-object v0, p0

    move-object v3, v0

    sget-object v4, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-virtual {v3, v4}, Ljava/lang/String;->getBytes(Ljava/nio/charset/Charset;)[B

    move-result-object v3

    const/4 v4, 0x2

    invoke-static {v3, v4}, Landroid/util/Base64;->encodeToString([BI)Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method static toBase64([B)Ljava/lang/String;
    .locals 5

    .prologue
    .line 122
    move-object v0, p0

    move-object v3, v0

    const/4 v4, 0x2

    invoke-static {v3, v4}, Landroid/util/Base64;->encodeToString([BI)Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method
