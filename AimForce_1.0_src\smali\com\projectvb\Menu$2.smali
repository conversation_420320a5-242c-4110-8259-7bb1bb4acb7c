.class Lcom/projectvb/Menu$2;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Menu;->onCreateTemplate()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;

.field final synthetic val$container_menu:Landroid/widget/LinearLayout;

.field final synthetic val$icon_cheat:Lcom/projectvb/ImageBase64;

.field final synthetic val$inject_close:Landroid/widget/Button;


# direct methods
.method constructor <init>(Lcom/projectvb/Menu;Landroid/widget/Button;Lcom/projectvb/ImageBase64;Landroid/widget/LinearLayout;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Menu;

    .line 181
    iput-object p1, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    iput-object p2, p0, Lcom/projectvb/Menu$2;->val$inject_close:Landroid/widget/Button;

    iput-object p3, p0, Lcom/projectvb/Menu$2;->val$icon_cheat:Lcom/projectvb/ImageBase64;

    iput-object p4, p0, Lcom/projectvb/Menu$2;->val$container_menu:Landroid/widget/LinearLayout;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2
    .param p1, "view"    # Landroid/view/View;

    .line 184
    iget-object v0, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$000(Lcom/projectvb/Menu;)I

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_1

    .line 185
    iget-object v0, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$100(Lcom/projectvb/Menu;)I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 187
    :cond_0
    iget-object v0, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$100(Lcom/projectvb/Menu;)I

    move-result v0

    if-ne v0, v1, :cond_2

    .line 188
    iget-object v0, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    const-string v1, "libqqqqaaqwdqwd.so"

    invoke-static {v0, v1}, Lcom/projectvb/Menu;->access$200(Lcom/projectvb/Menu;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 189
    invoke-static {}, Lcom/projectvb/Menu;->Init()V

    .line 191
    iget-object v0, p0, Lcom/projectvb/Menu$2;->val$inject_close:Landroid/widget/Button;

    const-string v1, "CLOSE"

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 193
    iget-object v0, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$008(Lcom/projectvb/Menu;)I

    goto :goto_0

    .line 196
    :cond_1
    iget-object v0, p0, Lcom/projectvb/Menu$2;->this$0:Lcom/projectvb/Menu;

    invoke-static {v0}, Lcom/projectvb/Menu;->access$000(Lcom/projectvb/Menu;)I

    move-result v0

    if-ne v0, v1, :cond_2

    .line 197
    iget-object v0, p0, Lcom/projectvb/Menu$2;->val$icon_cheat:Lcom/projectvb/ImageBase64;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/projectvb/ImageBase64;->setVisibility(I)V

    .line 198
    iget-object v0, p0, Lcom/projectvb/Menu$2;->val$container_menu:Landroid/widget/LinearLayout;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 200
    :cond_2
    :goto_0
    return-void
.end method
