.class Lcom/projectvb/Menu$CloseClickListener;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnClickListener;

# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;

# direct methods
.method constructor <init>(Lcom/projectvb/Menu;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Menu;

    .prologue
    .line 700
    iput-object p1, p0, Lcom/projectvb/Menu$CloseClickListener;->this$0:Lcom/projectvb/Menu;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2
    .param p1, "v"    # Landroid/view/View;

    .prologue
    .line 703
    :try_start_0
    # Hide the floating interface
    iget-object v0, p0, Lcom/projectvb/Menu$CloseClickListener;->this$0:Lcom/projectvb/Menu;
    iget-object v0, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;
    if-eqz v0, :cond_end

    iget-object v0, p0, Lcom/projectvb/Menu$CloseClickListener;->this$0:Lcom/projectvb/Menu;
    iget-object v0, v0, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    if-eqz v0, :cond_end

    iget-object v0, p0, Lcom/projectvb/Menu$CloseClickListener;->this$0:Lcom/projectvb/Menu;
    iget-object v0, v0, Lcom/projectvb/Menu;->windowManager:Landroid/view/WindowManager;
    iget-object v1, p0, Lcom/projectvb/Menu$CloseClickListener;->this$0:Lcom/projectvb/Menu;
    iget-object v1, v1, Lcom/projectvb/Menu;->frameLayout:Landroid/widget/FrameLayout;
    invoke-interface {v0, v1}, Landroid/view/WindowManager;->removeView(Landroid/view/View;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :cond_end

    :catch_0
    move-exception v0

    :cond_end
    return-void
.end method
