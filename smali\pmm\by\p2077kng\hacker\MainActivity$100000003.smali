.class Lpmm/by/p2077kng/hacker/MainActivity$100000003;
.super Ljava/lang/Object;
.source "MainActivity.java"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/MainActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000003"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/MainActivity;

.field private final val$editText:Landroid/widget/EditText;

.field private final val$editText2:Landroid/widget/EditText;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/MainActivity;Landroid/widget/EditText;Landroid/widget/EditText;)V
    .locals 7

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, v0

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    move-object v5, v0

    move-object v6, v1

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    move-object v5, v0

    move-object v6, v2

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->val$editText:Landroid/widget/EditText;

    move-object v5, v0

    move-object v6, v3

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->val$editText2:Landroid/widget/EditText;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/MainActivity$100000003;)Lpmm/by/p2077kng/hacker/MainActivity;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/CompoundButton;",
            "Z)V"
        }
    .end annotation

    .prologue
    .line 259
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move v4, v2

    if-eqz v4, :cond_0

    .line 260
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->val$editText:Landroid/widget/EditText;

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-static {v5}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v5

    const-string v6, "USER_Sv"

    invoke-virtual {v5, v6}, Lpmm/by/p2077kng/hacker/Prefs;->read(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 261
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->val$editText2:Landroid/widget/EditText;

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-static {v5}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v5

    const-string v6, "PASS_Sv"

    invoke-virtual {v5, v6}, Lpmm/by/p2077kng/hacker/Prefs;->read(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 262
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v4

    const-string v5, "save"

    const-string v6, "1"

    invoke-virtual {v4, v5, v6}, Lpmm/by/p2077kng/hacker/Prefs;->write(Ljava/lang/String;Ljava/lang/String;)V

    .line 267
    :goto_0
    return-void

    .line 265
    :cond_0
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->val$editText:Landroid/widget/EditText;

    const-string v5, ""

    invoke-virtual {v4, v5}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 266
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->val$editText2:Landroid/widget/EditText;

    const-string v5, ""

    invoke-virtual {v4, v5}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 267
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/MainActivity$100000003;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v4

    const-string v5, "save"

    const-string v6, "0"

    invoke-virtual {v4, v5, v6}, Lpmm/by/p2077kng/hacker/Prefs;->write(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0
.end method
