.class public Lcom/projectvb/NativeLib;
.super Ljava/lang/Object;
.source "NativeLib.java"

# direct methods
.method public constructor <init>()V
    .locals 0

    .prologue
    .line 10
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# Load the real PMM library
.method static constructor <clinit>()V
    .locals 1

    .prologue
    :try_start_0
    const-string v0, "P2077KNG"
    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/UnsatisfiedLinkError; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

# Real PMM native methods (using actual function names from P2077KNG.smali)
.method public static setESP(I)V
    .locals 1
    .param p0, "enabled"    # I

    .prologue
    :try_start_0
    if-eqz p0, :cond_disable

    # Enable ESP - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->AAAAAAAAA()V

    goto :goto_end

    :cond_disable
    # Disable ESP - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->BBBBBBBB()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public static setAimbot(I)V
    .locals 1
    .param p0, "enabled"    # I

    .prologue
    :try_start_0
    if-eqz p0, :cond_disable

    # Enable Aimbot - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->CCCCCCCC()V

    goto :goto_end

    :cond_disable
    # Disable Aimbot - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->DDDDDDDD()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public static setSpeed(I)V
    .locals 1
    .param p0, "enabled"    # I

    .prologue
    :try_start_0
    if-eqz p0, :cond_disable

    # Enable Speed - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->EEEEEEEE()V

    goto :goto_end

    :cond_disable
    # Disable Speed - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->FFFFFFFF()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public static setWallhack(I)V
    .locals 1
    .param p0, "enabled"    # I

    .prologue
    :try_start_0
    if-eqz p0, :cond_disable

    # Enable Wallhack - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->GGGGGGGG()V

    goto :goto_end

    :cond_disable
    # Disable Wallhack - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->HHHHHHHH()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method public static setNoRecoil(I)V
    .locals 1
    .param p0, "enabled"    # I

    .prologue
    :try_start_0
    if-eqz p0, :cond_disable

    # Enable No Recoil - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->IIIIIIIIII()V

    goto :goto_end

    :cond_disable
    # Disable No Recoil - call real PMM function
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->JJJJJJJJ()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method
