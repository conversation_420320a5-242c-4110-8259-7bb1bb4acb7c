.class interface Lcom/bad/modder/injector/InjectorService$InterfaceStr;
.super Ljava/lang/Object;
.source "InjectorService.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20a
    name = "InterfaceStr"
.end annotation


# virtual methods
.method public abstract OnWrite(Ljava/lang/String;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation
.end method
