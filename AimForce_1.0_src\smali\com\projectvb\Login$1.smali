.class Lcom/projectvb/Login$1;
.super Ljava/lang/Object;
.source "Login.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Login;->initializeSession()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/Login;


# direct methods
.method constructor <init>(Lcom/projectvb/Login;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Login;

    .line 53
    iput-object p1, p0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    .line 57
    :try_start_0
    const-string v0, "https://keyauth.win/api/1.3/?type=init&name=websit&ownerid=PF7qFni9VY&secret=b684ef652abd78d502b9a4ac1e0d70c4687aeb0adbe79d3f9e1e8e63799b0bdd&ver=1.0"

    .line 63
    .local v0, "requestUrl":Ljava/lang/String;
    iget-object v1, p0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    invoke-static {v1, v0}, Lcom/projectvb/Login;->access$000(Lcom/projectvb/Login;Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v1

    .line 65
    .local v1, "response":Lorg/json/JSONObject;
    const-string v2, "success"

    invoke-virtual {v1, v2}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 66
    iget-object v2, p0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    const-string v3, "sessionid"

    invoke-virtual {v1, v3}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lcom/projectvb/Login;->access$102(Lcom/projectvb/Login;Ljava/lang/String;)Ljava/lang/String;

    .line 67
    iget-object v2, p0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    const/4 v3, 0x1

    invoke-static {v2, v3}, Lcom/projectvb/Login;->access$202(Lcom/projectvb/Login;Z)Z

    .line 70
    new-instance v2, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v3

    invoke-direct {v2, v3}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v3, Lcom/projectvb/Login$1$1;

    invoke-direct {v3, p0}, Lcom/projectvb/Login$1$1;-><init>(Lcom/projectvb/Login$1;)V

    invoke-virtual {v2, v3}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_0

    .line 77
    :cond_0
    iget-object v2, p0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    const-string v3, "Servers On Maintenance!"

    invoke-static {v2, v3}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 82
    .end local v0    # "requestUrl":Ljava/lang/String;
    .end local v1    # "response":Lorg/json/JSONObject;
    :goto_0
    goto :goto_1

    .line 79
    :catch_0
    move-exception v0

    .line 80
    .local v0, "e":Ljava/lang/Exception;
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 81
    iget-object v1, p0, Lcom/projectvb/Login$1;->this$0:Lcom/projectvb/Login;

    const-string v2, "Error initializing session"

    invoke-static {v1, v2}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V

    .line 83
    .end local v0    # "e":Ljava/lang/Exception;
    :goto_1
    return-void
.end method
