.class Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/topjohnwu/superuser/internal/RootServiceServer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "ServiceContainer"
.end annotation


# instance fields
.field binder:Landroid/os/IBinder;

.field intent:Landroid/content/Intent;

.field service:Lcom/topjohnwu/superuser/ipc/RootService;


# direct methods
.method constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
