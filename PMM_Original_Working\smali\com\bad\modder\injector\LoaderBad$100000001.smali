.class Lcom/bad/modder/injector/LoaderBad$100000001;
.super Ljava/lang/Object;
.source "LoaderBad.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/LoaderBad;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000001"
.end annotation


# instance fields
.field private final val$ctx:Landroid/content/Context;


# direct methods
.method constructor <init>(Landroid/content/Context;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/bad/modder/injector/LoaderBad$100000001;->val$ctx:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public onOpenResult(ZI)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZI)V"
        }
    .end annotation

    .prologue
    .line 63
    move-object v0, p0

    move v1, p1

    move v2, p2

    move v4, v1

    if-nez v4, :cond_0

    .line 64
    move-object v4, v0

    iget-object v4, v4, Lcom/bad/modder/injector/LoaderBad$100000001;->val$ctx:Landroid/content/Context;

    invoke-static {v4}, Lcom/bad/modder/injector/LoaderBad;->showNoRootMessage(Landroid/content/Context;)V

    .line 65
    const/4 v4, 0x0

    sput-boolean v4, Lcom/bad/modder/injector/LoaderBad;->Very:Z

    :cond_0
    return-void
.end method
