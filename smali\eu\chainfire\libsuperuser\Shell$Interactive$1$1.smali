.class Leu/chainfire/libsuperuser/Shell$Interactive$1$1;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive$1;->onCommandResult(IILjava/util/List;Ljava/util/List;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Leu/chainfire/libsuperuser/Shell$Interactive$1;

.field final synthetic val$fExitCode:I


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive$1;I)V
    .locals 5

    .prologue
    .line 1647
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->this$1:Leu/chainfire/libsuperuser/Shell$Interactive$1;

    move-object v3, v0

    move v4, v2

    iput v4, v3, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->val$fExitCode:I

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    .prologue
    .line 1651
    move-object v0, p0

    move-object v2, v0

    :try_start_0
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->this$1:Leu/chainfire/libsuperuser/Shell$Interactive$1;

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$1;->val$onShellOpenResultListener:Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;

    move-object v3, v0

    iget v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->val$fExitCode:I

    if-nez v3, :cond_0

    const/4 v3, 0x1

    :goto_0
    move-object v4, v0

    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->val$fExitCode:I

    invoke-interface {v2, v3, v4}, Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;->onOpenResult(ZI)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1653
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->this$1:Leu/chainfire/libsuperuser/Shell$Interactive$1;

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->endCallback()V

    .line 1655
    return-void

    .line 1651
    :cond_0
    const/4 v3, 0x0

    goto :goto_0

    .line 1653
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$1$1;->this$1:Leu/chainfire/libsuperuser/Shell$Interactive$1;

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$1;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->endCallback()V

    .line 1654
    move-object v2, v1

    throw v2
.end method
