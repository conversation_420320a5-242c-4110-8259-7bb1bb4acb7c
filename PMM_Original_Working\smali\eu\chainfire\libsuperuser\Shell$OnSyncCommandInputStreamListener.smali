.class public interface abstract Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnCommandInputStream;
.implements Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnSyncCommandInputStreamListener"
.end annotation
