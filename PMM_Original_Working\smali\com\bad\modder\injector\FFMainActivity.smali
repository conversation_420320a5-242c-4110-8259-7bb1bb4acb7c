.class public Lcom/bad/modder/injector/FFMainActivity;
.super Ljava/lang/Object;
.source "FFMainActivity.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bad/modder/injector/FFMainActivity$100000000;,
        Lcom/bad/modder/injector/FFMainActivity$100000001;
    }
.end annotation


# static fields
.field public static FFMainActivity:Ljava/lang/String;

.field private static final TAG:Ljava/lang/String;

.field public static cacheDir:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95U3oYdEloEi0lQ285JxFlUGACFW9fAhUEVUoRBi17Xg0/PmhRBDInen0zFRFOaBEWBFRcAzsva2oTOxNsaRodKBReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9dExAxeVkdFRJrag9tJWwBMhx2ew0XLBNfbwI/dWhPGx0kQGI+FSt4dBEtH0FeZmQFZVBgYx5vajgWdRhRDwYTfmE7OyRnaxt7H3xhBxIOFGIQLTFLbDsVcnR5MTMXb0whHhFaehoGA0h+DRETYXAPIid8eWIcK397ECx2Rm5kGXFveyFgIm9bJjcrGF0cPgtEYR0nf3RAYCInfnksMHVZXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4TIxdheTECHnx1eh0tFA4YBRdHbgQVfmhgAzAVfG0mFXZFExEGA3lhDRUgSEExHwVqcTYfdRQPEBMfaWkDPwJqa2w4BVpcMh0rFAEfPCIfYWQ/FmJqMSYRemomHC1FeQwcNXxvO2QFYHkbJhN/eQ0ZHhxPEDwAVWE+EQ5iUTECHmpxBhEAHXISFzFLYTtgEmRAMTwjeX0WHChVCTA8D0heImQfbHAPZRFAbTIZK3tfFBUDRF0NODJrYA8SHlVpBBd2RVARADVGYRMRDmNrA2UQVHEHEXVZYBAQdgZwZztyfUADOCQfaTged38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9/XTg/JGB5Ez4jfE8NEHZjXRoufx9ZEj8ea1AbeyB+fWYYKntcEjwcVG8DOChnT2BtF29MYhErXUAXPANscB0/Imh8E20feUwyEhF7SRgAMUZaZgELZV8TFiJvaSAeE2ddHDkXS1pnOz5lewcfJ3x1LB92e3EQPTFBamckK2J8HyYfbAAGFXd7ARA8EBxpAxUUb1AbYh9FdQcELRQPEmYPQV0CETxgegdsJ295Ah4Xd08PLjJUXBIgLWdqbCIieU8xMHUVbBIQPh9gZmASYkEXbRN8dSwdAHsKBTxySHATYCVvXxsYHkRxLzcRb28UZzVHXhBsCGhwDz4ef1cgMi5VUB0GE25ZAxYhZFEfbCV8CCIwEEFbED4xS1oSYAVlUSU4E391Yx13e3UfPHZCaQIzJH1fMWweelt6EStrSxBmdktdACM/aFATYxV/XxQZE3dZDzMDYWIDbBJrUR8iJ355MBF0SWgQEHJhahIRAmlRE2cnRXUGHnVVCQ88fx9dZWASa08hbRF6Wzs3KEkPHAcPR3BmPxFrYDFjBX9pEhAEFEkULBN4Wh0jAmhAbHsifHE0ERBBchssC31pA2QFaGwtMyNKXCYedBQNFDxydVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsDmB5B2wlVVsdGR5BXxA8E2thEBUkfXsbbCd+cTEVDl5zFwAxfWk7FRN3egM8J39LDB53SXQTLBNfXWRgF2FwHxAeQFt6GCt7aRBmC39eZDchYFFgAh9FVyESd0VZED0XRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/bad/modder/injector/FFMainActivity;->TAG:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .prologue
    .line 78
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Start(Landroid/content/Context;)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 42
    move-object/from16 v0, p0

    new-instance v10, Ljava/text/SimpleDateFormat;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    new-instance v12, Ljava/lang/String;

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    const-string v14, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/L2dPGycnWlsWHh5VSBgWABxrDRkVfXkxEiQfaWEfAWcPDBBydVoiFRFoUAQOIn9fAhUEVUoRBi17Xg0/PmlrBwwTHldmH3ZGaBEWBFRcAzsva2oTOCd6eRYSdBQBExYcHm4AbCJvVg9lEUBbegQrQUoXIzFIbmZkAmULDyYlf30CFnccXBQuNVtZZzsSYkAbJyd/VzEfBH9dExAxeVkSYAdiQAciE3pqPxwtQnEwMxdfagI7Hmh5bBAeeUw+BAFZSRAsNUNaZmQFa18XexFVaSAaEFliDy41fmE+FSRnQTF7EFd9MxYOFFwQLAdVXQM/FXR5MT0XbFs8EXVdSQUsMVlpOxExYEAxMid8eWIcK397ECx2RmkSHW1rayFgIm99IRx2a10PORdrYgMWIXRAYCInR18CMBRFahEjH3leOBFxYkEHOidVTGIcdVVQFywPYn4QFSJvUGwYJGlLAzcoHGgdLn51WhQVcmhrJXsHf1smFXZFExEGA3lhDRUgSEExHwVqcTYfdRQPEBMfaWkDPwJqUSE7I1R+Mh0rFAEcBj4fWWRkFmJqMSYfSnUCHR5/eQwXD0NqZGwFYHoHEidFfT8cdUFdGhYAWVlnJw5iUDFhIEBxBxUDRnoSFzFLYTtgFWVqMTwkb3UMHgFBdBQuJUJwEjMiYVZgZRFFaQM3K0VKHww1ZV5kJytrYA8SA1V9EhV1QVARBhN6YhMRDmVBA2IleWEzMChFChItMVVwEjtyfUADOCQfaTged38TGDx2X34UESVvUmEyEXpqPhIrZ0kYBx9/XTg/JGB5Ez4jfE8NEHZjXRoufx9ZEj8ea1AbeyB+fWYYKntcEjwfbG8DOChnT2A9E2l+PR0Db0AXLBNpajs7FWtCExgfeUwyESt/eRgAMUddDWQRZXs9AgV5eW0wKhROFBB+RGECJDVoezEMJXx1Zh92e3MdIzEcWQJgAmNAAyYfbAAGFQB7DRQ8AB9hZBUUbHsQNx9/dQcQK2NKFGYTfG8yMy9rYAMOH295BDAhd08PLjJUXgAVAmJ7BDIneV8wMHUVbBIQPh9gZmATaGsDIhNAfSwRHl1JBTwPX2JlPx9vQGx7Hnlfeh4Bbw0QLnJHXhBsCGhwDz4ef1cgMj4UYhA+fkhZZBUkSEEXfyB6YQwfKFlbED4xS1oSYAVob2Q7J3p5bBF1QQETPANfYAAVJH1fMWweelt6EStrSxBmdktdACM/aGsheyN/XxQZE0lcEQMXf2INJ39sXwNhI3oIIh8uf3cTFgQGahIRBWd8AzsjV30YHh5VWhEWfxxvADMXawsfYSdAW2IVLRRLHS0PR3BmZB5leyYBJ2xPAhAEFEkULBN4Wh0jAmhAbHsifFsEH3RjcxMTH31pA2AVaGlgOydFaRsVKBQNFDxydVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsMWBwAAIkb3EEBC4UXw85B3lZEicCZFAbFSB+TwccKhxyFwAxfWBnHip9QGwiF2pAYhwoQV4wPHJvWiJkF2FWHxweRXomFwFFTxEuckheZDchYFJhAR9FeX4yLUViDxYTRmsQER5LQRdiH3xPMx8AQVoQEHJVa2Q7FXR5AzgTfm1hEXUUChosdx9dZgELfXwTMh9seQMcKBwNEi0lQVoiPHBlayESB296ODAtWVwUFwsCWRInLkhBG3seVWkPEXZBWxAFC3VeZx0IZwtsJx5adj0ddUJxHDx+WVkSIxdheSE4Jx9pYh0oHHoMEyVDag0BAmVfYBMjf30CMj5VThQQC3VZIicgdGsxFSdHCAwRE0EOHRUlS2tkOyJsaQM7J0B5ZB11FFoUPH5IbgMjFmtQG2URemkDHHRFeTccNUReEycXanoHbCdveWAcdRxIFGYXa1kSIzJgQRsfHm5hMDAEfHYTED5UXAMsKk96DzwTRQAXHC1dDRQsE19iImweaHlsEB58diYQK28NF2YxQW4DJyJqamAWHlV5bRF0WVAQFhNGXg0nJE97MX8leX0DECoYQhMjB1VeAj9xa2obZyNKdW0edRR0ERZzHmsNBRJhehsyHnpqMhJ0a08dLAdIaRMgcGp7IWMnf3o4F3UUXQ8QJUVhExIhbFBsEhBUfQYwdntiEBB2dWoCHRJoaxcEI1p2PRx1GEAXLgdsaQM7F2hwHwIkaVAyEhMUDR0udkduIjwsZWAxYwV5WwIwPhRKFywtbFpkDRZnehMVJHx1Nh90XncQPXZ1aWckKmR7bDgnVH0GFnQUDhoQB1lvAyMTa08mFxF/dQMRKH95FGc1a1o7PzVlT2AOHm8IExF0QV0PLHZkWWc/PmdAG2wiQHEwMBRKdhIWH2xwZGUtZEEfOyMfaWEePhRIDDwMHW8AJwtoexMcJFdTOxUre08RLHJ1WhQWMmtgDxIeVVcEF3Z/URATMQZhOyQhdFExYiV8WyIVA2tAHQUiVGECO3N9CxNtBVV1OBwuFA0PFnZpawAzE2FfGBMRRWkXMTEQBQ=="

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    const/4 v15, 0x0

    invoke-static {v14, v15}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v14

    invoke-direct {v13, v14}, Ljava/lang/String;-><init>([B)V

    invoke-direct {v11, v12}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;)V

    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    move-result-object v11

    invoke-virtual {v11}, Ljava/util/Calendar;->getTime()Ljava/util/Date;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/text/SimpleDateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    move-result-object v10

    move-object v2, v10

    .line 43
    new-instance v10, Ljava/lang/String;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    const-string v12, "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"

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    const/4 v13, 0x0

    invoke-static {v12, v13}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v12

    invoke-direct {v11, v12}, Ljava/lang/String;-><init>([B)V

    move-object v11, v2

    invoke-static {v10, v11}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    move-result v10

    .line 44
    new-instance v10, Ljava/util/GregorianCalendar;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    const/16 v12, 0x7e7

    const/4 v13, 0x3

    const/16 v14, 0x1e

    invoke-direct {v11, v12, v13, v14}, Ljava/util/GregorianCalendar;-><init>(III)V

    move-object v3, v10

    .line 45
    move-object v10, v3

    const/4 v11, 0x7

    const/4 v12, 0x0

    invoke-virtual {v10, v11, v12}, Ljava/util/Calendar;->add(II)V

    .line 46
    new-instance v10, Ljava/text/SimpleDateFormat;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    new-instance v12, Ljava/lang/String;

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    const-string v14, "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"

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v14

    const/4 v15, 0x0

    invoke-static {v14, v15}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v14

    invoke-direct {v13, v14}, Ljava/lang/String;-><init>([B)V

    invoke-direct {v11, v12}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;)V

    move-object v11, v3

    invoke-virtual {v11}, Ljava/util/Calendar;->getTime()Ljava/util/Date;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/text/SimpleDateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    move-result-object v10

    move-object v4, v10

    .line 47
    move-object v10, v2

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move v5, v10

    .line 48
    move-object v10, v4

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move v6, v10

    .line 49
    move v10, v5

    move v11, v6

    if-lt v10, v11, :cond_0

    .line 50
    move-object v10, v0

    new-instance v11, Ljava/lang/String;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    const-string v13, "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"

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    const/4 v14, 0x0

    invoke-static {v13, v14}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v13

    invoke-direct {v12, v13}, Ljava/lang/String;-><init>([B)V

    invoke-static {v11}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static {v10, v11, v12}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v10

    invoke-virtual {v10}, Landroid/widget/Toast;->show()V

    .line 51
    move-object v10, v0

    new-instance v11, Ljava/lang/String;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    const-string v13, "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"

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    const/4 v14, 0x0

    invoke-static {v13, v14}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v13

    invoke-direct {v12, v13}, Ljava/lang/String;-><init>([B)V

    invoke-static {v11}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static {v10, v11, v12}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v10

    invoke-virtual {v10}, Landroid/widget/Toast;->show()V

    .line 52
    move-object v10, v0

    new-instance v11, Ljava/lang/String;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    const-string v13, "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"

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    const/4 v14, 0x0

    invoke-static {v13, v14}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v13

    invoke-direct {v12, v13}, Ljava/lang/String;-><init>([B)V

    invoke-static {v11}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static {v10, v11, v12}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v10

    invoke-virtual {v10}, Landroid/widget/Toast;->show()V

    .line 53
    move-object v10, v0

    new-instance v11, Ljava/lang/String;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    const-string v13, "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"

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    const/4 v14, 0x0

    invoke-static {v13, v14}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v13

    invoke-direct {v12, v13}, Ljava/lang/String;-><init>([B)V

    invoke-static {v11}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v11

    const/4 v12, 0x0

    invoke-static {v10, v11, v12}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v10

    invoke-virtual {v10}, Landroid/widget/Toast;->show()V

    .line 54
    new-instance v10, Landroid/content/Intent;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    const-string v12, "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"

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    new-instance v13, Ljava/lang/String;

    move-object/from16 v17, v13

    move-object/from16 v13, v17

    move-object/from16 v14, v17

    const-string v15, "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"

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    const/16 v16, 0x0

    invoke-static/range {v15 .. v16}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v15

    invoke-direct {v14, v15}, Ljava/lang/String;-><init>([B)V

    invoke-static {v13}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v13

    invoke-direct {v11, v12, v13}, Landroid/content/Intent;-><init>(Ljava/lang/String;Landroid/net/Uri;)V

    move-object v7, v10

    .line 55
    move-object v10, v0

    move-object v11, v7

    invoke-virtual {v10, v11}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    .line 56
    new-instance v10, Landroid/os/Handler;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v12

    invoke-direct {v11, v12}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    move-object v8, v10

    .line 57
    move-object v10, v8

    new-instance v11, Lcom/bad/modder/injector/FFMainActivity$100000000;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    invoke-direct {v12}, Lcom/bad/modder/injector/FFMainActivity$100000000;-><init>()V

    const/16 v12, 0x1bee

    int-to-long v12, v12

    invoke-virtual {v10, v11, v12, v13}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    move-result v10

    .line 64
    :cond_0
    sget v10, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v11, 0x17

    if-lt v10, v11, :cond_1

    move-object v10, v0

    invoke-static {v10}, Landroid/provider/Settings;->canDrawOverlays(Landroid/content/Context;)Z

    move-result v10

    if-nez v10, :cond_1

    .line 65
    move-object v10, v0

    new-instance v11, Landroid/content/Intent;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    const-string v13, "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"

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    new-instance v14, Ljava/lang/StringBuffer;

    move-object/from16 v17, v14

    move-object/from16 v14, v17

    move-object/from16 v15, v17

    invoke-direct {v15}, Ljava/lang/StringBuffer;-><init>()V

    const-string v15, "HQUTeV0DZAJ3eR9nI38BOx11FXoaLHccfg07J2FwNjcgSghiHnRrTxs+JUprMjwvZVYHJhJVS34eE11KGD4xW2IDDQ5nUB8CFR4MMBF3HHMSEHJVb2cdAmlpYDoTbwAWEhQZdRgWABxrDRkVfXkxEiQfWzEVK1lJGAclQ2kdPwFlUGENAn9fAhUEGEgUPHZ1WR0VIGlrBwwTHlcMMANBVxIVBFRcAzsva2oTOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9dExAxeVkdbBdPex8iE3pqPxEeFXEULHZvbgI7Hmh5bHskQGI+FSt4dBEtH0FeZmQFa1Y2Nx5vaSAaEFliDy41fmE+FSRnQTF7EFd9MxYOFFwQLAdVXQM/FXR5MT0XbFs8EXVdSQUsMVlpOxExYEAxMidKcWIcK0UNNxw1RmkSHW1rayFgIm9fEhx2a0kRBgNuYgMVJGdQAycHbnksGB4UXBMWC1VeOBFxYkEHOidVXGISEF0JGCwfYn4TIxdheTECHnx1eh0tFA4YAAt1WmUjLmVQEzUjRVcSFXZFExEGA3lhDRUWZFExbCN6cTYfdRQPEBc+H2o4YS1qYBMiI1R+Mh0rFFofPiVffxNkJX1fIREffwg7MnV/eQwXD0NqZGwBYHlhARFFCBMcdllPEDwAWVkSLCFiQBsiIn9bMBwqe3kdEDF9YThgA3R7HyIFVQAWHChVCTA8dkJeImAiYVZgZRFFaQM3K0VvFCMPa2s4ZCtqahcWFW9XJhV0VV0RByJXaxARPmNrMREfeWEiFg4UQBItMUtrZycSdHobbQVAaTged38TGDx2X34TYBd9fGEyEXptehIBFF4SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cQ0wdVlcEjwfbG8DPxVraWAzF29MYhwrXUAXLBNpajs7FWtCExgfeUwyEhF7SRgAMUZaZgELZV8TFiJvaSYQdRRRDy4LRFxkEQJlezEMJXxbDB91WV4TFhNpahNhLWNAAyYfbAAGFQB7DRQ8AB9hZBUUbHsQNx9/dQcQK2NKFGYPQV0CETxgegdsJ295Ah4Xd08PLjJUXgAVAmJ7BDIneV8HHyhechMuMXlgEhUFYEAbPCN8aSIdAHsKBTxySHATYCVvXxsYHnlQPhArY2gQLnJHXmc/K2hwDx4eb1cgMisYUB0HMUVrEBUSaFEXfyN8ADYYEVlbED4xS1oSbAJ9QAM8I1V1YxJ1QQEPFgN8awMVE31fMSceelt6EStrSxBmdktdACM/aFYHDhV/aSEcdFlNFxYTf2IDbBJrUR8iJ355MBF0SVwdEC11a2QkKGJCYDgjWlszHnVVCRkWfxxvAjMSYWAfYSdAW2IVK2N5FC0fS1odAQVoamA1I39bAh8eVVwPExd4Wh0jAnRqbHsFaUsQH3VZVxMXckVeEmAiaGlgOx9sdRocdBQNFxAHRVxlbBZhUh8BHkp5AxUrZw8XEA9DXQNsDmB5B2wlVVsdGR5BXxA8E2thEBUkfXsbbCd+cTEVDl5zFwAxfWk7FRN3egM8J39LDB53SXQTLBNfXWRgF2FwHxAeQFt6GCt7aRBmC39eZDchYFFgAh9FVyESd0VZED0XRWITESBPewNhH35xBzAAQVoQEHJVbDhgFU95Hz0jVwhjHXd7CTcWdx9rBxkAb1YPJBFAbQMcK2sOEi0XQ2plIC9layESB296ODAtWVwUFwsCWRMVEnRAA3sFbnksHw5ZXB0FE2leZx0Ia2kxJxN8dmIedRgNGBYAH11kIyJoe2Q4Jx9pYhJ0WUsMECFHbzknAmVwDx4XbABtFXZFXxoQC0RiEGw+YnpgYid6cQcwAEFxHQdyYWo5P3F9CxMmHh95ZB0DSUgULgMGWhQBew=="

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    move-object v15, v0

    invoke-virtual {v15}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    invoke-virtual {v14}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-static {v14}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v14

    invoke-direct {v12, v13, v14}, Landroid/content/Intent;-><init>(Ljava/lang/String;Landroid/net/Uri;)V

    invoke-virtual {v10, v11}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    .line 67
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v10

    invoke-static {v10}, Landroid/os/Process;->killProcess(I)V

    .line 77
    :goto_0
    new-instance v10, Ljava/lang/StringBuffer;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    move-object v11, v0

    invoke-virtual {v11}, Landroid/content/Context;->getCacheDir()Ljava/io/File;

    move-result-object v11

    invoke-virtual {v11}, Ljava/io/File;->getPath()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWABxrDRkVfXkxDSRXU3o3K0kPDBBydVoiFRFoUAQOIn9pIBZ2Z0oULAN+WWQRAmtQbGETHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9xHRYfTnA4PwJkQA8mElR9BxEeFXEULHJ/ag07HmRCE2ElaVNmECt4dBEtH0FeZmQFZVBgYx5vajgWdRhRDwYTfmJkFSRnaxtsJXl9MwQoFXEQLAdVXQM/Ik96MQQjVQA8EQNCejA8A0h+DRETYXAPIidKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcoHGgdLn51WhQVcmhWAwIFSm0mGRBBSBosA2xeDSdzSEExNSdHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-static {v11}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    sput-object v10, Lcom/bad/modder/injector/FFMainActivity;->cacheDir:Ljava/lang/String;

    return-void

    .line 69
    :cond_1
    new-instance v10, Landroid/os/Handler;

    move-object/from16 v17, v10

    move-object/from16 v10, v17

    move-object/from16 v11, v17

    invoke-direct {v11}, Landroid/os/Handler;-><init>()V

    move-object v7, v10

    .line 70
    move-object v10, v7

    new-instance v11, Lcom/bad/modder/injector/FFMainActivity$100000001;

    move-object/from16 v17, v11

    move-object/from16 v11, v17

    move-object/from16 v12, v17

    move-object v13, v0

    invoke-direct {v12, v13}, Lcom/bad/modder/injector/FFMainActivity$100000001;-><init>(Landroid/content/Context;)V

    const/16 v12, 0x1f4

    int-to-long v12, v12

    invoke-virtual {v10, v11, v12, v13}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    move-result v10

    goto/16 :goto_0
.end method
