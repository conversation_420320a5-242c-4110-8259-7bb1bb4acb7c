.class Lcom/projectvb/Menu$RealFeatureClickListener;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnClickListener;

# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;
.field private featureName:Ljava/lang/String;
.field private isEnabled:Z

# direct methods
.method constructor <init>(Lcom/projectvb/Menu;Ljava/lang/String;)V
    .locals 1
    .param p1, "this$0"    # Lcom/projectvb/Menu;
    .param p2, "name"    # Ljava/lang/String;

    .prologue
    .line 900
    iput-object p1, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->this$0:Lcom/projectvb/Menu;
    iput-object p2, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const/4 v0, 0x0
    iput-boolean v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->isEnabled:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 4
    .param p1, "v"    # Landroid/view/View;

    .prologue
    .line 903
    :try_start_0
    # Toggle feature state
    iget-boolean v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->isEnabled:Z
    if-eqz v0, :cond_enable

    # Disable feature
    const/4 v0, 0x0
    iput-boolean v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->isEnabled:Z
    invoke-virtual {p0, v0}, Lcom/projectvb/Menu$RealFeatureClickListener;->callRealFunction(Z)V
    invoke-virtual {p0, p1, v0}, Lcom/projectvb/Menu$RealFeatureClickListener;->updateButtonText(Landroid/view/View;Z)V

    goto :goto_end

    :cond_enable
    # Enable feature
    const/4 v0, 0x1
    iput-boolean v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->isEnabled:Z
    invoke-virtual {p0, v0}, Lcom/projectvb/Menu$RealFeatureClickListener;->callRealFunction(Z)V
    invoke-virtual {p0, p1, v0}, Lcom/projectvb/Menu$RealFeatureClickListener;->updateButtonText(Landroid/view/View;Z)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method private callRealFunction(Z)V
    .locals 3
    .param p1, "enabled"    # Z

    .prologue
    .line 930
    :try_start_0
    # Call real PMM functions
    iget-object v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const-string v1, "ESP"
    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v0

    if-eqz v0, :cond_aimbot

    # ESP function
    if-eqz p1, :cond_esp_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->AAAAAAAAA()V
    goto :goto_end
    :cond_esp_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->BBBBBBBB()V
    goto :goto_end

    :cond_aimbot
    iget-object v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const-string v1, "Aimbot"
    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v0

    if-eqz v0, :cond_speed

    # Aimbot function
    if-eqz p1, :cond_aimbot_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->CCCCCCCC()V
    goto :goto_end
    :cond_aimbot_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->DDDDDDDD()V
    goto :goto_end

    :cond_speed
    iget-object v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const-string v1, "Speed"
    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v0

    if-eqz v0, :cond_wallhack

    # Speed function
    if-eqz p1, :cond_speed_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->EEEEEEEE()V
    goto :goto_end
    :cond_speed_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->FFFFFFFF()V
    goto :goto_end

    :cond_wallhack
    iget-object v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const-string v1, "Wallhack"
    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v0

    if-eqz v0, :cond_norecoil

    # Wallhack function
    if-eqz p1, :cond_wallhack_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->GGGGGGGG()V
    goto :goto_end
    :cond_wallhack_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->HHHHHHHH()V
    goto :goto_end

    :cond_norecoil
    iget-object v0, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const-string v1, "No Recoil"
    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v0

    if-eqz v0, :goto_end

    # No Recoil function
    if-eqz p1, :cond_norecoil_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->IIIIIIIIII()V
    goto :goto_end
    :cond_norecoil_off
    invoke-static {}, Lpmm/by/p2077kng/hacker/P2077KNG;->JJJJJJJJ()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    :goto_end
    return-void
.end method

.method private updateButtonText(Landroid/view/View;Z)V
    .locals 4
    .param p1, "button"    # Landroid/view/View;
    .param p2, "enabled"    # Z

    .prologue
    .line 980
    move-object v0, p1
    check-cast v0, Landroid/widget/Button;

    # Extract feature name
    iget-object v1, p0, Lcom/projectvb/Menu$RealFeatureClickListener;->featureName:Ljava/lang/String;
    const-string v2, ":"
    invoke-virtual {v1, v2}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;
    move-result-object v1
    const/4 v2, 0x0
    aget-object v1, v1, v2

    # Update button text
    new-instance v2, Ljava/lang/StringBuilder;
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    const-string v3, ": "
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    if-eqz p2, :cond_off
    const-string v3, "ON"
    goto :goto_set_text
    :cond_off
    const-string v3, "OFF"
    :goto_set_text
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;
    move-result-object v2
    invoke-virtual {v0, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    # Update button color
    if-eqz p2, :cond_color_off
    const v1, -0xff6634
    invoke-virtual {v0, v1}, Landroid/widget/Button;->setBackgroundColor(I)V
    goto :goto_end
    :cond_color_off
    const v1, -0x333334
    invoke-virtual {v0, v1}, Landroid/widget/Button;->setBackgroundColor(I)V

    :goto_end
    return-void
.end method
