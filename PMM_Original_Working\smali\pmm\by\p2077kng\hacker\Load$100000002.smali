.class Lpmm/by/p2077kng/hacker/Load$100000002;
.super Ljava/lang/Object;
.source "Load.java"

# interfaces
.implements Landroid/content/DialogInterface$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Load;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000002"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Load;

.field private final val$Rooted:Ljava/lang/String;

.field private final val$activity:Landroid/app/Activity;

.field private final val$gameff:Ljava/lang/String;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Load;Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;)V
    .locals 8

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v6, v0

    invoke-direct {v6}, Ljava/lang/Object;-><init>()V

    move-object v6, v0

    move-object v7, v1

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Load$100000002;->this$0:Lpmm/by/p2077kng/hacker/Load;

    move-object v6, v0

    move-object v7, v2

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Load$100000002;->val$activity:Landroid/app/Activity;

    move-object v6, v0

    move-object v7, v3

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Load$100000002;->val$Rooted:Ljava/lang/String;

    move-object v6, v0

    move-object v7, v4

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Load$100000002;->val$gameff:Ljava/lang/String;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Load$100000002;)Lpmm/by/p2077kng/hacker/Load;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Load$100000002;->this$0:Lpmm/by/p2077kng/hacker/Load;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/content/DialogInterface;I)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/DialogInterface;",
            "I)V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 227
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Load$100000002;->val$activity:Landroid/app/Activity;

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Load$100000002;->val$Rooted:Ljava/lang/String;

    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/Load$100000002;->val$gameff:Ljava/lang/String;

    invoke-static {v4, v5, v6}, Lpmm/by/p2077kng/hacker/LoaderBad;->EdlMode(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
