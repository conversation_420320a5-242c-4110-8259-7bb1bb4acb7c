.class Leu/chainfire/libsuperuser/Shell$Interactive$9;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

.field final synthetic val$STDERR:Ljava/util/List;

.field final synthetic val$STDOUT:Ljava/util/List;

.field final synthetic val$exitCode:[I


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;[ILjava/util/List;Ljava/util/List;)V
    .locals 7

    .prologue
    .line 2600
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, v0

    move-object v6, v1

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v5, v0

    move-object v6, v2

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$exitCode:[I

    move-object v5, v0

    move-object v6, v3

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$STDOUT:Ljava/util/List;

    move-object v5, v0

    move-object v6, v4

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$STDERR:Ljava/util/List;

    move-object v5, v0

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCommandResult(IILjava/util/List;Ljava/util/List;)V
    .locals 8
    .param p3    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .prologue
    .line 2603
    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$exitCode:[I

    const/4 v6, 0x0

    move v7, v2

    aput v7, v5, v6

    .line 2604
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$STDOUT:Ljava/util/List;

    if-eqz v5, :cond_0

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$STDOUT:Ljava/util/List;

    move-object v6, v3

    invoke-interface {v5, v6}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    move-result v5

    .line 2605
    :cond_0
    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$STDERR:Ljava/util/List;

    if-eqz v5, :cond_1

    move-object v5, v0

    iget-object v5, v5, Leu/chainfire/libsuperuser/Shell$Interactive$9;->val$STDERR:Ljava/util/List;

    move-object v6, v4

    invoke-interface {v5, v6}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    move-result v5

    .line 2606
    :cond_1
    return-void
.end method
