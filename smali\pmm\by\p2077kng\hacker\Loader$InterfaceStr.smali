.class interface Lpmm/by/p2077kng/hacker/Loader$InterfaceStr;
.super Ljava/lang/Object;
.source "Loader.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20a
    name = "InterfaceStr"
.end annotation


# virtual methods
.method public abstract OnWrite(Ljava/lang/String;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation
.end method
