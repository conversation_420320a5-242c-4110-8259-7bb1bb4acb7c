.class Lcom/projectvb/Menu$1;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Menu;->onCreateTemplate()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;

.field final synthetic val$container_menu:Landroid/widget/LinearLayout;

.field final synthetic val$icon_cheat:Lcom/projectvb/ImageBase64;


# direct methods
.method constructor <init>(Lcom/projectvb/Menu;Lcom/projectvb/ImageBase64;Landroid/widget/LinearLayout;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Menu;

    .line 120
    iput-object p1, p0, Lcom/projectvb/Menu$1;->this$0:Lcom/projectvb/Menu;

    iput-object p2, p0, Lcom/projectvb/Menu$1;->val$icon_cheat:Lcom/projectvb/ImageBase64;

    iput-object p3, p0, Lcom/projectvb/Menu$1;->val$container_menu:Landroid/widget/LinearLayout;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2
    .param p1, "view"    # Landroid/view/View;

    .line 123
    iget-object v0, p0, Lcom/projectvb/Menu$1;->val$icon_cheat:Lcom/projectvb/ImageBase64;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Lcom/projectvb/ImageBase64;->setVisibility(I)V

    .line 124
    iget-object v0, p0, Lcom/projectvb/Menu$1;->val$container_menu:Landroid/widget/LinearLayout;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 125
    return-void
.end method
