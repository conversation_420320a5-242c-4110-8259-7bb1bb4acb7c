.class Lpmm/by/p2077kng/hacker/Load$100000001;
.super Ljava/lang/Object;
.source "Load.java"

# interfaces
.implements Ljavax/net/ssl/HostnameVerifier;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Load;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000001"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Load;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Load;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Load$100000001;->this$0:Lpmm/by/p2077kng/hacker/Load;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Load$100000001;)Lpmm/by/p2077kng/hacker/Load;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Load$100000001;->this$0:Lpmm/by/p2077kng/hacker/Load;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public verify(Ljava/lang/String;Ljavax/net/ssl/SSLSession;)Z
    .locals 5
    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 166
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    const/4 v4, 0x1

    move v0, v4

    return v0
.end method
