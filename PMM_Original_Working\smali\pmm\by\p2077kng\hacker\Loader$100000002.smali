.class Lpmm/by/p2077kng/hacker/Loader$100000002;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000002"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lpmm/by/p2077kng/hacker/Loader$100000002$100000000;,
        Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;
    }
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000002;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000002;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000002;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 258
    move-object v0, p0

    move-object v1, p1

    const/4 v7, 0x0

    check-cast v7, Landroid/app/AlertDialog;

    move-object v3, v7

    .line 259
    new-instance v7, Landroid/app/AlertDialog$Builder;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v0

    iget-object v9, v9, Lpmm/by/p2077kng/hacker/Loader$100000002;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-virtual {v9}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v9

    invoke-direct {v8, v9}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    const-string v8, "\n\tDo you want to close??"

    invoke-virtual {v7, v8}, Landroid/app/AlertDialog$Builder;->setMessage(Ljava/lang/CharSequence;)Landroid/app/AlertDialog$Builder;

    move-result-object v7

    const-string v8, "OK"

    new-instance v9, Lpmm/by/p2077kng/hacker/Loader$100000002$100000000;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    move-object v11, v0

    invoke-direct {v10, v11}, Lpmm/by/p2077kng/hacker/Loader$100000002$100000000;-><init>(Lpmm/by/p2077kng/hacker/Loader$100000002;)V

    invoke-virtual {v7, v8, v9}, Landroid/app/AlertDialog$Builder;->setPositiveButton(Ljava/lang/CharSequence;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v7

    const-string v8, "Cancel"

    new-instance v9, Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    move-object v11, v0

    invoke-direct {v10, v11}, Lpmm/by/p2077kng/hacker/Loader$100000002$100000001;-><init>(Lpmm/by/p2077kng/hacker/Loader$100000002;)V

    invoke-virtual {v7, v8, v9}, Landroid/app/AlertDialog$Builder;->setNegativeButton(Ljava/lang/CharSequence;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v7

    invoke-virtual {v7}, Landroid/app/AlertDialog$Builder;->create()Landroid/app/AlertDialog;

    move-result-object v7

    move-object v4, v7

    .line 276
    sget v7, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v8, 0x19

    if-le v7, v8, :cond_0

    .line 278
    const/16 v7, 0x7f6

    move v5, v7

    .line 283
    :goto_0
    move-object v7, v4

    invoke-virtual {v7}, Landroid/app/AlertDialog;->getWindow()Landroid/view/Window;

    move-result-object v7

    move v8, v5

    invoke-virtual {v7, v8}, Landroid/view/Window;->setType(I)V

    .line 284
    move-object v7, v4

    invoke-virtual {v7}, Landroid/app/AlertDialog;->show()V

    return-void

    .line 281
    :cond_0
    const/16 v7, 0x7d2

    move v5, v7

    goto :goto_0
.end method
