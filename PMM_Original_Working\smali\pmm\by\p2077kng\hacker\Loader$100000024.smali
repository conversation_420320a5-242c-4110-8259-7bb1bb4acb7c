.class Lpmm/by/p2077kng/hacker/Loader$100000024;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000024"
.end annotation


# instance fields
.field private isActive:Z

.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;

.field private final val$button:Landroid/widget/TextView;

.field private final val$feature2:Ljava/lang/String;

.field private final val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;Landroid/widget/TextView;Ljava/lang/String;)V
    .locals 8

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v6, v0

    invoke-direct {v6}, Ljava/lang/Object;-><init>()V

    move-object v6, v0

    move-object v7, v1

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000024;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v6, v0

    move-object v7, v2

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;

    move-object v6, v0

    move-object v7, v3

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$button:Landroid/widget/TextView;

    move-object v6, v0

    move-object v7, v4

    iput-object v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$feature2:Ljava/lang/String;

    move-object v6, v0

    const/4 v7, 0x1

    iput-boolean v7, v6, Lpmm/by/p2077kng/hacker/Loader$100000024;->isActive:Z

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000024;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1194
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$interfaceBtn:Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;

    invoke-interface {v3}, Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;->OnWrite()V

    .line 1195
    move-object v3, v0

    iget-boolean v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->isActive:Z

    if-eqz v3, :cond_0

    .line 1196
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$button:Landroid/widget/TextView;

    new-instance v4, Ljava/lang/StringBuffer;

    move-object v6, v4

    move-object v4, v6

    move-object v5, v6

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$feature2:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, " ON"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1197
    move-object v3, v0

    const/4 v4, 0x0

    iput-boolean v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->isActive:Z

    .line 1198
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$button:Landroid/widget/TextView;

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000024;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v4

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fucking(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1203
    :goto_0
    return-void

    .line 1201
    :cond_0
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$button:Landroid/widget/TextView;

    new-instance v4, Ljava/lang/StringBuffer;

    move-object v6, v4

    move-object v4, v6

    move-object v5, v6

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$feature2:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, " OFF"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1202
    move-object v3, v0

    const/4 v4, 0x1

    iput-boolean v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->isActive:Z

    .line 1203
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000024;->val$button:Landroid/widget/TextView;

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000024;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v4

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fuckass(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    goto :goto_0
.end method
