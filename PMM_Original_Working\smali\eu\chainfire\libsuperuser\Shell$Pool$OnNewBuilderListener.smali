.class public interface abstract Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;
.super Ljava/lang/Object;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell$Pool;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnNewBuilderListener"
.end annotation


# virtual methods
.method public abstract newBuilder()Leu/chainfire/libsuperuser/Shell$Builder;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end method
