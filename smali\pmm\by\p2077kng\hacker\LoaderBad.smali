.class public Lpmm/by/p2077kng/hacker/LoaderBad;
.super Ljava/lang/Object;
.source "LoaderBad.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lpmm/by/p2077kng/hacker/LoaderBad$100000000;,
        Lpmm/by/p2077kng/hacker/LoaderBad$100000001;
    }
.end annotation


# static fields
.field static Very:Z


# direct methods
.method static final constructor <clinit>()V
    .locals 3

    .prologue
    const/4 v2, 0x1

    sput-boolean v2, Lpmm/by/p2077kng/hacker/LoaderBad;->Very:Z

    .line 23
    const-string v2, "P2077KNG"

    invoke-static {v2}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .prologue
    .line 89
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static Apps(Landroid/content/Context;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 39
    move-object v0, p0

    :try_start_0
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v4

    const-string v5, "su"

    invoke-virtual {v4, v5}, Ljava/lang/Runtime;->exec(Ljava/lang/String;)Ljava/lang/Process;

    move-result-object v4

    .line 40
    move-object v4, v0

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/LoaderBad;->DetectRoot(Landroid/content/Context;)V

    .line 41
    const-string v4, "setenforce 0"

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$SU;->run(Ljava/lang/String;)Ljava/util/List;

    move-result-object v4

    .line 42
    const/4 v4, 0x1

    sput-boolean v4, Lpmm/by/p2077kng/hacker/LoaderBad;->Very:Z
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 45
    :goto_0
    return-void

    .line 42
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 44
    move-object v4, v2

    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 45
    const/4 v4, 0x0

    sput-boolean v4, Lpmm/by/p2077kng/hacker/LoaderBad;->Very:Z

    goto :goto_0
.end method

.method private static ConnectCheck(Landroid/content/Context;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 80
    move-object v0, p0

    sget-boolean v4, Lpmm/by/p2077kng/hacker/LoaderBad;->Very:Z

    if-eqz v4, :cond_0

    .line 82
    move-object v4, v0

    invoke-virtual {v4}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v4

    const-string v5, "com.dts.freefireth"

    invoke-virtual {v4, v5}, Landroid/content/pm/PackageManager;->getLaunchIntentForPackage(Ljava/lang/String;)Landroid/content/Intent;

    move-result-object v4

    move-object v2, v4

    .line 84
    move-object v4, v2

    if-eqz v4, :cond_0

    :cond_0
    return-void
.end method

.method private static DetectRoot(Landroid/content/Context;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 63
    move-object v0, p0

    :try_start_0
    sget-object v4, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    new-instance v5, Lpmm/by/p2077kng/hacker/LoaderBad$100000001;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Lpmm/by/p2077kng/hacker/LoaderBad$100000001;-><init>(Landroid/content/Context;)V

    invoke-virtual {v4, v5}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->get(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    :try_end_0
    .catch Leu/chainfire/libsuperuser/Shell$ShellDiedException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v4

    .line 74
    :goto_0
    return-void

    .line 63
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 73
    move-object v4, v0

    invoke-static {v4}, Lpmm/by/p2077kng/hacker/LoaderBad;->showNoRootMessage(Landroid/content/Context;)V

    .line 74
    const/4 v4, 0x0

    sput-boolean v4, Lpmm/by/p2077kng/hacker/LoaderBad;->Very:Z

    goto :goto_0
.end method

.method public static EdlMode(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 34
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v5, v0

    move-object v6, v1

    move-object v7, v2

    invoke-static {v5, v6, v7}, Lpmm/by/p2077kng/hacker/LoaderBad;->RootCheck(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method static native RootCheck(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation
.end method

.method public static Start(Landroid/content/Context;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 29
    move-object v0, p0

    move-object v3, v0

    invoke-static {v3}, Lpmm/by/p2077kng/hacker/LoaderBad;->Apps(Landroid/content/Context;)V

    .line 30
    move-object v3, v0

    invoke-static {v3}, Lpmm/by/p2077kng/hacker/LoaderBad;->ConnectCheck(Landroid/content/Context;)V

    return-void
.end method

.method public static showNoRootMessage(Landroid/content/Context;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 50
    move-object v0, p0

    new-instance v3, Landroid/app/AlertDialog$Builder;

    move-object v7, v3

    move-object v3, v7

    move-object v4, v7

    move-object v5, v0

    invoke-direct {v4, v5}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    const-string v4, "Unable to obtain root access"

    invoke-virtual {v3, v4}, Landroid/app/AlertDialog$Builder;->setMessage(Ljava/lang/CharSequence;)Landroid/app/AlertDialog$Builder;

    move-result-object v3

    const v4, 0x104000a

    new-instance v5, Lpmm/by/p2077kng/hacker/LoaderBad$100000000;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    invoke-direct {v6}, Lpmm/by/p2077kng/hacker/LoaderBad$100000000;-><init>()V

    invoke-virtual {v3, v4, v5}, Landroid/app/AlertDialog$Builder;->setNegativeButton(ILandroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v3

    invoke-virtual {v3}, Landroid/app/AlertDialog$Builder;->show()Landroid/app/AlertDialog;

    move-result-object v3

    return-void
.end method
