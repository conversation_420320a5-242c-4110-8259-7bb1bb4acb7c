.class Lcom/projectvb/Login$2$1;
.super Ljava/lang/Object;
.source "Login.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Login$2;->onClick(Landroid/view/View;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lcom/projectvb/Login$2;

.field final synthetic val$licenseKey:Ljava/lang/String;


# direct methods
.method constructor <init>(Lcom/projectvb/Login$2;Ljava/lang/String;)V
    .locals 0
    .param p1, "this$1"    # Lcom/projectvb/Login$2;

    .line 158
    iput-object p1, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iput-object p2, p0, Lcom/projectvb/Login$2$1;->val$licenseKey:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private enableLoginUI()V
    .locals 2

    .line 200
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lcom/projectvb/Login$2$1$2;

    invoke-direct {v1, p0}, Lcom/projectvb/Login$2$1$2;-><init>(Lcom/projectvb/Login$2$1;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 207
    return-void
.end method


# virtual methods
.method public run()V
    .locals 6

    .line 162
    const-string v0, "UTF-8"

    :try_start_0
    iget-object v1, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v1, v1, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v1}, Lcom/projectvb/Login;->access$600(Lcom/projectvb/Login;)Ljava/lang/String;

    move-result-object v1

    .line 163
    .local v1, "hwid":Ljava/lang/String;
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "https://keyauth.win/api/1.3/?type=license&key="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v3, p0, Lcom/projectvb/Login$2$1;->val$licenseKey:Ljava/lang/String;

    .line 164
    invoke-static {v3, v0}, Ljava/net/URLEncoder;->encode(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "&hwid="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 165
    invoke-static {v1, v0}, Ljava/net/URLEncoder;->encode(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "&sessionid="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v3, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v3, v3, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    .line 166
    invoke-static {v3}, Lcom/projectvb/Login;->access$100(Lcom/projectvb/Login;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v3, v0}, Ljava/net/URLEncoder;->encode(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "&name="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "websit"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "&ownerid="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "PF7qFni9VY"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "&secret="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "b684ef652abd78d502b9a4ac1e0d70c4687aeb0adbe79d3f9e1e8e63799b0bdd"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "&ver="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "1.0"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 172
    .local v0, "url":Ljava/lang/String;
    iget-object v2, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v2, v2, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v2, v0}, Lcom/projectvb/Login;->access$000(Lcom/projectvb/Login;Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v2

    .line 174
    .local v2, "response":Lorg/json/JSONObject;
    const-string v3, "success"

    invoke-virtual {v2, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 176
    iget-object v3, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v3, v3, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v3}, Lcom/projectvb/Login;->access$700(Lcom/projectvb/Login;)Landroid/content/Context;

    move-result-object v3

    const-string v4, "RageCheatsPrefs"

    const/4 v5, 0x0

    invoke-virtual {v3, v4, v5}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v3

    .line 177
    invoke-interface {v3}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v3

    const-string v4, "saved_license"

    iget-object v5, p0, Lcom/projectvb/Login$2$1;->val$licenseKey:Ljava/lang/String;

    invoke-interface {v3, v4, v5}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v3

    invoke-interface {v3}, Landroid/content/SharedPreferences$Editor;->apply()V

    .line 179
    iget-object v3, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v3, v3, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    const-string v4, "Login successful"

    invoke-static {v3, v4}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V

    .line 181
    new-instance v3, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v4

    invoke-direct {v3, v4}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v4, Lcom/projectvb/Login$2$1$1;

    invoke-direct {v4, p0}, Lcom/projectvb/Login$2$1$1;-><init>(Lcom/projectvb/Login$2$1;)V

    invoke-virtual {v3, v4}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_0

    .line 188
    :cond_0
    iget-object v3, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v3, v3, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Invalid license: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, "message"

    invoke-virtual {v2, v5}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v3, v4}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V

    .line 189
    invoke-direct {p0}, Lcom/projectvb/Login$2$1;->enableLoginUI()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 196
    .end local v0    # "url":Ljava/lang/String;
    .end local v1    # "hwid":Ljava/lang/String;
    .end local v2    # "response":Lorg/json/JSONObject;
    :goto_0
    goto :goto_1

    .line 192
    :catch_0
    move-exception v0

    .line 193
    .local v0, "e":Ljava/lang/Exception;
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 194
    iget-object v1, p0, Lcom/projectvb/Login$2$1;->this$1:Lcom/projectvb/Login$2;

    iget-object v1, v1, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    const-string v2, "Network error or invalid response."

    invoke-static {v1, v2}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V

    .line 195
    invoke-direct {p0}, Lcom/projectvb/Login$2$1;->enableLoginUI()V

    .line 197
    .end local v0    # "e":Ljava/lang/Exception;
    :goto_1
    return-void
.end method
