.class Lpmm/by/p2077kng/hacker/Loader$100000007;
.super Ljava/lang/Object;
.source "Loader.java"

# interfaces
.implements Landroid/view/View$OnTouchListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Loader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000007"
.end annotation


# instance fields
.field final collapsedView:Landroid/view/View;

.field final expandedView:Landroid/view/View;

.field private initialTouchX:F

.field private initialTouchY:F

.field private initialX:I

.field private initialY:I

.field private final this$0:Lpmm/by/p2077kng/hacker/Loader;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v3, v0

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000007;->collapsedView:Landroid/view/View;

    move-object v3, v0

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Loader$100000007;->expandedView:Landroid/view/View;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Loader$100000007;)Lpmm/by/p2077kng/hacker/Loader;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 11

    .prologue
    .line 661
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v7, v2

    invoke-virtual {v7}, Landroid/view/MotionEvent;->getAction()I

    move-result v7

    packed-switch v7, :pswitch_data_0

    .line 683
    const/4 v7, 0x0

    move v0, v7

    :goto_0
    return v0

    .line 663
    :pswitch_0
    move-object v7, v0

    move-object v8, v0

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    iget v8, v8, Landroid/view/WindowManager$LayoutParams;->x:I

    iput v8, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialX:I

    .line 664
    move-object v7, v0

    move-object v8, v0

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    iget v8, v8, Landroid/view/WindowManager$LayoutParams;->y:I

    iput v8, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialY:I

    .line 665
    move-object v7, v0

    move-object v8, v2

    invoke-virtual {v8}, Landroid/view/MotionEvent;->getRawX()F

    move-result v8

    iput v8, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialTouchX:F

    .line 666
    move-object v7, v0

    move-object v8, v2

    invoke-virtual {v8}, Landroid/view/MotionEvent;->getRawY()F

    move-result v8

    iput v8, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialTouchY:F

    .line 667
    const/4 v7, 0x1

    move v0, v7

    goto :goto_0

    .line 669
    :pswitch_1
    move-object v7, v2

    invoke-virtual {v7}, Landroid/view/MotionEvent;->getRawX()F

    move-result v7

    move-object v8, v0

    iget v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialTouchX:F

    sub-float/2addr v7, v8

    float-to-int v7, v7

    move v4, v7

    .line 670
    move-object v7, v2

    invoke-virtual {v7}, Landroid/view/MotionEvent;->getRawY()F

    move-result v7

    move-object v8, v0

    iget v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialTouchY:F

    sub-float/2addr v7, v8

    float-to-int v7, v7

    move v5, v7

    .line 671
    move v7, v4

    const/16 v8, 0xa

    if-ge v7, v8, :cond_0

    move v7, v5

    const/16 v8, 0xa

    if-ge v7, v8, :cond_0

    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    invoke-virtual {v7}, Lpmm/by/p2077kng/hacker/Loader;->isViewCollapsed()Z

    move-result v7

    if-eqz v7, :cond_0

    .line 672
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->collapsedView:Landroid/view/View;

    const/16 v8, 0x8

    invoke-virtual {v7, v8}, Landroid/view/View;->setVisibility(I)V

    .line 673
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->expandedView:Landroid/view/View;

    const/4 v8, 0x0

    invoke-virtual {v7, v8}, Landroid/view/View;->setVisibility(I)V

    .line 676
    :cond_0
    const/4 v7, 0x1

    move v0, v7

    goto :goto_0

    .line 678
    :pswitch_2
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    move-object v8, v0

    iget v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialX:I

    move-object v9, v2

    invoke-virtual {v9}, Landroid/view/MotionEvent;->getRawX()F

    move-result v9

    move-object v10, v0

    iget v10, v10, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialTouchX:F

    sub-float/2addr v9, v10

    float-to-int v9, v9

    add-int/2addr v8, v9

    iput v8, v7, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 679
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    move-object v8, v0

    iget v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialY:I

    move-object v9, v2

    invoke-virtual {v9}, Landroid/view/MotionEvent;->getRawY()F

    move-result v9

    move-object v10, v0

    iget v10, v10, Lpmm/by/p2077kng/hacker/Loader$100000007;->initialTouchY:F

    sub-float/2addr v9, v10

    float-to-int v9, v9

    add-int/2addr v8, v9

    iput v8, v7, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 680
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    move-object v8, v0

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/Loader;->mFloatingView:Landroid/view/View;

    move-object v9, v0

    iget-object v9, v9, Lpmm/by/p2077kng/hacker/Loader$100000007;->this$0:Lpmm/by/p2077kng/hacker/Loader;

    iget-object v9, v9, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v7, v8, v9}, Landroid/view/WindowManager;->updateViewLayout(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 681
    const/4 v7, 0x1

    move v0, v7

    goto/16 :goto_0

    .line 661
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
        :pswitch_1
        :pswitch_2
    .end packed-switch
.end method
