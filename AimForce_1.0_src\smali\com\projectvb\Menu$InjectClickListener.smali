.class Lcom/projectvb/Menu$InjectClickListener;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnClickListener;

# instance fields
.field final synthetic this$0:Lcom/projectvb/Menu;

# direct methods
.method constructor <init>(Lcom/projectvb/Menu;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Menu;

    .prologue
    .line 500
    iput-object p1, p0, Lcom/projectvb/Menu$InjectClickListener;->this$0:Lcom/projectvb/Menu;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 4
    .param p1, "v"    # Landroid/view/View;

    .prologue
    .line 503
    :try_start_0
    # Check if emulator
    invoke-static {}, Lcom/projectvb/Utils;->currArch()Ljava/lang/String;
    move-result-object v0
    const-string v1, "x86"
    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v0

    if-eqz v0, :cond_normal

    # Emulator injection
    iget-object v0, p0, Lcom/projectvb/Menu$InjectClickListener;->this$0:Lcom/projectvb/Menu;
    const-string v1, "com.dts.freefireth"
    const-string v2, "libserver.so"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->InjectX86(Ljava/lang/String;Ljava/lang/String;)Z
    move-result v0

    goto :goto_result

    :cond_normal
    # Normal injection
    iget-object v0, p0, Lcom/projectvb/Menu$InjectClickListener;->this$0:Lcom/projectvb/Menu;
    invoke-virtual {v0}, Lcom/projectvb/Menu;->Inject()V
    const/4 v0, 0x1

    :goto_result
    if-eqz v0, :cond_failed

    # Success - change button text
    check-cast p1, Landroid/widget/Button;
    const-string v1, "✅ INJECTED"
    invoke-virtual {p1, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V
    const v1, -0xff6634
    invoke-virtual {p1, v1}, Landroid/widget/Button;->setBackgroundColor(I)V

    goto :goto_end

    :cond_failed
    # Failed - change button text
    check-cast p1, Landroid/widget/Button;
    const-string v1, "❌ FAILED"
    invoke-virtual {p1, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V
    const v1, -0x10000
    invoke-virtual {p1, v1}, Landroid/widget/Button;->setBackgroundColor(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    # Exception occurred
    check-cast p1, Landroid/widget/Button;
    const-string v1, "⚠️ ERROR"
    invoke-virtual {p1, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V
    const v1, -0x10000
    invoke-virtual {p1, v1}, Landroid/widget/Button;->setBackgroundColor(I)V

    :goto_end
    return-void
.end method
