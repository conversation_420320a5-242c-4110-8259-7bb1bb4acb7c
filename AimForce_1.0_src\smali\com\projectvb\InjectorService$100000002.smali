.class Lcom/projectvb/InjectorService$100000002;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000002"
.end annotation


# instance fields
.field private final this$0:Lcom/projectvb/InjectorService;


# direct methods
.method constructor <init>(Lcom/projectvb/InjectorService;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/projectvb/InjectorService$100000002;->this$0:Lcom/projectvb/InjectorService;

    return-void
.end method

.method static access$0(Lcom/projectvb/InjectorService$100000002;)Lcom/projectvb/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000002;->this$0:Lcom/projectvb/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 531
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lcom/projectvb/InjectorService$100000002;->this$0:Lcom/projectvb/InjectorService;

    invoke-virtual {v3}, Lcom/projectvb/InjectorService;->Inject()V

    return-void
.end method
