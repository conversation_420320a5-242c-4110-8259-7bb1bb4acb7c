.class Lpmm/by/p2077kng/hacker/Load$100000000;
.super Ljava/lang/Object;
.source "Load.java"

# interfaces
.implements Ljavax/net/ssl/X509TrustManager;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/Load;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000000"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/Load;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/Load;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Load$100000000;->this$0:Lpmm/by/p2077kng/hacker/Load;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/Load$100000000;)Lpmm/by/p2077kng/hacker/Load;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Load$100000000;->this$0:Lpmm/by/p2077kng/hacker/Load;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public checkClientTrusted([Ljava/security/cert/X509Certificate;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/security/cert/X509Certificate;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    return-void
.end method

.method public checkServerTrusted([Ljava/security/cert/X509Certificate;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/security/cert/X509Certificate;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    return-void
.end method

.method public getAcceptedIssuers()[Ljava/security/cert/X509Certificate;
    .locals 3

    .prologue
    .line 157
    move-object v0, p0

    const/4 v2, 0x0

    new-array v2, v2, [Ljava/security/cert/X509Certificate;

    move-object v0, v2

    return-object v0
.end method
