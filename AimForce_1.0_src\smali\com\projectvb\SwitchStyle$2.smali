.class Lcom/projectvb/SwitchStyle$2;
.super Ljava/lang/Object;
.source "SwitchStyle.java"

# interfaces
.implements Landroid/animation/ValueAnimator$AnimatorUpdateListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/SwitchStyle;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/SwitchStyle;


# direct methods
.method constructor <init>(Lcom/projectvb/SwitchStyle;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/SwitchStyle;

    .line 1005
    iput-object p1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onAnimationUpdate(Landroid/animation/ValueAnimator;)V
    .locals 6
    .param p1, "animation"    # Landroid/animation/ValueAnimator;

    .line 1008
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Float;

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v0

    .line 1009
    .local v0, "value":F
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$300(Lcom/projectvb/SwitchStyle;)I

    move-result v1

    const/4 v2, 0x1

    if-eq v1, v2, :cond_1

    const/4 v3, 0x3

    if-eq v1, v3, :cond_1

    const/4 v3, 0x4

    if-eq v1, v3, :cond_1

    const/4 v2, 0x5

    if-eq v1, v2, :cond_0

    goto/16 :goto_0

    .line 1038
    :cond_0
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v1

    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v2

    iget v2, v2, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1039
    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$600(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v3

    iget v3, v3, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    iget-object v4, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v4}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v4

    iget v4, v4, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    sub-float/2addr v3, v4

    mul-float v3, v3, v0

    add-float/2addr v2, v3

    iput v2, v1, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    .line 1041
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v1

    iget v1, v1, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$800(Lcom/projectvb/SwitchStyle;)F

    move-result v2

    sub-float/2addr v1, v2

    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$900(Lcom/projectvb/SwitchStyle;)F

    move-result v2

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$800(Lcom/projectvb/SwitchStyle;)F

    move-result v3

    sub-float/2addr v2, v3

    div-float/2addr v1, v2

    .line 1043
    .local v1, "fraction":F
    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v2

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$700(Lcom/projectvb/SwitchStyle;)Landroid/animation/ArgbEvaluator;

    move-result-object v3

    iget-object v4, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1045
    invoke-static {v4}, Lcom/projectvb/SwitchStyle;->access$1000(Lcom/projectvb/SwitchStyle;)I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    iget-object v5, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1046
    invoke-static {v5}, Lcom/projectvb/SwitchStyle;->access$1100(Lcom/projectvb/SwitchStyle;)I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    .line 1043
    invoke-virtual {v3, v1, v4, v5}, Landroid/animation/ArgbEvaluator;->evaluate(FLjava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    iput v3, v2, Lcom/projectvb/SwitchStyle$ViewState;->checkStateColor:I

    .line 1049
    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v2

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$1200(Lcom/projectvb/SwitchStyle;)F

    move-result v3

    mul-float v3, v3, v1

    iput v3, v2, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    .line 1050
    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v2

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$700(Lcom/projectvb/SwitchStyle;)Landroid/animation/ArgbEvaluator;

    move-result-object v3

    const/4 v4, 0x0

    .line 1052
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    iget-object v5, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1053
    invoke-static {v5}, Lcom/projectvb/SwitchStyle;->access$1300(Lcom/projectvb/SwitchStyle;)I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    .line 1050
    invoke-virtual {v3, v1, v4, v5}, Landroid/animation/ArgbEvaluator;->evaluate(FLjava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    iput v3, v2, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    .line 1055
    goto/16 :goto_0

    .line 1015
    .end local v1    # "fraction":F
    :cond_1
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v1

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$700(Lcom/projectvb/SwitchStyle;)Landroid/animation/ArgbEvaluator;

    move-result-object v3

    iget-object v4, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1017
    invoke-static {v4}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v4

    iget v4, v4, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    iget-object v5, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1018
    invoke-static {v5}, Lcom/projectvb/SwitchStyle;->access$600(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v5

    iget v5, v5, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    .line 1015
    invoke-virtual {v3, v0, v4, v5}, Landroid/animation/ArgbEvaluator;->evaluate(FLjava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    iput v3, v1, Lcom/projectvb/SwitchStyle$ViewState;->checkedLineColor:I

    .line 1021
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v1

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v3

    iget v3, v3, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    iget-object v4, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1022
    invoke-static {v4}, Lcom/projectvb/SwitchStyle;->access$600(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v4

    iget v4, v4, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    iget-object v5, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v5}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v5

    iget v5, v5, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    sub-float/2addr v4, v5

    mul-float v4, v4, v0

    add-float/2addr v3, v4

    iput v3, v1, Lcom/projectvb/SwitchStyle$ViewState;->radius:F

    .line 1024
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$300(Lcom/projectvb/SwitchStyle;)I

    move-result v1

    if-eq v1, v2, :cond_2

    .line 1025
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v1

    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v2

    iget v2, v2, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1026
    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$600(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v3

    iget v3, v3, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    iget-object v4, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v4}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v4

    iget v4, v4, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    sub-float/2addr v3, v4

    mul-float v3, v3, v0

    add-float/2addr v2, v3

    iput v2, v1, Lcom/projectvb/SwitchStyle$ViewState;->buttonX:F

    .line 1029
    :cond_2
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v1}, Lcom/projectvb/SwitchStyle;->access$400(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v1

    iget-object v2, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-static {v2}, Lcom/projectvb/SwitchStyle;->access$700(Lcom/projectvb/SwitchStyle;)Landroid/animation/ArgbEvaluator;

    move-result-object v2

    iget-object v3, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1031
    invoke-static {v3}, Lcom/projectvb/SwitchStyle;->access$500(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v3

    iget v3, v3, Lcom/projectvb/SwitchStyle$ViewState;->checkStateColor:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    iget-object v4, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    .line 1032
    invoke-static {v4}, Lcom/projectvb/SwitchStyle;->access$600(Lcom/projectvb/SwitchStyle;)Lcom/projectvb/SwitchStyle$ViewState;

    move-result-object v4

    iget v4, v4, Lcom/projectvb/SwitchStyle$ViewState;->checkStateColor:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    .line 1029
    invoke-virtual {v2, v0, v3, v4}, Landroid/animation/ArgbEvaluator;->evaluate(FLjava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    iput v2, v1, Lcom/projectvb/SwitchStyle$ViewState;->checkStateColor:I

    .line 1035
    nop

    .line 1064
    :goto_0
    iget-object v1, p0, Lcom/projectvb/SwitchStyle$2;->this$0:Lcom/projectvb/SwitchStyle;

    invoke-virtual {v1}, Lcom/projectvb/SwitchStyle;->postInvalidate()V

    .line 1065
    return-void
.end method
