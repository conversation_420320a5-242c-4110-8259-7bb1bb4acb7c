.class public interface Lcom/github/megatronking/stringfog/IStringFog;
.super Ljava/lang/Object;
.source "IStringFog.java"


# virtual methods
.method public abstract decrypt(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract encrypt(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract overflow(Ljava/lang/String;Ljava/lang/String;)Z
.end method
