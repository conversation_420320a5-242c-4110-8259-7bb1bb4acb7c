.class Lcom/projectvb/PMMInterface$1;
.super Ljava/lang/Object;
.source "PMMInterface.java"

# interfaces
.implements Landroid/view/View$OnClickListener;

# instance fields
.field final synthetic this$0:Lcom/projectvb/PMMInterface;

# direct methods
.method constructor <init>(Lcom/projectvb/PMMInterface;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/PMMInterface;

    .prologue
    .line 100
    iput-object p1, p0, Lcom/projectvb/PMMInterface$1;->this$0:Lcom/projectvb/PMMInterface;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 4
    .param p1, "v"    # Landroid/view/View;

    .prologue
    .line 103
    :try_start_0
    # Call the injection method from Menu class
    new-instance v0, Lcom/projectvb/Menu;
    iget-object v1, p0, Lcom/projectvb/PMMInterface$1;->this$0:Lcom/projectvb/PMMInterface;
    iget-object v1, v1, Lcom/projectvb/PMMInterface;->context:Landroid/content/Context;
    const/4 v2, 0x1
    invoke-direct {v0, v1, v2}, Lcom/projectvb/Menu;-><init>(Landroid/content/Context;I)V

    # Check if emulator
    invoke-static {}, Lcom/projectvb/Utils;->currArch()Ljava/lang/String;
    move-result-object v1
    const-string v2, "x86"
    invoke-virtual {v1, v2}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z
    move-result v1

    if-eqz v1, :cond_normal

    # Emulator injection
    const-string v1, "com.dts.freefireth"
    const-string v2, "libserver.so"
    invoke-virtual {v0, v1, v2}, Lcom/projectvb/Menu;->InjectX86(Ljava/lang/String;Ljava/lang/String;)Z
    move-result v1

    goto :goto_result

    :cond_normal
    # Normal injection
    invoke-virtual {v0}, Lcom/projectvb/Menu;->Inject()V
    const/4 v1, 0x1

    :goto_result
    if-eqz v1, :cond_failed

    # Success
    sget-object v0, Lcom/projectvb/PMMInterface;->Bypass:Landroid/widget/Button;
    const-string v1, "Injected"
    invoke-virtual {v0, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    goto :goto_end

    :cond_failed
    # Failed
    sget-object v0, Lcom/projectvb/PMMInterface;->Bypass:Landroid/widget/Button;
    const-string v1, "Failed"
    invoke-virtual {v0, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_end

    :catch_0
    move-exception v0

    # Exception occurred
    sget-object v1, Lcom/projectvb/PMMInterface;->Bypass:Landroid/widget/Button;
    const-string v2, "Error"
    invoke-virtual {v1, v2}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    :goto_end
    return-void
.end method
