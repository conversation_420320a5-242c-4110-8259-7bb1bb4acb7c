.class public final Lcom/bad/modder/injector/R$drawable;
.super Ljava/lang/Object;
.source "R.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x39
    name = "drawable"
.end annotation


# static fields
.field public static final theme_blue_ocean:I = 0x7f020000

.field public static final theme_classic:I = 0x7f020001


# direct methods
.method public constructor <init>()V
    .locals 3

    .prologue
    .line 20
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method
