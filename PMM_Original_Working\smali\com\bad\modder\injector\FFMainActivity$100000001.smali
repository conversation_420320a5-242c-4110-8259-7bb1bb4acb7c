.class Lcom/bad/modder/injector/FFMainActivity$100000001;
.super Ljava/lang/Object;
.source "FFMainActivity.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/FFMainActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000001"
.end annotation


# instance fields
.field private final val$context:Landroid/content/Context;


# direct methods
.method constructor <init>(Landroid/content/Context;)V
    .locals 5

    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    move-object v3, v0

    move-object v4, v1

    iput-object v4, v3, Lcom/bad/modder/injector/FFMainActivity$100000001;->val$context:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public run()V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    .line 73
    move-object v0, p0

    move-object v4, v0

    iget-object v4, v4, Lcom/bad/modder/injector/FFMainActivity$100000001;->val$context:Landroid/content/Context;

    new-instance v5, Landroid/content/Intent;

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    move-object v7, v0

    iget-object v7, v7, Lcom/bad/modder/injector/FFMainActivity$100000001;->val$context:Landroid/content/Context;

    :try_start_0
    const-string v8, "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"

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v8

    invoke-direct {v6, v7, v8}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    invoke-virtual {v4, v5}, Landroid/content/Context;->startService(Landroid/content/Intent;)Landroid/content/ComponentName;

    move-result-object v4

    return-void

    :catch_0
    move-exception v4

    move-object v2, v4

    new-instance v4, Ljava/lang/NoClassDefFoundError;

    move-object v12, v4

    move-object v4, v12

    move-object v5, v12

    move-object v6, v2

    invoke-virtual {v6}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v5, v6}, Ljava/lang/NoClassDefFoundError;-><init>(Ljava/lang/String;)V

    throw v4
.end method
