.class public interface abstract Lcom/projectvb/SwitchStyle$OnCheckedChangeListener;
.super Ljava/lang/Object;
.source "SwitchStyle.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/projectvb/SwitchStyle;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnCheckedChangeListener"
.end annotation


# virtual methods
.method public abstract onCheckedChanged(Lcom/projectvb/SwitchStyle;Z)V
.end method
