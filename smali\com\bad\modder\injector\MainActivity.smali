.class public Lcom/bad/modder/injector/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/bad/modder/injector/MainActivity$100000000;
    }
.end annotation


# static fields
.field public static FF:Z


# direct methods
.method static final constructor <clinit>()V
    .locals 3

    const/4 v2, 0x0

    sput-boolean v2, Lcom/bad/modder/injector/MainActivity;->FF:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .prologue
    .line 81
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Landroid/app/Activity;-><init>()V

    return-void
.end method

.method private CarregaLibInject(Landroid/content/Context;)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 53
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    const-string v12, "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"

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    move-object v3, v12

    .line 54
    const-string v12, "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"

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-static {v12}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    move-object v4, v12

    .line 56
    move-object v12, v3

    move-object v5, v12

    .line 57
    move-object v12, v4

    move-object v6, v12

    .line 61
    move-object v12, v0

    :try_start_0
    invoke-virtual {v12}, Lcom/bad/modder/injector/MainActivity;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v12

    move-object v13, v5

    invoke-virtual {v12, v13}, Landroid/content/res/AssetManager;->open(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object v12

    move-object v8, v12

    .line 63
    new-instance v12, Ljava/io/FileOutputStream;

    move-object/from16 v17, v12

    move-object/from16 v12, v17

    move-object/from16 v13, v17

    new-instance v14, Ljava/lang/StringBuffer;

    move-object/from16 v17, v14

    move-object/from16 v14, v17

    move-object/from16 v15, v17

    invoke-direct {v15}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v15, Ljava/lang/StringBuffer;

    move-object/from16 v17, v15

    move-object/from16 v15, v17

    move-object/from16 v16, v17

    invoke-direct/range {v16 .. v16}, Ljava/lang/StringBuffer;-><init>()V

    move-object/from16 v16, v6

    invoke-virtual/range {v15 .. v16}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v15

    const-string v16, "EBYfS2tnIChkez0/I1cIeh13TnEwPHcebDsRMX1wNjEkaUsHGR4cTxs+A0ZZACcOamphNxJVS34VdndiFBclYlkiJwNsawcCJXxpMBF1SXMSPAdFb2Q4KGJ7YD4XbwAWEhQZdRgWABxrDRkVfXkxDSRXU3o3K0kPDBBydVoiFRFoUAQOIn9pIBZ2Z0oULAN+WWQRAmtQbGETHlcMMANBVxA9dkFpZ2xzZ0ATOBNqeRYSERReFDwTaG4AOxRvUWRjHkoIegQrQUoXIzF5cDk3C2B5GxYQVXlsMC1rSA9mPldiZRUSYnobJSN/YQYRFH9xHRYfTnA4PwJkQA8mElR9BxEeFXEULHJ/ag07HmRCE2ElaVNmECt4dBEtH0FeZmQFZVBgYx5vajgWdRhRDwYTfmJkFSRnaxtsJXl9MwQoFXEQLAdVXQM/Ik96MQQjVQA8EQNCejA8A0h+DRETYXAPIidKcWIcK0UNNxw1RmkSHW1rayFgIm99IRx2a10PORdrWWQNdGNAYCInfnk0EQBBUREtchxeOBFta2sHOhd5fmIRAEkBGCwfYn4SHRdhbBMYJGlLAzcoHGgdLn51WhQVcmhWAwIFSm0mGRBBSBosA2xeDSdzSEExNSdHcQ0wAEFKEBMfaWkDPwJqUSE7I1R9IhUte1saFi15XmVkHmh5IREfem0HNwFjeRQsD0ddOz8CYHxgexFFfSAZHkFdGhYAWVkSLCFnQTE1EVdhAjAAe2kTLTF9YTtgF2tqAyYFVQAWHgFBdBQuJUJeImQfb2oxHCRKaQMyK3tPFy4TR2oDNxVrYAMWEXwABBoRa10RBhNKYhMRPmNrMRwlfnEHEXVZYBAQdgZwZztyfUADOCQfaTgRdBQKBSwTXGACMxNheQQwIFV2PhIoHA0SLQNLazsBAmVfEx4nf2khHhF7WRoufhxZOyQubGpgIiJ8cTYwE2tcEjwcVG8DOChnexM9I0d+PR0Db0AXLBNpajs7Imh8E20feUwyEhF7SR0ufnVeHQERZXAAAiV/aSAeE0VdHDkHS1oQFT5lewcVH0dxZhEeXmgdIwdlXWQ7E2tpHzMkank7Fg5afjQVexA="

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v16

    invoke-virtual/range {v15 .. v16}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v15

    invoke-virtual {v15}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    move-object v15, v5

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    invoke-virtual {v14}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-direct {v13, v14}, Ljava/io/FileOutputStream;-><init>(Ljava/lang/String;)V

    move-object v9, v12

    .line 64
    const/16 v12, 0x400

    new-array v12, v12, [B

    move-object v10, v12

    .line 65
    :goto_0
    move-object v12, v8

    move-object v13, v10

    invoke-virtual {v12, v13}, Ljava/io/InputStream;->read([B)I

    move-result v12

    move/from16 v17, v12

    move/from16 v12, v17

    move/from16 v13, v17

    move v7, v13

    const/4 v13, 0x0

    if-gt v12, v13, :cond_0

    .line 71
    move-object v12, v9

    invoke-virtual {v12}, Ljava/io/OutputStream;->flush()V

    .line 72
    move-object v12, v9

    invoke-virtual {v12}, Ljava/io/OutputStream;->close()V

    .line 73
    move-object v12, v8

    invoke-virtual {v12}, Ljava/io/InputStream;->close()V

    .line 79
    :goto_1
    return-void

    .line 67
    :cond_0
    move-object v12, v9

    move-object v13, v10

    const/4 v14, 0x0

    move v15, v7

    invoke-virtual {v12, v13, v14, v15}, Ljava/io/OutputStream;->write([BII)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 73
    :catch_0
    move-exception v12

    move-object v7, v12

    .line 77
    move-object v12, v1

    const-string v13, "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"

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v13

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 79
    const/4 v12, 0x1

    sput-boolean v12, Lcom/bad/modder/injector/MainActivity;->FF:Z

    goto :goto_1
.end method


# virtual methods
.method protected onCreate(Landroid/os/Bundle;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/os/Bundle;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Override;
    .end annotation

    .prologue
    move-object v0, p0

    move-object v1, p1

    move-object v5, v0

    const-string v6, "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"

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/github/megatronking/stringfog/xor/StringFogImpl;->decrypt(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v5, v6}, Ladrt/ADRTLogCatReader;->onContext(Landroid/content/Context;Ljava/lang/String;)V

    .line 20
    move-object v5, v0

    invoke-virtual {v5}, Lcom/bad/modder/injector/MainActivity;->getActionBar()Landroid/app/ActionBar;

    move-result-object v5

    invoke-virtual {v5}, Landroid/app/ActionBar;->hide()V

    .line 21
    move-object v5, v0

    invoke-virtual {v5}, Lcom/bad/modder/injector/MainActivity;->getWindow()Landroid/view/Window;

    move-result-object v5

    move-object v3, v5

    .line 22
    move-object v5, v3

    const/high16 v6, -0x80000000

    invoke-virtual {v5, v6}, Landroid/view/Window;->addFlags(I)V

    .line 23
    move-object v5, v3

    const/high16 v6, 0x4000000

    invoke-virtual {v5, v6}, Landroid/view/Window;->clearFlags(I)V

    .line 24
    move-object v5, v3

    move-object v6, v0

    const/high16 v7, 0x7f040000

    invoke-virtual {v6, v7}, Lcom/bad/modder/injector/MainActivity;->getColor(I)I

    move-result v6

    invoke-virtual {v5, v6}, Landroid/view/Window;->setStatusBarColor(I)V

    .line 25
    move-object v5, v3

    move-object v6, v0

    const/high16 v7, 0x7f040000

    invoke-virtual {v6, v7}, Lcom/bad/modder/injector/MainActivity;->getColor(I)I

    move-result v6

    invoke-virtual {v5, v6}, Landroid/view/Window;->setNavigationBarColor(I)V

    .line 26
    move-object v5, v0

    invoke-static {v5}, Lcom/bad/modder/injector/FFMainActivity;->Start(Landroid/content/Context;)V

    .line 27
    new-instance v5, Lcom/bad/modder/injector/MainActivity$100000000;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Lcom/bad/modder/injector/MainActivity$100000000;-><init>(Lcom/bad/modder/injector/MainActivity;)V

    invoke-static {v5}, Ljava/lang/Thread;->setDefaultUncaughtExceptionHandler(Ljava/lang/Thread$UncaughtExceptionHandler;)V

    .line 43
    move-object v5, v0

    move-object v6, v1

    invoke-super {v5, v6}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 44
    sget-boolean v5, Lcom/bad/modder/injector/MainActivity;->FF:Z

    if-eqz v5, :cond_0

    .line 45
    move-object v5, v0

    invoke-virtual {v5}, Lcom/bad/modder/injector/MainActivity;->finishAffinity()V

    .line 47
    :cond_0
    move-object v5, v0

    invoke-static {v5}, Lcom/bad/modder/injector/LoaderBad;->Start(Landroid/content/Context;)V

    .line 48
    move-object v5, v0

    move-object v6, v0

    invoke-direct {v5, v6}, Lcom/bad/modder/injector/MainActivity;->CarregaLibInject(Landroid/content/Context;)V

    return-void
.end method
