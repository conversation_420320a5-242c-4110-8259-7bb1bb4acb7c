.class public interface abstract Leu/chainfire/libsuperuser/Shell$OnResult;
.super Ljava/lang/Object;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnResult"
.end annotation


# static fields
.field public static final SHELL_DIED:I = -0x2

.field public static final SHELL_EXEC_FAILED:I = -0x3

.field public static final SHELL_RUNNING:I = 0x0

.field public static final SHELL_WRONG_UID:I = -0x4

.field public static final WATCHDOG_EXIT:I = -0x1
