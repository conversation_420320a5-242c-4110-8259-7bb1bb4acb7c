.class final Leu/chainfire/libsuperuser/Shell$Pool$1;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Leu/chainfire/libsuperuser/Shell$Pool$OnNewBuilderListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/Shell$Pool;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# direct methods
.method constructor <init>()V
    .locals 2

    .prologue
    .line 3196
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public newBuilder()Leu/chainfire/libsuperuser/Shell$Builder;
    .locals 4
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .prologue
    .line 3201
    move-object v0, p0

    new-instance v1, Leu/chainfire/libsuperuser/Shell$Builder;

    move-object v3, v1

    move-object v1, v3

    move-object v2, v3

    invoke-direct {v2}, Leu/chainfire/libsuperuser/Shell$Builder;-><init>()V

    const/4 v2, 0x1

    .line 3202
    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Builder;->setWantSTDERR(Z)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v1

    const/4 v2, 0x0

    .line 3203
    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Builder;->setWatchdogTimeout(I)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v1

    const/4 v2, 0x0

    .line 3204
    invoke-virtual {v1, v2}, Leu/chainfire/libsuperuser/Shell$Builder;->setMinimalLogging(Z)Leu/chainfire/libsuperuser/Shell$Builder;

    move-result-object v1

    .line 3201
    move-object v0, v1

    return-object v0
.end method
