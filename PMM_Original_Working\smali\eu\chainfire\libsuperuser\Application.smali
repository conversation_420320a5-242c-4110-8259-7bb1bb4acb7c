.class public Leu/chainfire/libsuperuser/Application;
.super Landroid/app/Application;
.source "Application.java"


# static fields
.field private static final mApplicationHandler:Landroid/os/Handler;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .prologue
    .line 63
    new-instance v0, Landroid/os/Handler;

    move-object v2, v0

    move-object v0, v2

    move-object v1, v2

    invoke-direct {v1}, Landroid/os/Handler;-><init>()V

    sput-object v0, Leu/chainfire/libsuperuser/Application;->mApplicationHandler:Landroid/os/Handler;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .prologue
    .line 32
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Landroid/app/Application;-><init>()V

    return-void
.end method

.method public static toast(Landroid/content/Context;Ljava/lang/String;)V
    .locals 10
    .param p0    # Landroid/content/Context;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 44
    move-object v0, p0

    move-object v1, p1

    move-object v4, v0

    if-nez v4, :cond_0

    .line 61
    :goto_0
    return-void

    .line 46
    :cond_0
    move-object v4, v0

    instance-of v4, v4, Leu/chainfire/libsuperuser/Application;

    if-nez v4, :cond_1

    .line 47
    move-object v4, v0

    invoke-virtual {v4}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v4

    move-object v0, v4

    .line 50
    :cond_1
    move-object v4, v0

    instance-of v4, v4, Leu/chainfire/libsuperuser/Application;

    if-eqz v4, :cond_2

    .line 51
    move-object v4, v0

    move-object v2, v4

    .line 52
    move-object v4, v1

    move-object v3, v4

    .line 54
    move-object v4, v0

    check-cast v4, Leu/chainfire/libsuperuser/Application;

    new-instance v5, Leu/chainfire/libsuperuser/Application$1;

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    move-object v7, v2

    move-object v8, v3

    invoke-direct {v6, v7, v8}, Leu/chainfire/libsuperuser/Application$1;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    invoke-virtual {v4, v5}, Leu/chainfire/libsuperuser/Application;->runInApplicationThread(Ljava/lang/Runnable;)V

    .line 61
    :cond_2
    goto :goto_0
.end method


# virtual methods
.method public onCreate()V
    .locals 3

    .prologue
    .line 77
    move-object v0, p0

    move-object v2, v0

    invoke-super {v2}, Landroid/app/Application;->onCreate()V

    .line 82
    :try_start_0
    const-string v2, "android.os.AsyncTask"

    invoke-static {v2}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v2

    .line 86
    :goto_0
    return-void

    .line 83
    :catch_0
    move-exception v2

    move-object v1, v2

    goto :goto_0
.end method

.method public runInApplicationThread(Ljava/lang/Runnable;)V
    .locals 4
    .param p1    # Ljava/lang/Runnable;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/AnyThread;
    .end annotation

    .prologue
    .line 72
    move-object v0, p0

    move-object v1, p1

    sget-object v2, Leu/chainfire/libsuperuser/Application;->mApplicationHandler:Landroid/os/Handler;

    move-object v3, v1

    invoke-virtual {v2, v3}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v2

    .line 73
    return-void
.end method
