<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="30" android:compileSdkVersionCodename="11" package="pmm.by.p2077kng.hacker" platformBuildVersionCode="30" platformBuildVersionName="11">
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <application android:allowBackup="true" android:debuggable="true" android:icon="@drawable/RiverMod" android:label="PMM Injector" android:resizeableActivity="true" android:theme="@style/AppTheme">
        <activity android:label="PMM Injector" android:name="pmm.by.p2077kng.hacker.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <meta-data android:name="android.max_aspect" android:value="7.0"/>
        <service android:enabled="true" android:exported="true" android:name="pmm.by.p2077kng.hacker.Loader" android:stopWithTask="true"/>
    </application>
</manifest>
