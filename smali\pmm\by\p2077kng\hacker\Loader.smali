.class public Lpmm/by/p2077kng/hacker/Loader;
.super Landroid/app/Service;
.source "Loader.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lpmm/by/p2077kng/hacker/Loader$100000002;,
        Lpmm/by/p2077kng/hacker/Loader$100000003;,
        Lpmm/by/p2077kng/hacker/Loader$100000004;,
        Lpmm/by/p2077kng/hacker/Loader$100000005;,
        Lpmm/by/p2077kng/hacker/Loader$100000006;,
        Lpmm/by/p2077kng/hacker/Loader$100000007;,
        Lpmm/by/p2077kng/hacker/Loader$100000008;,
        Lpmm/by/p2077kng/hacker/Loader$100000009;,
        Lpmm/by/p2077kng/hacker/Loader$100000010;,
        Lpmm/by/p2077kng/hacker/Loader$100000011;,
        Lpmm/by/p2077kng/hacker/Loader$100000012;,
        Lpmm/by/p2077kng/hacker/Loader$100000013;,
        Lpmm/by/p2077kng/hacker/Loader$100000014;,
        Lpmm/by/p2077kng/hacker/Loader$100000015;,
        Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;,
        Lpmm/by/p2077kng/hacker/Loader$100000016;,
        Lpmm/by/p2077kng/hacker/Loader$100000017;,
        Lpmm/by/p2077kng/hacker/Loader$100000018;,
        Lpmm/by/p2077kng/hacker/Loader$100000019;,
        Lpmm/by/p2077kng/hacker/Loader$100000020;,
        Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;,
        Lpmm/by/p2077kng/hacker/Loader$100000021;,
        Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;,
        Lpmm/by/p2077kng/hacker/Loader$100000022;,
        Lpmm/by/p2077kng/hacker/Loader$100000023;,
        Lpmm/by/p2077kng/hacker/Loader$100000024;,
        Lpmm/by/p2077kng/hacker/Loader$100000025;,
        Lpmm/by/p2077kng/hacker/Loader$100000026;,
        Lpmm/by/p2077kng/hacker/Loader$100000027;,
        Lpmm/by/p2077kng/hacker/Loader$BTN;,
        Lpmm/by/p2077kng/hacker/Loader$InterfaceStr;
    }
.end annotation


# static fields
.field private static Bypass:Landroid/widget/Button;

.field public static Lin1:Landroid/widget/LinearLayout;

.field public static Lin2:Landroid/widget/LinearLayout;

.field public static Lin3:Landroid/widget/LinearLayout;

.field public static TITLE:Ljava/lang/String;

.field private static logs:Landroid/widget/Button;

.field static str:Ljava/lang/String;

.field private static username:Landroid/widget/TextView;


# instance fields
.field public Status:Landroid/widget/TextView;

.field public StatusOk:Z

.field private StatusText:Landroid/widget/TextView;

.field private TTP2077KNG:Landroid/widget/LinearLayout;

.field private alert:Landroid/app/AlertDialog;

.field canvasLayoutParams:Landroid/view/WindowManager$LayoutParams;

.field private close:Landroid/widget/Button;

.field public collapse_view:Landroid/widget/RelativeLayout;

.field private edittextvalue:Landroid/widget/EditText;

.field private espParams:Landroid/view/WindowManager$LayoutParams;

.field private ffid:Landroid/widget/ImageView;

.field public frameLayout:Landroid/widget/FrameLayout;

.field private mButtonPanel:Landroid/widget/LinearLayout;

.field public mCollapsed:Landroid/widget/RelativeLayout;

.field public mEspWindowManager:Landroid/view/WindowManager;

.field public mExpanded:Landroid/widget/LinearLayout;

.field public mFloatingView:Landroid/view/View;

.field private mRootContainer:Landroid/widget/RelativeLayout;

.field private mViewImage3:Landroid/widget/ImageView;

.field public mWindowManager:Landroid/view/WindowManager;

.field menuparams:Landroid/view/WindowManager$LayoutParams;

.field private overlayView:Lpmm/by/p2077kng/hacker/ESPView;

.field public params:Landroid/view/WindowManager$LayoutParams;

.field private patches:Landroid/widget/LinearLayout;

.field private rootFrame:Landroid/widget/FrameLayout;

.field scrlLL:Landroid/widget/LinearLayout$LayoutParams;

.field scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

.field private scrollView:Landroid/widget/ScrollView;

.field private startimage:Landroid/widget/ImageView;

.field private startimage2:Landroid/widget/ImageView;

.field private view1:Landroid/widget/LinearLayout;

.field private view2:Landroid/widget/LinearLayout;

.field public windowManager:Landroid/view/WindowManager;


# direct methods
.method static final constructor <clinit>()V
    .locals 3

    .prologue
    .line 98
    const-string v2, "<font face=>PK<font>"

    sput-object v2, Lpmm/by/p2077kng/hacker/Loader;->TITLE:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .prologue
    .line 1335
    move-object v0, p0

    move-object v2, v0

    invoke-direct {v2}, Landroid/app/Service;-><init>()V

    return-void
.end method

.method private CreateMenuList()V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 694
    move-object/from16 v0, p0

    move-object v8, v0

    invoke-direct {v8}, Lpmm/by/p2077kng/hacker/Loader;->MenuPro()[Ljava/lang/String;

    move-result-object v8

    move-object v2, v8

    .line 695
    const/4 v8, 0x0

    move v3, v8

    :goto_0
    move v8, v3

    move-object v9, v2

    array-length v9, v9

    if-lt v8, v9, :cond_0

    return-void

    .line 696
    :cond_0
    move v8, v3

    move v4, v8

    .line 697
    move-object v8, v2

    move v9, v3

    aget-object v8, v8, v9

    move-object v5, v8

    .line 698
    move-object v8, v5

    const-string v9, "Toggle_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_2

    .line 699
    move-object v8, v0

    move-object v9, v5

    const-string v10, "Toggle_"

    const-string v11, ""

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v9

    new-instance v10, Lpmm/by/p2077kng/hacker/Loader$100000008;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v0

    move v13, v4

    invoke-direct {v11, v12, v13}, Lpmm/by/p2077kng/hacker/Loader$100000008;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-direct {v8, v9, v10}, Lpmm/by/p2077kng/hacker/Loader;->addSwitch(Ljava/lang/String;Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;)V

    .line 695
    :cond_1
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 704
    :cond_2
    move-object v8, v5

    const-string v9, "SeekBar_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_3

    .line 705
    move-object v8, v5

    const-string v9, "_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v8

    move-object v6, v8

    .line 706
    move-object v8, v0

    move-object v9, v6

    const/4 v10, 0x1

    aget-object v9, v9, v10

    move-object v10, v6

    const/4 v11, 0x2

    aget-object v10, v10, v11

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move-object v11, v6

    const/4 v12, 0x3

    aget-object v11, v11, v12

    invoke-static {v11}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v11

    new-instance v12, Lpmm/by/p2077kng/hacker/Loader$100000009;

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    move-object v14, v0

    move v15, v4

    invoke-direct {v13, v14, v15}, Lpmm/by/p2077kng/hacker/Loader$100000009;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-direct {v8, v9, v10, v11, v12}, Lpmm/by/p2077kng/hacker/Loader;->addSeekBar(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V

    goto :goto_1

    .line 711
    :cond_3
    move-object v8, v5

    const-string v9, "SeekBar2_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_4

    .line 712
    move-object v8, v5

    const-string v9, "_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v8

    move-object v6, v8

    .line 713
    move-object v8, v0

    move-object v9, v6

    const/4 v10, 0x1

    aget-object v9, v9, v10

    move-object v10, v6

    const/4 v11, 0x2

    aget-object v10, v10, v11

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move-object v11, v6

    const/4 v12, 0x3

    aget-object v11, v11, v12

    invoke-static {v11}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v11

    new-instance v12, Lpmm/by/p2077kng/hacker/Loader$100000010;

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    move-object v14, v0

    move v15, v4

    invoke-direct {v13, v14, v15}, Lpmm/by/p2077kng/hacker/Loader$100000010;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-direct {v8, v9, v10, v11, v12}, Lpmm/by/p2077kng/hacker/Loader;->addSeekBar2(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V

    goto :goto_1

    .line 718
    :cond_4
    move-object v8, v5

    const-string v9, "SeekSpot_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_5

    .line 719
    move-object v8, v5

    const-string v9, "_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v8

    move-object v6, v8

    .line 720
    move-object v8, v0

    move-object v9, v6

    const/4 v10, 0x1

    aget-object v9, v9, v10

    move-object v10, v6

    const/4 v11, 0x2

    aget-object v10, v10, v11

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move-object v11, v6

    const/4 v12, 0x3

    aget-object v11, v11, v12

    invoke-static {v11}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v11

    new-instance v12, Lpmm/by/p2077kng/hacker/Loader$100000011;

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    move-object v14, v0

    move v15, v4

    invoke-direct {v13, v14, v15}, Lpmm/by/p2077kng/hacker/Loader$100000011;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-direct {v8, v9, v10, v11, v12}, Lpmm/by/p2077kng/hacker/Loader;->addSeekBarSpot(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V

    goto/16 :goto_1

    .line 725
    :cond_5
    move-object v8, v5

    const-string v9, "SeekFov_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_6

    .line 726
    move-object v8, v5

    const-string v9, "_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v8

    move-object v6, v8

    .line 727
    move-object v8, v0

    move-object v9, v6

    const/4 v10, 0x1

    aget-object v9, v9, v10

    move-object v10, v6

    const/4 v11, 0x2

    aget-object v10, v10, v11

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move-object v11, v6

    const/4 v12, 0x3

    aget-object v11, v11, v12

    invoke-static {v11}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v11

    new-instance v12, Lpmm/by/p2077kng/hacker/Loader$100000012;

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    move-object v14, v0

    move v15, v4

    invoke-direct {v13, v14, v15}, Lpmm/by/p2077kng/hacker/Loader$100000012;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-direct {v8, v9, v10, v11, v12}, Lpmm/by/p2077kng/hacker/Loader;->addSeekBarFov(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V

    goto/16 :goto_1

    .line 732
    :cond_6
    move-object v8, v5

    const-string v9, "SeekColor_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_7

    .line 733
    move-object v8, v5

    const-string v9, "_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v8

    move-object v6, v8

    .line 734
    move-object v8, v0

    move-object v9, v6

    const/4 v10, 0x1

    aget-object v9, v9, v10

    move-object v10, v6

    const/4 v11, 0x2

    aget-object v10, v10, v11

    invoke-static {v10}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v10

    move-object v11, v6

    const/4 v12, 0x3

    aget-object v11, v11, v12

    invoke-static {v11}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v11

    new-instance v12, Lpmm/by/p2077kng/hacker/Loader$100000013;

    move-object/from16 v16, v12

    move-object/from16 v12, v16

    move-object/from16 v13, v16

    move-object v14, v0

    move v15, v4

    invoke-direct {v13, v14, v15}, Lpmm/by/p2077kng/hacker/Loader$100000013;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-direct {v8, v9, v10, v11, v12}, Lpmm/by/p2077kng/hacker/Loader;->addSeekBarColor(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V

    goto/16 :goto_1

    .line 740
    :cond_7
    move-object v8, v5

    const-string v9, "Category_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_8

    .line 741
    move-object v8, v0

    move-object v9, v5

    const-string v10, "Category_"

    const-string v11, ""

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v9

    invoke-direct {v8, v9}, Lpmm/by/p2077kng/hacker/Loader;->addCategory(Ljava/lang/String;)V

    goto/16 :goto_1

    .line 743
    :cond_8
    move-object v8, v5

    const-string v9, "Button_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_9

    .line 744
    move-object v8, v0

    move-object v9, v5

    const-string v10, "Button_"

    const-string v11, ""

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v9

    new-instance v10, Lpmm/by/p2077kng/hacker/Loader$100000014;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v0

    move v13, v4

    invoke-direct {v11, v12, v13}, Lpmm/by/p2077kng/hacker/Loader$100000014;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-virtual {v8, v9, v10}, Lpmm/by/p2077kng/hacker/Loader;->addButton(Ljava/lang/String;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V

    goto/16 :goto_1

    .line 750
    :cond_9
    move-object v8, v5

    const-string v9, "PkPmm_"

    invoke-virtual {v8, v9}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v8

    if-eqz v8, :cond_1

    .line 751
    move-object v8, v0

    move-object v9, v5

    const-string v10, "PkPmm_"

    const-string v11, ""

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v9

    new-instance v10, Lpmm/by/p2077kng/hacker/Loader$100000015;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v0

    move v13, v4

    invoke-direct {v11, v12, v13}, Lpmm/by/p2077kng/hacker/Loader$100000015;-><init>(Lpmm/by/p2077kng/hacker/Loader;I)V

    invoke-virtual {v8, v9, v10}, Lpmm/by/p2077kng/hacker/Loader;->addButtonpmm(Ljava/lang/String;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V

    goto/16 :goto_1
.end method

.method public static CustomToast(Ljava/lang/String;Landroid/content/Context;)V
    .locals 20
    .annotation runtime Landroid/annotation/TargetApi;
        value = 0x15
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/content/Context;",
            ")V"
        }
    .end annotation

    .prologue
    .line 154
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    new-instance v10, Landroid/widget/LinearLayout;

    move-object/from16 v19, v10

    move-object/from16 v10, v19

    move-object/from16 v11, v19

    move-object v12, v2

    invoke-direct {v11, v12}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object v4, v10

    .line 155
    move-object v10, v4

    new-instance v11, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, -0x1

    const/4 v14, -0x1

    invoke-direct {v12, v13, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v11}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 156
    move-object v10, v4

    const/16 v11, 0xc

    move-object v12, v2

    invoke-static {v11, v12}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v11

    const/16 v12, 0xc

    move-object v13, v2

    invoke-static {v12, v13}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v12

    const/16 v13, 0xc

    move-object v14, v2

    invoke-static {v13, v14}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v13

    const/16 v14, 0xc

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    invoke-virtual {v10, v11, v12, v13, v14}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 157
    move-object v10, v4

    const v11, 0x3f666666    # 0.9f

    invoke-virtual {v10, v11}, Landroid/widget/LinearLayout;->setAlpha(F)V

    .line 159
    new-instance v10, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v19, v10

    move-object/from16 v10, v19

    move-object/from16 v11, v19

    invoke-direct {v11}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v5, v10

    .line 160
    move-object v10, v5

    const-string v11, "#FFFFFF"

    invoke-static {v11}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v11

    invoke-virtual {v10, v11}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 161
    move-object v10, v5

    const/16 v11, 0x8

    new-array v11, v11, [F

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x0

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x1

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x2

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x3

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x4

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x5

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x6

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, 0x7

    const/4 v14, 0x5

    move-object v15, v2

    invoke-static {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(ILandroid/content/Context;)I

    move-result v14

    int-to-float v14, v14

    aput v14, v12, v13

    invoke-virtual {v10, v11}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadii([F)V

    .line 163
    new-instance v10, Landroid/graphics/drawable/RippleDrawable;

    move-object/from16 v19, v10

    move-object/from16 v10, v19

    move-object/from16 v11, v19

    new-instance v12, Landroid/content/res/ColorStateList;

    move-object/from16 v19, v12

    move-object/from16 v12, v19

    move-object/from16 v13, v19

    const/4 v14, 0x1

    new-array v14, v14, [[I

    move-object/from16 v19, v14

    move-object/from16 v14, v19

    move-object/from16 v15, v19

    const/16 v16, 0x0

    const/16 v17, 0x0

    move/from16 v0, v17

    new-array v0, v0, [I

    move-object/from16 v17, v0

    aput-object v17, v15, v16

    const/4 v15, 0x1

    new-array v15, v15, [I

    move-object/from16 v19, v15

    move-object/from16 v15, v19

    move-object/from16 v16, v19

    const/16 v17, 0x0

    const/16 v18, -0x1

    aput v18, v16, v17

    invoke-direct {v13, v14, v15}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    move-object v13, v5

    const/4 v14, 0x0

    check-cast v14, Landroid/graphics/drawable/Drawable;

    invoke-direct {v11, v12, v13, v14}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v6, v10

    .line 164
    move-object v10, v4

    move-object v11, v6

    invoke-virtual {v10, v11}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 167
    new-instance v10, Landroid/widget/TextView;

    move-object/from16 v19, v10

    move-object/from16 v10, v19

    move-object/from16 v11, v19

    move-object v12, v2

    invoke-direct {v11, v12}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v7, v10

    .line 168
    move-object v10, v7

    new-instance v11, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v19, v11

    move-object/from16 v11, v19

    move-object/from16 v12, v19

    const/4 v13, -0x2

    const/4 v14, -0x1

    invoke-direct {v12, v13, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v11}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 169
    move-object v10, v7

    move-object v11, v1

    invoke-virtual {v10, v11}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 170
    move-object v10, v7

    const-string v11, "#000000"

    invoke-static {v11}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v11

    invoke-virtual {v10, v11}, Landroid/widget/TextView;->setTextColor(I)V

    .line 172
    move-object v10, v4

    move-object v11, v7

    invoke-virtual {v10, v11}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 174
    new-instance v10, Landroid/widget/Toast;

    move-object/from16 v19, v10

    move-object/from16 v10, v19

    move-object/from16 v11, v19

    move-object v12, v2

    invoke-direct {v11, v12}, Landroid/widget/Toast;-><init>(Landroid/content/Context;)V

    move-object v8, v10

    .line 175
    move-object v10, v8

    const/4 v11, 0x1

    invoke-virtual {v10, v11}, Landroid/widget/Toast;->setDuration(I)V

    .line 176
    move-object v10, v8

    move-object v11, v4

    invoke-virtual {v10, v11}, Landroid/widget/Toast;->setView(Landroid/view/View;)V

    .line 177
    move-object v10, v8

    invoke-virtual {v10}, Landroid/widget/Toast;->show()V

    return-void
.end method

.method private DrawCanvas()V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 231
    move-object v0, p0

    move-object v4, v0

    new-instance v5, Landroid/view/WindowManager$LayoutParams;

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    const/4 v7, -0x1

    const/4 v8, -0x1

    move-object v9, v0

    invoke-direct {v9}, Lpmm/by/p2077kng/hacker/Loader;->getLayoutType()I

    move-result v9

    const/16 v10, 0x38

    const/4 v11, -0x3

    invoke-direct/range {v6 .. v11}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    move-object v12, v5

    move-object v5, v12

    move-object v6, v12

    move-object v2, v6

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Loader;->espParams:Landroid/view/WindowManager$LayoutParams;

    .line 233
    move-object v4, v2

    const v5, 0x800033

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 234
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->espParams:Landroid/view/WindowManager$LayoutParams;

    const/4 v5, -0x5

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 235
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->espParams:Landroid/view/WindowManager$LayoutParams;

    const/4 v5, -0x5

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 236
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader;->overlayView:Lpmm/by/p2077kng/hacker/ESPView;

    check-cast v5, Landroid/view/View;

    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/Loader;->espParams:Landroid/view/WindowManager$LayoutParams;

    check-cast v6, Landroid/view/ViewGroup$LayoutParams;

    invoke-interface {v4, v5, v6}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method public static native DrawOn(Lpmm/by/p2077kng/hacker/ESPView;Landroid/graphics/Canvas;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lpmm/by/p2077kng/hacker/ESPView;",
            "Landroid/graphics/Canvas;",
            ")V"
        }
    .end annotation
.end method

.method private FlutuanteService()V
    .locals 33
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 467
    move-object/from16 v2, p0

    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/FrameLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->rootFrame:Landroid/widget/FrameLayout;

    .line 468
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/RelativeLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mRootContainer:Landroid/widget/RelativeLayout;

    .line 469
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/RelativeLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    .line 470
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/LinearLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    .line 471
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/LinearLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->view1:Landroid/widget/LinearLayout;

    .line 472
    new-instance v24, Landroid/widget/ImageView;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-virtual/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v26

    invoke-direct/range {v25 .. v26}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    move-object/from16 v4, v24

    .line 473
    move-object/from16 v24, v2

    move-object/from16 v25, v4

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    .line 474
    move-object/from16 v24, v2

    invoke-virtual/range {v24 .. v24}, Lpmm/by/p2077kng/hacker/Loader;->getAssets()Landroid/content/res/AssetManager;

    move-result-object v24

    move-object/from16 v5, v24

    .line 475
    move-object/from16 v24, v4

    new-instance v25, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 476
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/LinearLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    .line 477
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/LinearLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->view2:Landroid/widget/LinearLayout;

    .line 478
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/LinearLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mButtonPanel:Landroid/widget/LinearLayout;

    .line 480
    new-instance v24, Landroid/widget/RelativeLayout;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-direct/range {v25 .. v26}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v6, v24

    .line 481
    move-object/from16 v24, v6

    new-instance v25, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x1

    invoke-direct/range {v26 .. v28}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 482
    move-object/from16 v24, v6

    const/16 v25, 0x10

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->setVerticalGravity(I)V

    .line 484
    new-instance v24, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    invoke-direct/range {v25 .. v25}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object/from16 v7, v24

    .line 485
    move-object/from16 v24, v2

    move-object/from16 v25, v7

    const/high16 v26, 0x40400000    # 3.0f

    invoke-direct/range {v24 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->setCornerRadius(Landroid/graphics/drawable/GradientDrawable;F)V

    .line 486
    move-object/from16 v24, v7

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->colourOne()Ljava/lang/String;

    move-result-object v25

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 488
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/Button;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-direct/range {v26 .. v27}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    .line 489
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    move-object/from16 v28, v2

    const/16 v29, 0x21

    invoke-direct/range {v28 .. v29}, Lpmm/by/p2077kng/hacker/Loader;->m6dp(I)I

    move-result v28

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 490
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    const/16 v25, 0x2

    const/high16 v26, 0x41200000    # 10.0f

    invoke-virtual/range {v24 .. v26}, Landroid/widget/Button;->setTextSize(IF)V

    .line 491
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    const/16 v25, 0x0

    check-cast v25, Landroid/graphics/Typeface;

    const/16 v26, 0x1

    invoke-virtual/range {v24 .. v26}, Landroid/widget/Button;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 492
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->colourOne()Ljava/lang/String;

    move-result-object v25

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 493
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    const/16 v25, -0x1

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setTextColor(I)V

    .line 494
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    move-object/from16 v25, v7

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 495
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v24, v0

    const-string v25, "Close"

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 497
    new-instance v24, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    const/16 v26, -0x2

    move-object/from16 v27, v2

    const/16 v28, 0x21

    invoke-direct/range {v27 .. v28}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v27

    invoke-direct/range {v25 .. v27}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    move-object/from16 v8, v24

    .line 498
    move-object/from16 v24, v8

    const/16 v25, 0xb

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 500
    new-instance v24, Landroid/widget/Button;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-direct/range {v25 .. v26}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    sput-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    .line 501
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    const/16 v25, 0x2

    const/high16 v26, 0x41200000    # 10.0f

    invoke-virtual/range {v24 .. v26}, Landroid/widget/Button;->setTextSize(IF)V

    .line 502
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    const/16 v25, 0x0

    check-cast v25, Landroid/graphics/Typeface;

    const/16 v26, 0x1

    invoke-virtual/range {v24 .. v26}, Landroid/widget/Button;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 503
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->colourOne()Ljava/lang/String;

    move-result-object v25

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 504
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    const/16 v25, -0x1

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setTextColor(I)V

    .line 505
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    const-string v25, "Logs"

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 506
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    move-object/from16 v25, v8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 507
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    move-object/from16 v25, v7

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 508
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    const/16 v25, 0x8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setVisibility(I)V

    .line 510
    new-instance v24, Landroid/widget/Button;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-direct/range {v25 .. v26}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    sput-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    .line 511
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const/16 v25, 0x2

    const/high16 v26, 0x41200000    # 10.0f

    invoke-virtual/range {v24 .. v26}, Landroid/widget/Button;->setTextSize(IF)V

    .line 512
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const/16 v25, 0x0

    check-cast v25, Landroid/graphics/Typeface;

    const/16 v26, 0x1

    invoke-virtual/range {v24 .. v26}, Landroid/widget/Button;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 513
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->colourOne()Ljava/lang/String;

    move-result-object v25

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 514
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const/16 v25, -0x1

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setTextColor(I)V

    .line 515
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const-string v25, "Inject"

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 516
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    move-object/from16 v25, v7

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 517
    sget-object v24, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    move-object/from16 v25, v8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 519
    move-object/from16 v24, v6

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 520
    move-object/from16 v24, v6

    sget-object v25, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 521
    move-object/from16 v24, v6

    sget-object v25, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 523
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->rootFrame:Landroid/widget/FrameLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/FrameLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    const/16 v28, -0x1

    invoke-direct/range {v26 .. v28}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/FrameLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 524
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mRootContainer:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/FrameLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 525
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 526
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 527
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/LinearLayout;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-direct/range {v26 .. v27}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    .line 528
    new-instance v24, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    invoke-direct/range {v25 .. v25}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object/from16 v9, v24

    .line 529
    move-object/from16 v24, v9

    const-string v25, "#B30E0E0E"

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 530
    move-object/from16 v24, v2

    move-object/from16 v25, v9

    const/high16 v26, 0x41000000    # 8.0f

    invoke-direct/range {v24 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->setCornerRadius(Landroid/graphics/drawable/GradientDrawable;F)V

    .line 531
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 532
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x11

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 533
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x1

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 534
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v9

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 535
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    const/16 v28, 0xc8

    invoke-direct/range {v27 .. v28}, Lpmm/by/p2077kng/hacker/Loader;->m6dp(I)I

    move-result v27

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 537
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    const/16 v28, -0x1

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 538
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x7

    const/16 v26, 0x2

    const/16 v27, 0x7

    const/16 v28, 0x2

    invoke-virtual/range {v24 .. v28}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 539
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x1

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 541
    new-instance v24, Landroid/widget/ScrollView;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-virtual/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v26

    invoke-direct/range {v25 .. v26}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v24

    .line 542
    move-object/from16 v24, v10

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    move-object/from16 v28, v2

    const/16 v29, 0x98

    invoke-direct/range {v28 .. v29}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v28

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ScrollView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 544
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->view1:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    const/16 v28, 0x0

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 545
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->view1:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 546
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    const/16 v28, -0x1

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 547
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x1

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 548
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->view2:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    const/16 v28, 0x0

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 549
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->view2:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 550
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->view2:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    const/16 v25, 0x0

    const/16 v26, 0x0

    const/16 v27, 0x0

    const/16 v28, 0xa

    invoke-virtual/range {v24 .. v28}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 551
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mButtonPanel:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 553
    new-instance v24, Landroid/widget/LinearLayout;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-direct/range {v25 .. v26}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v24

    .line 554
    move-object/from16 v24, v11

    new-instance v25, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x1

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 555
    move-object/from16 v24, v11

    const/16 v25, 0x0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 556
    move-object/from16 v24, v11

    move-object/from16 v25, v2

    const/16 v26, 0x5

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v25

    move-object/from16 v26, v2

    const/16 v27, 0x5

    invoke-direct/range {v26 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v26

    move-object/from16 v27, v2

    const/16 v28, 0x5

    invoke-direct/range {v27 .. v28}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v27

    move-object/from16 v28, v2

    const/16 v29, 0x5

    invoke-direct/range {v28 .. v29}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v28

    invoke-virtual/range {v24 .. v28}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 558
    move-object/from16 v24, v2

    invoke-direct/range {v24 .. v24}, Lpmm/by/p2077kng/hacker/Loader;->Icon()Ljava/lang/String;

    move-result-object v24

    const/16 v25, 0x0

    invoke-static/range {v24 .. v25}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v24

    move-object/from16 v12, v24

    .line 560
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/ImageView;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    .line 561
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 562
    const/16 v24, 0x1

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->IconSize()I

    move-result v25

    move/from16 v0, v25

    int-to-float v0, v0

    move/from16 v25, v0

    move-object/from16 v26, v2

    invoke-virtual/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->getResources()Landroid/content/res/Resources;

    move-result-object v26

    invoke-virtual/range {v26 .. v26}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v26

    invoke-static/range {v24 .. v26}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v24

    move/from16 v0, v24

    float-to-int v0, v0

    move/from16 v24, v0

    move/from16 v13, v24

    .line 563
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    move/from16 v25, v13

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 564
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    move/from16 v25, v13

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 565
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->requestLayout()V

    .line 566
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    sget-object v25, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 567
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    move-object/from16 v25, v12

    const/16 v26, 0x0

    move-object/from16 v27, v12

    move-object/from16 v0, v27

    array-length v0, v0

    move/from16 v27, v0

    invoke-static/range {v25 .. v27}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 568
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    const/16 v25, 0x3e8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setImageAlpha(I)V

    .line 569
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    check-cast v24, Landroid/view/ViewGroup$MarginLayoutParams;

    move-object/from16 v25, v2

    const/16 v26, 0xa

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->convertDipToPixels(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 571
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/ImageView;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-virtual/range {v27 .. v27}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v27

    invoke-direct/range {v26 .. v27}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    .line 572
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 573
    const/16 v24, 0x1

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->IconSize3()I

    move-result v25

    move/from16 v0, v25

    int-to-float v0, v0

    move/from16 v25, v0

    move-object/from16 v26, v2

    invoke-virtual/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->getResources()Landroid/content/res/Resources;

    move-result-object v26

    invoke-virtual/range {v26 .. v26}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v26

    invoke-static/range {v24 .. v26}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v24

    move/from16 v0, v24

    float-to-int v0, v0

    move/from16 v24, v0

    move/from16 v14, v24

    .line 574
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    move/from16 v25, v14

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 575
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    move/from16 v25, v14

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 576
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->requestLayout()V

    .line 577
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    sget-object v25, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 578
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    move-object/from16 v25, v12

    const/16 v26, 0x0

    move-object/from16 v27, v12

    move-object/from16 v0, v27

    array-length v0, v0

    move/from16 v27, v0

    invoke-static/range {v25 .. v27}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 579
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    const/16 v25, 0x3e8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setImageAlpha(I)V

    .line 580
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    check-cast v24, Landroid/view/ViewGroup$MarginLayoutParams;

    move-object/from16 v25, v2

    const/16 v26, 0xa

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->convertDipToPixels(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 582
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/ImageView;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-direct/range {v26 .. v27}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    .line 583
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    new-instance v25, Landroid/widget/RelativeLayout$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    invoke-direct/range {v26 .. v28}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 584
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    move-object/from16 v25, v2

    move-object/from16 v26, v2

    invoke-direct/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->IconSize2()I

    move-result v26

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 585
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    move-object/from16 v25, v2

    move-object/from16 v26, v2

    invoke-direct/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->IconSize2()I

    move-result v26

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 586
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->requestLayout()V

    .line 587
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    sget-object v25, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 588
    move-object/from16 v24, v2

    invoke-direct/range {v24 .. v24}, Lpmm/by/p2077kng/hacker/Loader;->Icon2()Ljava/lang/String;

    move-result-object v24

    const/16 v25, 0x0

    invoke-static/range {v24 .. v25}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v24

    move-object/from16 v15, v24

    .line 589
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    const/16 v25, 0xc8

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setImageAlpha(I)V

    .line 590
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    move-object/from16 v25, v15

    const/16 v26, 0x0

    move-object/from16 v27, v15

    move-object/from16 v0, v27

    array-length v0, v0

    move/from16 v27, v0

    invoke-static/range {v25 .. v27}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v24, v0

    invoke-virtual/range {v24 .. v24}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v24

    check-cast v24, Landroid/view/ViewGroup$MarginLayoutParams;

    move-object/from16 v25, v2

    const/16 v26, 0x2e

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->convertDipToPixels(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->leftMargin:I

    .line 593
    new-instance v24, Landroid/widget/TextView;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-direct/range {v25 .. v26}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v16, v24

    .line 594
    new-instance v24, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    const/16 v26, -0x2

    const/16 v27, -0x2

    invoke-direct/range {v25 .. v27}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v17, v24

    .line 595
    move-object/from16 v24, v17

    const/16 v25, 0x11

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 596
    move-object/from16 v24, v17

    move-object/from16 v25, v2

    const/16 v26, 0xa

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->m6dp(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->leftMargin:I

    .line 597
    move-object/from16 v24, v16

    move-object/from16 v25, v17

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 598
    move-object/from16 v24, v16

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->TitleMain()Ljava/lang/String;

    move-result-object v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 599
    move-object/from16 v24, v16

    const/high16 v25, 0x41580000    # 13.5f

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setTextSize(F)V

    .line 600
    move-object/from16 v24, v16

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->colourOne()Ljava/lang/String;

    move-result-object v25

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setTextColor(I)V

    .line 602
    move-object/from16 v24, v2

    new-instance v25, Landroid/widget/TextView;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    move-object/from16 v27, v2

    invoke-direct/range {v26 .. v27}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->Status:Landroid/widget/TextView;

    .line 603
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->Status:Landroid/widget/TextView;

    move-object/from16 v24, v0

    move-object/from16 v25, v17

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 604
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->Status:Landroid/widget/TextView;

    move-object/from16 v24, v0

    const/high16 v25, 0x41380000    # 11.5f

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setTextSize(F)V

    .line 606
    new-instance v24, Landroid/widget/TextView;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    move-object/from16 v26, v2

    invoke-virtual/range {v26 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v26

    invoke-direct/range {v25 .. v26}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v18, v24

    .line 607
    new-instance v24, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v32, v24

    move-object/from16 v24, v32

    move-object/from16 v25, v32

    const/16 v26, -0x2

    const/16 v27, -0x2

    invoke-direct/range {v25 .. v27}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v19, v24

    .line 608
    move-object/from16 v24, v19

    const/16 v25, 0x11

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/widget/LinearLayout$LayoutParams;->gravity:I

    .line 609
    move-object/from16 v24, v19

    move-object/from16 v25, v2

    const/16 v26, 0xc

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->m6dp(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 610
    move-object/from16 v24, v19

    move-object/from16 v25, v2

    const/16 v26, 0xc

    invoke-direct/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->m6dp(I)I

    move-result v25

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/ViewGroup$MarginLayoutParams;->bottomMargin:I

    .line 611
    move-object/from16 v24, v18

    move-object/from16 v25, v19

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 612
    move-object/from16 v24, v18

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->TitleOnn()Ljava/lang/String;

    move-result-object v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 613
    move-object/from16 v24, v18

    sget-object v25, Landroid/graphics/Typeface;->MONOSPACE:Landroid/graphics/Typeface;

    const/16 v26, 0x1

    invoke-virtual/range {v24 .. v26}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 614
    move-object/from16 v24, v18

    const/high16 v25, 0x41400000    # 12.0f

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setTextSize(F)V

    .line 615
    move-object/from16 v24, v18

    const-string v25, "#FFFFFF"

    invoke-static/range {v25 .. v25}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v25

    invoke-virtual/range {v24 .. v25}, Landroid/widget/TextView;->setTextColor(I)V

    .line 617
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->rootFrame:Landroid/widget/FrameLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mRootContainer:Landroid/widget/RelativeLayout;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 618
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mRootContainer:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 619
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mRootContainer:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 621
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage:Landroid/widget/ImageView;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 622
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 623
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v11

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 624
    move-object/from16 v24, v11

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->startimage2:Landroid/widget/ImageView;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 626
    move-object/from16 v24, v11

    move-object/from16 v25, v16

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 627
    move-object/from16 v24, v11

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->Status:Landroid/widget/TextView;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 628
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v10

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 629
    move-object/from16 v24, v10

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v25, v0

    invoke-virtual/range {v24 .. v25}, Landroid/widget/ScrollView;->addView(Landroid/view/View;)V

    .line 630
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v18

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 631
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    move-object/from16 v25, v6

    invoke-virtual/range {v24 .. v25}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 632
    move-object/from16 v24, v2

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->rootFrame:Landroid/widget/FrameLayout;

    move-object/from16 v25, v0

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mFloatingView:Landroid/view/View;

    .line 633
    sget v24, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v25, 0x1a

    move/from16 v0, v24

    move/from16 v1, v25

    if-lt v0, v1, :cond_0

    .line 634
    move-object/from16 v24, v2

    new-instance v25, Landroid/view/WindowManager$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    const/16 v29, 0x7f6

    const/16 v30, 0x8

    const/16 v31, -0x3

    invoke-direct/range {v26 .. v31}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    .line 638
    :goto_0
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    move-object/from16 v24, v0

    move-object/from16 v20, v24

    .line 639
    move-object/from16 v24, v20

    const/16 v25, 0x33

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 640
    move-object/from16 v24, v20

    const/16 v25, 0x0

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 641
    move-object/from16 v24, v20

    const/16 v25, 0x64

    move/from16 v0, v25

    move-object/from16 v1, v24

    iput v0, v1, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 642
    move-object/from16 v24, v2

    move-object/from16 v25, v2

    const-string v26, "window"

    invoke-virtual/range {v25 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v25

    check-cast v25, Landroid/view/WindowManager;

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    .line 643
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    move-object/from16 v0, v25

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mFloatingView:Landroid/view/View;

    move-object/from16 v25, v0

    move-object/from16 v26, v2

    move-object/from16 v0, v26

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    move-object/from16 v26, v0

    invoke-interface/range {v24 .. v26}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 645
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mCollapsed:Landroid/widget/RelativeLayout;

    move-object/from16 v24, v0

    move-object/from16 v21, v24

    .line 646
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    move-object/from16 v24, v0

    move-object/from16 v22, v24

    .line 647
    move-object/from16 v24, v2

    move-object/from16 v0, v24

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->mFloatingView:Landroid/view/View;

    move-object/from16 v24, v0

    move-object/from16 v25, v2

    invoke-direct/range {v25 .. v25}, Lpmm/by/p2077kng/hacker/Loader;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v25

    invoke-virtual/range {v24 .. v25}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 648
    move-object/from16 v24, v2

    move-object/from16 v25, v21

    move-object/from16 v26, v22

    invoke-direct/range {v24 .. v26}, Lpmm/by/p2077kng/hacker/Loader;->initMenuButton(Landroid/view/View;Landroid/view/View;)V

    return-void

    .line 636
    :cond_0
    move-object/from16 v24, v2

    new-instance v25, Landroid/view/WindowManager$LayoutParams;

    move-object/from16 v32, v25

    move-object/from16 v25, v32

    move-object/from16 v26, v32

    const/16 v27, -0x2

    const/16 v28, -0x2

    const/16 v29, 0x7d2

    const/16 v30, 0x8

    const/16 v31, -0x3

    invoke-direct/range {v26 .. v31}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    move-object/from16 v0, v25

    move-object/from16 v1, v24

    iput-object v0, v1, Lpmm/by/p2077kng/hacker/Loader;->params:Landroid/view/WindowManager$LayoutParams;

    goto/16 :goto_0
.end method

.method private native Icon()Ljava/lang/String;
.end method

.method private native Icon2()Ljava/lang/String;
.end method

.method private native IconSize()I
.end method

.method private native IconSize2()I
.end method

.method private native IconSize3()I
.end method

.method public static native Init()I
.end method

.method private native IsConnected()Z
.end method

.method private native MenuPro()[Ljava/lang/String;
.end method

.method private native P2077KNG()Ljava/lang/String;
.end method

.method private Running()V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 290
    move-object v0, p0

    new-instance v4, Landroid/os/Handler;

    move-object v9, v4

    move-object v4, v9

    move-object v5, v9

    invoke-direct {v5}, Landroid/os/Handler;-><init>()V

    move-object v2, v4

    .line 291
    move-object v4, v2

    new-instance v5, Lpmm/by/p2077kng/hacker/Loader$100000003;

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    move-object v7, v0

    move-object v8, v2

    invoke-direct {v6, v7, v8}, Lpmm/by/p2077kng/hacker/Loader$100000003;-><init>(Lpmm/by/p2077kng/hacker/Loader;Landroid/os/Handler;)V

    invoke-virtual {v4, v5}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    move-result v4

    return-void
.end method

.method private Thread()V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 1316
    move-object v0, p0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Loader;->frameLayout:Landroid/widget/FrameLayout;

    if-nez v2, :cond_0

    .line 1317
    :goto_0
    return-void

    :cond_0
    goto :goto_0
.end method

.method private ThreadStatus()V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 455
    move-object v0, p0

    move-object v2, v0

    iget-boolean v2, v2, Lpmm/by/p2077kng/hacker/Loader;->StatusOk:Z

    if-nez v2, :cond_0

    .line 462
    :goto_0
    return-void

    .line 458
    :cond_0
    move-object v2, v0

    invoke-direct {v2}, Lpmm/by/p2077kng/hacker/Loader;->IsConnected()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 459
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Loader;->Status:Landroid/widget/TextView;

    const-string v3, "<b>STATUS : </b><font color=\'#00FF00\'>Online</font>"

    invoke-static {v3}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 462
    :goto_1
    goto :goto_0

    :cond_1
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Loader;->Status:Landroid/widget/TextView;

    const-string v3, "<b>STATUS : </b><font color=\'#de0c0c\'>Offline</font>"

    invoke-static {v3}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_1
.end method

.method private native Title4()Ljava/lang/String;
.end method

.method private native Title5()Ljava/lang/String;
.end method

.method private native Title6()Ljava/lang/String;
.end method

.method private native TitleMain()Ljava/lang/String;
.end method

.method private native TitleOnn()Ljava/lang/String;
.end method

.method public static native VMOSPatch()Ljava/lang/String;
.end method

.method public static VersionInject()Ljava/lang/String;
    .locals 4

    .prologue
    .line 310
    sget-object v2, Landroid/os/Build;->MODEL:Ljava/lang/String;

    const-string v3, "Root"

    invoke-virtual {v2, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 311
    const-string v2, "<font color=\'#00FF00\'>Root"

    move-object v0, v2

    .line 313
    :goto_0
    return-object v0

    :cond_0
    const-string v2, "<font color=\'#00FF00\'>Root"

    move-object v0, v2

    goto :goto_0
.end method

.method static synthetic access$1000009(Lpmm/by/p2077kng/hacker/Loader;)Ljava/lang/String;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    invoke-direct {v3}, Lpmm/by/p2077kng/hacker/Loader;->P2077KNG()Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method static synthetic access$1000049(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 4

    move-object v0, p0

    move-object v3, v0

    invoke-direct {v3}, Lpmm/by/p2077kng/hacker/Loader;->ThreadStatus()V

    return-void
.end method

.method static synthetic access$1000113(Lpmm/by/p2077kng/hacker/Loader;)V
    .locals 4

    move-object v0, p0

    move-object v3, v0

    invoke-direct {v3}, Lpmm/by/p2077kng/hacker/Loader;->Thread()V

    return-void
.end method

.method static synthetic access$L1000019()Landroid/widget/Button;
    .locals 3

    sget-object v2, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    move-object v0, v2

    return-object v0
.end method

.method static synthetic access$S1000019(Landroid/widget/Button;)V
    .locals 4

    move-object v0, p0

    move-object v3, v0

    sput-object v3, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    return-void
.end method

.method public static accont(Ljava/lang/String;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1258
    move-object v0, p0

    new-instance v8, Ljava/io/File;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    move-object v10, v0

    invoke-direct {v9, v10}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v2, v8

    .line 1259
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->exists()Z

    move-result v8

    if-nez v8, :cond_0

    .line 1275
    :goto_0
    return-void

    .line 1260
    :cond_0
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->isFile()Z

    move-result v8

    if-eqz v8, :cond_1

    .line 1261
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->delete()Z

    move-result v8

    .line 1262
    goto :goto_0

    .line 1264
    :cond_1
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object v8

    move-object v3, v8

    .line 1265
    move-object v8, v3

    if-eqz v8, :cond_2

    .line 1266
    move-object v8, v3

    move-object v4, v8

    const/4 v8, 0x0

    move v5, v8

    .line 1271
    :goto_1
    move v8, v5

    move-object v9, v4

    array-length v9, v9

    if-lt v8, v9, :cond_3

    .line 1275
    :cond_2
    move-object v8, v2

    invoke-virtual {v8}, Ljava/io/File;->delete()Z

    move-result v8

    goto :goto_0

    .line 1266
    :cond_3
    move-object v8, v4

    move v9, v5

    aget-object v8, v8, v9

    move-object v6, v8

    .line 1267
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->isDirectory()Z

    move-result v8

    if-eqz v8, :cond_4

    .line 1268
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lpmm/by/p2077kng/hacker/Loader;->accont(Ljava/lang/String;)V

    .line 1270
    :cond_4
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->isFile()Z

    move-result v8

    if-eqz v8, :cond_5

    .line 1271
    move-object v8, v6

    invoke-virtual {v8}, Ljava/io/File;->delete()Z

    move-result v8

    :cond_5
    add-int/lit8 v5, v5, 0x1

    goto :goto_1
.end method

.method private addCategory(Ljava/lang/String;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1091
    move-object v0, p0

    move-object v1, p1

    new-instance v7, Landroid/widget/LinearLayout;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v0

    invoke-direct {v8, v9}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object v3, v7

    .line 1092
    new-instance v7, Landroid/widget/LinearLayout$LayoutParams;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    const/4 v9, -0x1

    const/4 v10, -0x2

    invoke-direct {v8, v9, v10}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object v4, v7

    .line 1093
    move-object v7, v3

    const/4 v8, 0x1

    invoke-virtual {v7, v8}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 1094
    move-object v7, v3

    move-object v8, v4

    invoke-virtual {v7, v8}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 1095
    new-instance v7, Landroid/widget/TextView;

    move-object v12, v7

    move-object v7, v12

    move-object v8, v12

    move-object v9, v0

    invoke-direct {v8, v9}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v5, v7

    .line 1096
    move-object v7, v5

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v12, v8

    move-object v8, v12

    move-object v9, v12

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v12, v9

    move-object v9, v12

    move-object v10, v12

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v12, v10

    move-object v10, v12

    move-object v11, v12

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    const-string v11, ""

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    move-object v11, v1

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, " - "

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    move-object v9, v0

    invoke-direct {v9}, Lpmm/by/p2077kng/hacker/Loader;->P2077KNG()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1097
    move-object v7, v5

    const/16 v8, 0x11

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setGravity(I)V

    .line 1098
    move-object v7, v5

    const/16 v8, 0xc

    int-to-float v8, v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setTextSize(F)V

    .line 1099
    move-object v7, v5

    const/4 v8, 0x0

    check-cast v8, Landroid/graphics/Typeface;

    const/4 v9, 0x0

    invoke-virtual {v7, v8, v9}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 1100
    move-object v7, v5

    move-object v8, v0

    invoke-direct {v8}, Lpmm/by/p2077kng/hacker/Loader;->colourOne()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1101
    move-object v7, v5

    const/16 v8, 0xa

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    invoke-virtual {v7, v8, v9, v10, v11}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 1102
    move-object v7, v3

    move-object v8, v5

    invoke-virtual {v7, v8}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1103
    move-object v7, v0

    iget-object v7, v7, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object v8, v3

    invoke-virtual {v7, v8}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSeekBar(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;",
            ")V"
        }
    .end annotation

    .prologue
    .line 763
    move-object/from16 v2, p0

    move-object/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move-object/from16 v6, p4

    new-instance v18, Landroid/widget/LinearLayout;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v8, v18

    .line 764
    new-instance v18, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    const/16 v20, -0x1

    const/16 v21, -0x1

    invoke-direct/range {v19 .. v21}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v18

    .line 765
    move-object/from16 v18, v8

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 766
    move-object/from16 v18, v8

    move-object/from16 v19, v9

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 767
    new-instance v18, Landroid/widget/TextView;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v18

    .line 768
    move-object/from16 v18, v10

    new-instance v19, Ljava/lang/StringBuffer;

    move-object/from16 v26, v19

    move-object/from16 v19, v26

    move-object/from16 v20, v26

    invoke-direct/range {v20 .. v20}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v20, Ljava/lang/StringBuffer;

    move-object/from16 v26, v20

    move-object/from16 v20, v26

    move-object/from16 v21, v26

    invoke-direct/range {v21 .. v21}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v21, Ljava/lang/StringBuffer;

    move-object/from16 v26, v21

    move-object/from16 v21, v26

    move-object/from16 v22, v26

    invoke-direct/range {v22 .. v22}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v22, Ljava/lang/StringBuffer;

    move-object/from16 v26, v22

    move-object/from16 v22, v26

    move-object/from16 v23, v26

    invoke-direct/range {v23 .. v23}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v23, Ljava/lang/StringBuffer;

    move-object/from16 v26, v23

    move-object/from16 v23, v26

    move-object/from16 v24, v26

    invoke-direct/range {v24 .. v24}, Ljava/lang/StringBuffer;-><init>()V

    const-string v24, "<font face=><b>"

    invoke-virtual/range {v23 .. v24}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v23

    move-object/from16 v24, v3

    invoke-virtual/range {v23 .. v24}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v23

    invoke-virtual/range {v23 .. v23}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v23

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    const-string v23, ": <font color=\'WHITE\'>"

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    invoke-virtual/range {v22 .. v22}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    const/16 v22, 0x0

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v21

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    const-string v21, "x"

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    invoke-virtual/range {v20 .. v20}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v20

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    const-string v20, "</font>"

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    invoke-virtual/range {v19 .. v19}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 769
    move-object/from16 v18, v10

    const/16 v19, 0xff

    const/16 v20, 0xff

    const/16 v21, 0xff

    invoke-static/range {v19 .. v21}, Landroid/graphics/Color;->rgb(III)I

    move-result v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextColor(I)V

    .line 770
    move-object/from16 v18, v10

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 771
    move-object/from16 v18, v10

    const/16 v19, 0xb

    move/from16 v0, v19

    int-to-float v0, v0

    move/from16 v19, v0

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextSize(F)V

    .line 773
    new-instance v18, Landroid/widget/SeekBar;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v18

    .line 774
    move-object/from16 v18, v11

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/SeekBar;->setPadding(IIII)V

    .line 775
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 776
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 777
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 778
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setThumbTintList(Landroid/content/res/ColorStateList;)V

    .line 779
    sget v18, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v19, 0x15

    move/from16 v0, v18

    move/from16 v1, v19

    if-lt v0, v1, :cond_0

    .line 782
    :cond_0
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 783
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 784
    move/from16 v18, v4

    move/from16 v12, v18

    .line 785
    move-object/from16 v18, v11

    move-object/from16 v13, v18

    .line 786
    move-object/from16 v18, v6

    move-object/from16 v14, v18

    .line 787
    move-object/from16 v18, v10

    move-object/from16 v15, v18

    .line 788
    move-object/from16 v18, v3

    move-object/from16 v16, v18

    .line 789
    move-object/from16 v18, v11

    new-instance v19, Lpmm/by/p2077kng/hacker/Loader$100000016;

    move-object/from16 v26, v19

    move-object/from16 v19, v26

    move-object/from16 v20, v26

    move-object/from16 v21, v2

    move-object/from16 v22, v14

    move-object/from16 v23, v15

    move-object/from16 v24, v16

    move-object/from16 v25, v10

    invoke-direct/range {v20 .. v25}, Lpmm/by/p2077kng/hacker/Loader$100000016;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;Landroid/widget/TextView;)V

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 810
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v8

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 811
    move-object/from16 v18, v8

    move-object/from16 v19, v10

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 812
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v11

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSeekBar2(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;",
            ")V"
        }
    .end annotation

    .prologue
    .line 816
    move-object/from16 v2, p0

    move-object/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move-object/from16 v6, p4

    new-instance v18, Landroid/widget/LinearLayout;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v8, v18

    .line 817
    new-instance v18, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    const/16 v20, -0x1

    const/16 v21, -0x1

    invoke-direct/range {v19 .. v21}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v18

    .line 818
    move-object/from16 v18, v8

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 819
    move-object/from16 v18, v8

    move-object/from16 v19, v9

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 820
    new-instance v18, Landroid/widget/TextView;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v18

    .line 821
    move-object/from16 v18, v10

    new-instance v19, Ljava/lang/StringBuffer;

    move-object/from16 v26, v19

    move-object/from16 v19, v26

    move-object/from16 v20, v26

    invoke-direct/range {v20 .. v20}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v20, Ljava/lang/StringBuffer;

    move-object/from16 v26, v20

    move-object/from16 v20, v26

    move-object/from16 v21, v26

    invoke-direct/range {v21 .. v21}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v21, Ljava/lang/StringBuffer;

    move-object/from16 v26, v21

    move-object/from16 v21, v26

    move-object/from16 v22, v26

    invoke-direct/range {v22 .. v22}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v22, Ljava/lang/StringBuffer;

    move-object/from16 v26, v22

    move-object/from16 v22, v26

    move-object/from16 v23, v26

    invoke-direct/range {v23 .. v23}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v23, Ljava/lang/StringBuffer;

    move-object/from16 v26, v23

    move-object/from16 v23, v26

    move-object/from16 v24, v26

    invoke-direct/range {v24 .. v24}, Ljava/lang/StringBuffer;-><init>()V

    const-string v24, "<font face=><b>"

    invoke-virtual/range {v23 .. v24}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v23

    move-object/from16 v24, v3

    invoke-virtual/range {v23 .. v24}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v23

    invoke-virtual/range {v23 .. v23}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v23

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    const-string v23, ": <font color=\'WHITE\'>"

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    invoke-virtual/range {v22 .. v22}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    const/16 v22, 0x0

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v21

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    const-string v21, "x"

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    invoke-virtual/range {v20 .. v20}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v20

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    const-string v20, "</font>"

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    invoke-virtual/range {v19 .. v19}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 822
    move-object/from16 v18, v10

    const/16 v19, 0xff

    const/16 v20, 0xff

    const/16 v21, 0xff

    invoke-static/range {v19 .. v21}, Landroid/graphics/Color;->rgb(III)I

    move-result v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextColor(I)V

    .line 823
    move-object/from16 v18, v10

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 824
    move-object/from16 v18, v10

    const/16 v19, 0xb

    move/from16 v0, v19

    int-to-float v0, v0

    move/from16 v19, v0

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextSize(F)V

    .line 826
    new-instance v18, Landroid/widget/SeekBar;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v18

    .line 827
    move-object/from16 v18, v11

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/SeekBar;->setPadding(IIII)V

    .line 828
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 829
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 830
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 831
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setThumbTintList(Landroid/content/res/ColorStateList;)V

    .line 832
    sget v18, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v19, 0x15

    move/from16 v0, v18

    move/from16 v1, v19

    if-lt v0, v1, :cond_0

    .line 835
    :cond_0
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 836
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 837
    move/from16 v18, v4

    move/from16 v12, v18

    .line 838
    move-object/from16 v18, v11

    move-object/from16 v13, v18

    .line 839
    move-object/from16 v18, v6

    move-object/from16 v14, v18

    .line 840
    move-object/from16 v18, v10

    move-object/from16 v15, v18

    .line 841
    move-object/from16 v18, v3

    move-object/from16 v16, v18

    .line 842
    move-object/from16 v18, v11

    new-instance v19, Lpmm/by/p2077kng/hacker/Loader$100000017;

    move-object/from16 v26, v19

    move-object/from16 v19, v26

    move-object/from16 v20, v26

    move-object/from16 v21, v2

    move-object/from16 v22, v14

    move-object/from16 v23, v15

    move-object/from16 v24, v16

    move-object/from16 v25, v10

    invoke-direct/range {v20 .. v25}, Lpmm/by/p2077kng/hacker/Loader$100000017;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;Landroid/widget/TextView;)V

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 863
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v8

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 864
    move-object/from16 v18, v8

    move-object/from16 v19, v10

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 865
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v11

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSeekBarColor(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V
    .locals 26
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;",
            ")V"
        }
    .end annotation

    .prologue
    .line 988
    move-object/from16 v2, p0

    move-object/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move-object/from16 v6, p4

    new-instance v18, Landroid/widget/LinearLayout;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v8, v18

    .line 989
    new-instance v18, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    const/16 v20, -0x1

    const/16 v21, -0x1

    invoke-direct/range {v19 .. v21}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v18

    .line 990
    move-object/from16 v18, v8

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 991
    move-object/from16 v18, v8

    move-object/from16 v19, v9

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 992
    new-instance v18, Landroid/widget/TextView;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v18

    .line 993
    move-object/from16 v18, v10

    new-instance v19, Ljava/lang/StringBuffer;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    invoke-direct/range {v20 .. v20}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v20, Ljava/lang/StringBuffer;

    move-object/from16 v25, v20

    move-object/from16 v20, v25

    move-object/from16 v21, v25

    invoke-direct/range {v21 .. v21}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v21, Ljava/lang/StringBuffer;

    move-object/from16 v25, v21

    move-object/from16 v21, v25

    move-object/from16 v22, v25

    invoke-direct/range {v22 .. v22}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v22, Ljava/lang/StringBuffer;

    move-object/from16 v25, v22

    move-object/from16 v22, v25

    move-object/from16 v23, v25

    invoke-direct/range {v23 .. v23}, Ljava/lang/StringBuffer;-><init>()V

    const-string v23, "<font face=><b>"

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    move-object/from16 v23, v3

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    invoke-virtual/range {v22 .. v22}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    const-string v22, ": <font color=\'WHITE\'>"

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v21

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    const-string v21, "WHITE"

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    invoke-virtual/range {v20 .. v20}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v20

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    const-string v20, "</font>"

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    invoke-virtual/range {v19 .. v19}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 994
    move-object/from16 v18, v10

    const/16 v19, 0xff

    const/16 v20, 0xff

    const/16 v21, 0xff

    invoke-static/range {v19 .. v21}, Landroid/graphics/Color;->rgb(III)I

    move-result v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextColor(I)V

    .line 995
    move-object/from16 v18, v10

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 996
    move-object/from16 v18, v10

    const/16 v19, 0xb

    move/from16 v0, v19

    int-to-float v0, v0

    move/from16 v19, v0

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextSize(F)V

    .line 998
    new-instance v18, Landroid/widget/SeekBar;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v18

    .line 999
    move-object/from16 v18, v11

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/SeekBar;->setPadding(IIII)V

    .line 1000
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 1001
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 1002
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 1003
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setThumbTintList(Landroid/content/res/ColorStateList;)V

    .line 1004
    sget v18, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v19, 0x15

    move/from16 v0, v18

    move/from16 v1, v19

    if-lt v0, v1, :cond_0

    .line 1007
    :cond_0
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 1008
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 1009
    move/from16 v18, v4

    move/from16 v12, v18

    .line 1010
    move-object/from16 v18, v11

    move-object/from16 v13, v18

    .line 1011
    move-object/from16 v18, v6

    move-object/from16 v14, v18

    .line 1012
    move-object/from16 v18, v10

    move-object/from16 v15, v18

    .line 1013
    move-object/from16 v18, v3

    move-object/from16 v16, v18

    .line 1014
    move-object/from16 v18, v11

    new-instance v19, Lpmm/by/p2077kng/hacker/Loader$100000020;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    move-object/from16 v22, v14

    move-object/from16 v23, v15

    move-object/from16 v24, v16

    invoke-direct/range {v20 .. v24}, Lpmm/by/p2077kng/hacker/Loader$100000020;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;)V

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 1067
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v8

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1068
    move-object/from16 v18, v8

    move-object/from16 v19, v10

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 1069
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v11

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSeekBarFov(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;",
            ")V"
        }
    .end annotation

    .prologue
    .line 935
    move-object/from16 v2, p0

    move-object/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move-object/from16 v6, p4

    new-instance v18, Landroid/widget/LinearLayout;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v8, v18

    .line 936
    new-instance v18, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    const/16 v20, -0x1

    const/16 v21, -0x1

    invoke-direct/range {v19 .. v21}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v18

    .line 937
    move-object/from16 v18, v8

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 938
    move-object/from16 v18, v8

    move-object/from16 v19, v9

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 939
    new-instance v18, Landroid/widget/TextView;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v18

    .line 940
    move-object/from16 v18, v10

    new-instance v19, Ljava/lang/StringBuffer;

    move-object/from16 v26, v19

    move-object/from16 v19, v26

    move-object/from16 v20, v26

    invoke-direct/range {v20 .. v20}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v20, Ljava/lang/StringBuffer;

    move-object/from16 v26, v20

    move-object/from16 v20, v26

    move-object/from16 v21, v26

    invoke-direct/range {v21 .. v21}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v21, Ljava/lang/StringBuffer;

    move-object/from16 v26, v21

    move-object/from16 v21, v26

    move-object/from16 v22, v26

    invoke-direct/range {v22 .. v22}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v22, Ljava/lang/StringBuffer;

    move-object/from16 v26, v22

    move-object/from16 v22, v26

    move-object/from16 v23, v26

    invoke-direct/range {v23 .. v23}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v23, Ljava/lang/StringBuffer;

    move-object/from16 v26, v23

    move-object/from16 v23, v26

    move-object/from16 v24, v26

    invoke-direct/range {v24 .. v24}, Ljava/lang/StringBuffer;-><init>()V

    const-string v24, "<font face=><b>"

    invoke-virtual/range {v23 .. v24}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v23

    move-object/from16 v24, v3

    invoke-virtual/range {v23 .. v24}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v23

    invoke-virtual/range {v23 .. v23}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v23

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    const-string v23, ": <font color=\'WHITE\'>"

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    invoke-virtual/range {v22 .. v22}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    move/from16 v22, v5

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v21

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    const-string v21, "x"

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    invoke-virtual/range {v20 .. v20}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v20

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    const-string v20, "</font>"

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    invoke-virtual/range {v19 .. v19}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 941
    move-object/from16 v18, v10

    const/16 v19, 0xff

    const/16 v20, 0xff

    const/16 v21, 0xff

    invoke-static/range {v19 .. v21}, Landroid/graphics/Color;->rgb(III)I

    move-result v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextColor(I)V

    .line 942
    move-object/from16 v18, v10

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 943
    move-object/from16 v18, v10

    const/16 v19, 0xb

    move/from16 v0, v19

    int-to-float v0, v0

    move/from16 v19, v0

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextSize(F)V

    .line 945
    new-instance v18, Landroid/widget/SeekBar;

    move-object/from16 v26, v18

    move-object/from16 v18, v26

    move-object/from16 v19, v26

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v18

    .line 946
    move-object/from16 v18, v11

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/SeekBar;->setPadding(IIII)V

    .line 947
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 948
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 949
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 950
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setThumbTintList(Landroid/content/res/ColorStateList;)V

    .line 951
    sget v18, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v19, 0x15

    move/from16 v0, v18

    move/from16 v1, v19

    if-lt v0, v1, :cond_0

    .line 954
    :cond_0
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 955
    move-object/from16 v18, v11

    move/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 956
    move/from16 v18, v4

    move/from16 v12, v18

    .line 957
    move-object/from16 v18, v11

    move-object/from16 v13, v18

    .line 958
    move-object/from16 v18, v6

    move-object/from16 v14, v18

    .line 959
    move-object/from16 v18, v10

    move-object/from16 v15, v18

    .line 960
    move-object/from16 v18, v3

    move-object/from16 v16, v18

    .line 961
    move-object/from16 v18, v11

    new-instance v19, Lpmm/by/p2077kng/hacker/Loader$100000019;

    move-object/from16 v26, v19

    move-object/from16 v19, v26

    move-object/from16 v20, v26

    move-object/from16 v21, v2

    move-object/from16 v22, v14

    move-object/from16 v23, v15

    move-object/from16 v24, v16

    move-object/from16 v25, v10

    invoke-direct/range {v20 .. v25}, Lpmm/by/p2077kng/hacker/Loader$100000019;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;Landroid/widget/TextView;)V

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 982
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v8

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 983
    move-object/from16 v18, v8

    move-object/from16 v19, v10

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 984
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v11

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSeekBarSpot(Ljava/lang/String;IILpmm/by/p2077kng/hacker/Loader$InterfaceInt;)V
    .locals 26
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;",
            ")V"
        }
    .end annotation

    .prologue
    .line 869
    move-object/from16 v2, p0

    move-object/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move-object/from16 v6, p4

    new-instance v18, Landroid/widget/LinearLayout;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object/from16 v8, v18

    .line 870
    new-instance v18, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    const/16 v20, -0x1

    const/16 v21, -0x1

    invoke-direct/range {v19 .. v21}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object/from16 v9, v18

    .line 871
    move-object/from16 v18, v8

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 872
    move-object/from16 v18, v8

    move-object/from16 v19, v9

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 873
    new-instance v18, Landroid/widget/TextView;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object/from16 v10, v18

    .line 874
    move-object/from16 v18, v10

    new-instance v19, Ljava/lang/StringBuffer;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    invoke-direct/range {v20 .. v20}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v20, Ljava/lang/StringBuffer;

    move-object/from16 v25, v20

    move-object/from16 v20, v25

    move-object/from16 v21, v25

    invoke-direct/range {v21 .. v21}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v21, Ljava/lang/StringBuffer;

    move-object/from16 v25, v21

    move-object/from16 v21, v25

    move-object/from16 v22, v25

    invoke-direct/range {v22 .. v22}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v22, Ljava/lang/StringBuffer;

    move-object/from16 v25, v22

    move-object/from16 v22, v25

    move-object/from16 v23, v25

    invoke-direct/range {v23 .. v23}, Ljava/lang/StringBuffer;-><init>()V

    const-string v23, "<font face=><b>"

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    move-object/from16 v23, v3

    invoke-virtual/range {v22 .. v23}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v22

    invoke-virtual/range {v22 .. v22}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v22

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    const-string v22, ": <font color=\'WHITE\'>"

    invoke-virtual/range {v21 .. v22}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v21

    invoke-virtual/range {v21 .. v21}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v21

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    const-string v21, "HEAD"

    invoke-virtual/range {v20 .. v21}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v20

    invoke-virtual/range {v20 .. v20}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v20

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    const-string v20, "</font>"

    invoke-virtual/range {v19 .. v20}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v19

    invoke-virtual/range {v19 .. v19}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 875
    move-object/from16 v18, v10

    const/16 v19, 0xff

    const/16 v20, 0xff

    const/16 v21, 0xff

    invoke-static/range {v19 .. v21}, Landroid/graphics/Color;->rgb(III)I

    move-result v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextColor(I)V

    .line 876
    move-object/from16 v18, v10

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 877
    move-object/from16 v18, v10

    const/16 v19, 0xb

    move/from16 v0, v19

    int-to-float v0, v0

    move/from16 v19, v0

    invoke-virtual/range {v18 .. v19}, Landroid/widget/TextView;->setTextSize(F)V

    .line 879
    new-instance v18, Landroid/widget/SeekBar;

    move-object/from16 v25, v18

    move-object/from16 v18, v25

    move-object/from16 v19, v25

    move-object/from16 v20, v2

    invoke-direct/range {v19 .. v20}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    move-object/from16 v11, v18

    .line 880
    move-object/from16 v18, v11

    const/16 v19, 0xf

    const/16 v20, 0x5

    const/16 v21, 0xf

    const/16 v22, 0x5

    invoke-virtual/range {v18 .. v22}, Landroid/widget/SeekBar;->setPadding(IIII)V

    .line 881
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 882
    move-object/from16 v18, v11

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 883
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 884
    move-object/from16 v18, v11

    move-object/from16 v19, v2

    invoke-direct/range {v19 .. v19}, Lpmm/by/p2077kng/hacker/Loader;->colourTwo()Ljava/lang/String;

    move-result-object v19

    invoke-static/range {v19 .. v19}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v19

    invoke-static/range {v19 .. v19}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v19

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setThumbTintList(Landroid/content/res/ColorStateList;)V

    .line 886
    sget v18, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v19, 0x15

    move/from16 v0, v18

    move/from16 v1, v19

    if-lt v0, v1, :cond_0

    .line 889
    :cond_0
    move-object/from16 v18, v11

    move/from16 v19, v5

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setMax(I)V

    .line 890
    move-object/from16 v18, v11

    const/16 v19, 0x1

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 891
    move/from16 v18, v4

    move/from16 v12, v18

    .line 892
    move-object/from16 v18, v11

    move-object/from16 v13, v18

    .line 893
    move-object/from16 v18, v6

    move-object/from16 v14, v18

    .line 894
    move-object/from16 v18, v10

    move-object/from16 v15, v18

    .line 895
    move-object/from16 v18, v3

    move-object/from16 v16, v18

    .line 896
    move-object/from16 v18, v11

    new-instance v19, Lpmm/by/p2077kng/hacker/Loader$100000018;

    move-object/from16 v25, v19

    move-object/from16 v19, v25

    move-object/from16 v20, v25

    move-object/from16 v21, v2

    move-object/from16 v22, v14

    move-object/from16 v23, v15

    move-object/from16 v24, v16

    invoke-direct/range {v20 .. v24}, Lpmm/by/p2077kng/hacker/Loader$100000018;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceInt;Landroid/widget/TextView;Ljava/lang/String;)V

    invoke-virtual/range {v18 .. v19}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 929
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v8

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 930
    move-object/from16 v18, v8

    move-object/from16 v19, v10

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 931
    move-object/from16 v18, v2

    move-object/from16 v0, v18

    iget-object v0, v0, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object/from16 v18, v0

    move-object/from16 v19, v11

    invoke-virtual/range {v18 .. v19}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method private addSwitch(Ljava/lang/String;Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1073
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    new-instance v6, Landroid/widget/Switch;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    move-object v8, v0

    invoke-direct {v7, v8}, Landroid/widget/Switch;-><init>(Landroid/content/Context;)V

    move-object v4, v6

    .line 1074
    move-object v6, v4

    new-instance v7, Ljava/lang/StringBuffer;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v11, v9

    move-object v9, v11

    move-object v10, v11

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    const-string v10, "<font face=><b>"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "<font color=\'WHITE\'>"

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    move-object v9, v1

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "</font>"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v7

    invoke-virtual {v6, v7}, Landroid/widget/Switch;->setText(Ljava/lang/CharSequence;)V

    .line 1075
    move-object v6, v4

    const/4 v7, -0x1

    invoke-virtual {v6, v7}, Landroid/widget/Switch;->setTextColor(I)V

    .line 1076
    move-object v6, v4

    const/16 v7, 0xe

    int-to-float v7, v7

    invoke-virtual {v6, v7}, Landroid/widget/Switch;->setTextSize(F)V

    .line 1077
    move-object v6, v4

    new-instance v7, Lpmm/by/p2077kng/hacker/Loader$100000021;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    move-object v9, v0

    move-object v10, v2

    invoke-direct {v8, v9, v10}, Lpmm/by/p2077kng/hacker/Loader$100000021;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBool;)V

    invoke-virtual {v6, v7}, Landroid/widget/Switch;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 1087
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object v7, v4

    invoke-virtual {v6, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void
.end method

.method public static bruno_icon(Landroid/content/Context;Ljava/lang/String;)Landroid/graphics/drawable/Drawable;
    .locals 12

    .prologue
    .line 142
    move-object v0, p0

    move-object v1, p1

    move-object v5, v1

    const/4 v6, 0x0

    invoke-static {v5, v6}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v5

    move-object v3, v5

    .line 143
    new-instance v5, Landroid/graphics/drawable/BitmapDrawable;

    move-object v11, v5

    move-object v5, v11

    move-object v6, v11

    move-object v7, v0

    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v7

    move-object v8, v3

    const/4 v9, 0x0

    move-object v10, v3

    array-length v10, v10

    invoke-static {v8, v9, v10}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v8

    invoke-direct {v6, v7, v8}, Landroid/graphics/drawable/BitmapDrawable;-><init>(Landroid/content/res/Resources;Landroid/graphics/Bitmap;)V

    move-object v0, v5

    return-object v0
.end method

.method private native colourOne()Ljava/lang/String;
.end method

.method private native colourTwo()Ljava/lang/String;
.end method

.method private convertDipToPixels(I)I
    .locals 5

    .prologue
    .line 1282
    move-object v0, p0

    move v1, p1

    move v3, v1

    int-to-float v3, v3

    move-object v4, v0

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/Loader;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    invoke-virtual {v4}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v4

    iget v4, v4, Landroid/util/DisplayMetrics;->density:F

    mul-float/2addr v3, v4

    const/high16 v4, 0x3f000000    # 0.5f

    add-float/2addr v3, v4

    float-to-int v3, v3

    move v0, v3

    return v0
.end method

.method private static currArch()Ljava/lang/String;
    .locals 5

    .prologue
    .line 318
    const-string v3, "os.arch"

    check-cast v3, Ljava/lang/String;

    invoke-static {v3}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    move-object v1, v3

    .line 319
    move-object v3, v1

    if-eqz v3, :cond_0

    .line 320
    move-object v3, v1

    const-string v4, "armv8l"

    check-cast v4, Ljava/lang/CharSequence;

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-nez v3, :cond_1

    move-object v3, v1

    const-string v4, "aarch64"

    check-cast v4, Ljava/lang/CharSequence;

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-nez v3, :cond_1

    .line 321
    move-object v3, v1

    const-string v4, "i686"

    check-cast v4, Ljava/lang/CharSequence;

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 322
    const-string v3, "x86"

    move-object v0, v3

    .line 328
    :goto_0
    return-object v0

    :cond_0
    const-string v3, "armeabi-v7a"

    move-object v0, v3

    goto :goto_0

    .line 325
    :cond_1
    const-string v3, "arm64-v8a"

    move-object v0, v3

    goto :goto_0
.end method

.method private dp(I)I
    .locals 6

    .prologue
    .line 1221
    move-object v0, p0

    move v1, p1

    const/4 v3, 0x1

    move v4, v1

    int-to-float v4, v4

    move-object v5, v0

    invoke-virtual {v5}, Lpmm/by/p2077kng/hacker/Loader;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    invoke-virtual {v5}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v5

    invoke-static {v3, v4, v5}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v3

    float-to-int v3, v3

    move v0, v3

    return v0
.end method

.method public static dp(ILandroid/content/Context;)I
    .locals 7

    .prologue
    .line 147
    move v0, p0

    move-object v1, p1

    move-object v5, v1

    invoke-virtual {v5}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    invoke-virtual {v5}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v5

    iget v5, v5, Landroid/util/DisplayMetrics;->density:F

    move v3, v5

    .line 148
    move v5, v0

    int-to-float v5, v5

    move v6, v3

    mul-float/2addr v5, v6

    const/high16 v6, 0x3f800000    # 1.0f

    add-float/2addr v5, v6

    float-to-int v5, v5

    move v0, v5

    return v0
.end method

.method private static getArch(Ljava/lang/String;)Ljava/lang/String;
    .locals 5
    .annotation runtime Landroid/annotation/SuppressLint;
        value = "Abi"
    .end annotation

    .prologue
    .line 333
    move-object v0, p0

    move-object v3, v0

    const-string v4, "lib/arm64"

    check-cast v4, Ljava/lang/CharSequence;

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 334
    const-string v3, "arm64"

    move-object v0, v3

    .line 342
    :goto_0
    return-object v0

    .line 336
    :cond_0
    move-object v3, v0

    const-string v4, "lib/arm"

    check-cast v4, Ljava/lang/CharSequence;

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 337
    const-string v3, "armv7"

    move-object v0, v3

    goto :goto_0

    .line 339
    :cond_1
    move-object v3, v0

    const-string v4, "lib/x86"

    check-cast v4, Ljava/lang/CharSequence;

    invoke-virtual {v3, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 340
    const-string v3, "x86"

    move-object v0, v3

    goto :goto_0

    .line 342
    :cond_2
    const-string v3, ""

    move-object v0, v3

    goto :goto_0
.end method

.method private getLayoutType()I
    .locals 6

    .prologue
    .line 217
    move-object v0, p0

    sget v4, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v5, 0x1a

    if-lt v4, v5, :cond_0

    .line 219
    const/16 v4, 0x7f6

    move v2, v4

    .line 227
    :goto_0
    move v4, v2

    move v0, v4

    return v0

    .line 220
    :cond_0
    sget v4, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v5, 0x18

    if-lt v4, v5, :cond_1

    .line 221
    const/16 v4, 0x7d2

    move v2, v4

    goto :goto_0

    .line 222
    :cond_1
    sget v4, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v5, 0x17

    if-lt v4, v5, :cond_2

    .line 223
    const/16 v4, 0x7d5

    move v2, v4

    goto :goto_0

    .line 225
    :cond_2
    const/16 v4, 0x7d3

    move v2, v4

    goto :goto_0
.end method

.method private initMenuButton(Landroid/view/View;Landroid/view/View;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 370
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    sget-object v4, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    new-instance v5, Lpmm/by/p2077kng/hacker/Loader$100000004;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Lpmm/by/p2077kng/hacker/Loader$100000004;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    invoke-virtual {v4, v5}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 376
    sget-object v4, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    new-instance v5, Lpmm/by/p2077kng/hacker/Loader$100000005;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Lpmm/by/p2077kng/hacker/Loader$100000005;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    invoke-virtual {v4, v5}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 381
    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Loader;->close:Landroid/widget/Button;

    new-instance v5, Lpmm/by/p2077kng/hacker/Loader$100000006;

    move-object v8, v5

    move-object v5, v8

    move-object v6, v8

    move-object v7, v0

    invoke-direct {v6, v7}, Lpmm/by/p2077kng/hacker/Loader$100000006;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    invoke-virtual {v4, v5}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method private m6dp(I)I
    .locals 6

    .prologue
    .line 1279
    move-object v0, p0

    move v1, p1

    const/4 v3, 0x1

    move v4, v1

    int-to-float v4, v4

    move-object v5, v0

    invoke-virtual {v5}, Lpmm/by/p2077kng/hacker/Loader;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    invoke-virtual {v5}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v5

    invoke-static {v3, v4, v5}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v3

    float-to-int v3, v3

    move v0, v3

    return v0
.end method

.method private onTouchListener()Landroid/view/View$OnTouchListener;
    .locals 6

    .prologue
    .line 652
    move-object v0, p0

    new-instance v2, Lpmm/by/p2077kng/hacker/Loader$100000007;

    move-object v5, v2

    move-object v2, v5

    move-object v3, v5

    move-object v4, v0

    invoke-direct {v3, v4}, Lpmm/by/p2077kng/hacker/Loader$100000007;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    move-object v0, v2

    return-object v0
.end method

.method private setCornerRadius(Landroid/graphics/drawable/GradientDrawable;F)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/graphics/drawable/GradientDrawable;",
            "F)V"
        }
    .end annotation

    .prologue
    .line 690
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v1

    move v5, v2

    invoke-virtual {v4, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    return-void
.end method


# virtual methods
.method public native AntLeech()V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation
.end method

.method public native Changes(II)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)V"
        }
    .end annotation
.end method

.method Destroy()V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 1288
    move-object v0, p0

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader;->mFloatingView:Landroid/view/View;

    move-object v2, v5

    .line 1289
    move-object v5, v2

    if-eqz v5, :cond_0

    .line 1290
    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    move-object v6, v2

    invoke-interface {v5, v6}, Landroid/view/WindowManager;->removeView(Landroid/view/View;)V

    .line 1291
    move-object v5, v0

    const/4 v6, 0x0

    check-cast v6, Landroid/view/View;

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/Loader;->mFloatingView:Landroid/view/View;

    .line 1293
    :cond_0
    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader;->overlayView:Lpmm/by/p2077kng/hacker/ESPView;

    move-object v3, v5

    .line 1294
    move-object v5, v3

    if-eqz v5, :cond_1

    .line 1295
    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Loader;->mWindowManager:Landroid/view/WindowManager;

    move-object v6, v3

    invoke-interface {v5, v6}, Landroid/view/WindowManager;->removeView(Landroid/view/View;)V

    .line 1296
    move-object v5, v0

    const/4 v6, 0x0

    check-cast v6, Lpmm/by/p2077kng/hacker/ESPView;

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/Loader;->overlayView:Lpmm/by/p2077kng/hacker/ESPView;

    .line 1298
    :cond_1
    const-string v5, "/dev/libBad.so"

    invoke-static {v5}, Lpmm/by/p2077kng/hacker/Loader;->accont(Ljava/lang/String;)V

    return-void
.end method

.method public Game(Ljava/lang/String;)I
    .locals 15

    .prologue
    .line 347
    move-object v0, p0

    move-object/from16 v1, p1

    :try_start_0
    new-instance v9, Ljava/util/ArrayList;

    move-object v14, v9

    move-object v9, v14

    move-object v10, v14

    invoke-direct {v10}, Ljava/util/ArrayList;-><init>()V

    move-object v3, v9

    .line 348
    sget-object v9, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-object v4, v9

    .line 349
    move-object v9, v4

    new-instance v10, Ljava/lang/StringBuffer;

    move-object v14, v10

    move-object v10, v14

    move-object v11, v14

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v11, Ljava/lang/StringBuffer;

    move-object v14, v11

    move-object v11, v14

    move-object v12, v14

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v12, "(toolbox ps; toolbox ps -A; toybox ps; toybox ps -A) | grep \" "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    move-object v12, v1

    invoke-virtual {v11, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, "$\""

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    move-object v11, v3

    const/4 v12, 0x0

    check-cast v12, Ljava/util/List;

    const/4 v13, 0x0

    invoke-virtual {v9, v10, v11, v12, v13}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I

    move-result v9

    .line 350
    move-object v9, v3

    invoke-virtual {v9}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v9

    move-object v5, v9

    .line 351
    :cond_0
    move-object v9, v5

    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    move-result v9

    if-nez v9, :cond_1

    .line 361
    const/4 v9, -0x1

    move v0, v9

    .line 364
    :goto_0
    return v0

    .line 352
    :cond_1
    move-object v9, v5

    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/String;

    invoke-virtual {v9}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v9

    move-object v6, v9

    .line 353
    :goto_1
    move-object v9, v6

    const-string v10, "  "

    invoke-virtual {v9, v10}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v9

    if-nez v9, :cond_2

    .line 356
    move-object v9, v6

    const-string v10, " "

    invoke-virtual {v9, v10}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v9

    move-object v7, v9

    .line 357
    move-object v9, v7

    array-length v9, v9

    const/4 v10, 0x2

    if-lt v9, v10, :cond_0

    .line 358
    move-object v9, v7

    const/4 v10, 0x1

    aget-object v9, v9, v10

    invoke-static {v9}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v9

    move v0, v9

    goto :goto_0

    .line 354
    :cond_2
    move-object v9, v6

    const-string v10, "  "

    const-string v11, " "

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;
    :try_end_0
    .catch Leu/chainfire/libsuperuser/Shell$ShellDiedException; {:try_start_0 .. :try_end_0} :catch_0

    move-result-object v9

    move-object v6, v9

    goto :goto_1

    .line 361
    :catch_0
    move-exception v9

    move-object v3, v9

    .line 363
    move-object v9, v3

    invoke-virtual {v9}, Leu/chainfire/libsuperuser/Shell$ShellDiedException;->printStackTrace()V

    .line 364
    const/4 v9, -0x1

    move v0, v9

    goto :goto_0
.end method

.method public Inject()V
    .locals 21
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 395
    move-object/from16 v0, p0

    :try_start_0
    const-string v12, "com.dts.freefireth"

    move-object v2, v12

    .line 397
    new-instance v12, Ljava/lang/StringBuffer;

    move-object/from16 v20, v12

    move-object/from16 v12, v20

    move-object/from16 v13, v20

    invoke-direct {v13}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v13, Ljava/lang/StringBuffer;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    invoke-direct {v14}, Ljava/lang/StringBuffer;-><init>()V

    move-object v14, v0

    invoke-virtual {v14}, Lpmm/by/p2077kng/hacker/Loader;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v14

    iget-object v14, v14, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    sget-object v14, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v12

    const-string v13, "libinject.so"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v12

    invoke-virtual {v12}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v12

    move-object v3, v12

    .line 398
    new-instance v12, Ljava/io/File;

    move-object/from16 v20, v12

    move-object/from16 v12, v20

    move-object/from16 v13, v20

    move-object v14, v3

    invoke-direct {v13, v14}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    const/4 v13, 0x1

    invoke-virtual {v12, v13}, Ljava/io/File;->setExecutable(Z)Z

    move-result v12

    .line 400
    new-instance v12, Ljava/lang/StringBuffer;

    move-object/from16 v20, v12

    move-object/from16 v12, v20

    move-object/from16 v13, v20

    invoke-direct {v13}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v13, Ljava/lang/StringBuffer;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    invoke-direct {v14}, Ljava/lang/StringBuffer;-><init>()V

    move-object v14, v0

    invoke-virtual {v14}, Lpmm/by/p2077kng/hacker/Loader;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v14

    iget-object v14, v14, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    sget-object v14, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v12

    const-string v13, "libserver.so"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v12

    invoke-virtual {v12}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v12

    move-object v4, v12

    .line 401
    const-string v12, "/dev/libserver.so"

    move-object v5, v12

    .line 403
    const-string v12, "u:object_r:system_lib_file:s0"

    move-object v6, v12

    .line 404
    new-instance v12, Ljava/util/ArrayList;

    move-object/from16 v20, v12

    move-object/from16 v12, v20

    move-object/from16 v13, v20

    invoke-direct {v13}, Ljava/util/ArrayList;-><init>()V

    move-object v7, v12

    .line 405
    sget-object v12, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    const-string v13, "ls -lZ /system/lib/libandroid_runtime.so"

    move-object v14, v7

    const/4 v15, 0x0

    check-cast v15, Ljava/util/List;

    const/16 v16, 0x0

    invoke-virtual/range {v12 .. v16}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;Ljava/util/List;Ljava/util/List;Z)I

    move-result v12

    .line 406
    move-object v12, v7

    check-cast v12, Ljava/util/Collection;

    invoke-interface {v12}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v12

    move-object v8, v12

    .line 409
    :cond_0
    :goto_0
    move-object v12, v8

    invoke-interface {v12}, Ljava/util/Iterator;->hasNext()Z

    move-result v12

    if-nez v12, :cond_1

    .line 413
    sget-object v12, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    const/4 v13, 0x2

    new-array v13, v13, [Ljava/lang/String;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    const/4 v15, 0x0

    new-instance v16, Ljava/lang/StringBuffer;

    move-object/from16 v20, v16

    move-object/from16 v16, v20

    move-object/from16 v17, v20

    invoke-direct/range {v17 .. v17}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v17, Ljava/lang/StringBuffer;

    move-object/from16 v20, v17

    move-object/from16 v17, v20

    move-object/from16 v18, v20

    invoke-direct/range {v18 .. v18}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v18, Ljava/lang/StringBuffer;

    move-object/from16 v20, v18

    move-object/from16 v18, v20

    move-object/from16 v19, v20

    invoke-direct/range {v19 .. v19}, Ljava/lang/StringBuffer;-><init>()V

    const-string v19, "cp "

    invoke-virtual/range {v18 .. v19}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v18

    move-object/from16 v19, v4

    invoke-virtual/range {v18 .. v19}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v18

    invoke-virtual/range {v18 .. v18}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v18

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v17

    const-string v18, " "

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v17

    invoke-virtual/range {v17 .. v17}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v17

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    move-object/from16 v17, v5

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    invoke-virtual/range {v16 .. v16}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v16

    aput-object v16, v14, v15

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    const/4 v15, 0x1

    new-instance v16, Ljava/lang/StringBuffer;

    move-object/from16 v20, v16

    move-object/from16 v16, v20

    move-object/from16 v17, v20

    invoke-direct/range {v17 .. v17}, Ljava/lang/StringBuffer;-><init>()V

    const-string v17, "chmod 777 "

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    move-object/from16 v17, v5

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    invoke-virtual/range {v16 .. v16}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v16

    aput-object v16, v14, v15

    invoke-virtual {v12, v13}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;)I

    move-result v12

    .line 414
    sget-object v12, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    const-string v13, "setenforce 0"

    invoke-virtual {v12, v13}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;)I

    move-result v12

    .line 416
    move-object v12, v0

    move-object v13, v2

    invoke-virtual {v12, v13}, Lpmm/by/p2077kng/hacker/Loader;->Game(Ljava/lang/String;)I

    move-result v12

    move v10, v12

    .line 417
    move v12, v10

    const/4 v13, 0x0

    if-ge v12, v13, :cond_2

    .line 447
    :goto_1
    return-void

    .line 406
    :cond_1
    move-object v12, v8

    invoke-interface {v12}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Ljava/lang/String;

    move-object v10, v12

    .line 407
    move-object v12, v10

    const-string v13, " u:object_r:"

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v12

    if-eqz v12, :cond_0

    move-object v12, v10

    const-string v13, ":s0 "

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v12

    if-eqz v12, :cond_0

    .line 408
    move-object v12, v10

    move-object v13, v10

    const-string v14, "u:object_r:"

    invoke-virtual {v13, v14}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v13

    invoke-virtual {v12, v13}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v12

    move-object v6, v12

    .line 409
    move-object v12, v6

    const/4 v13, 0x0

    move-object v14, v6

    const/16 v15, 0x20

    invoke-virtual {v14, v15}, Ljava/lang/String;->indexOf(I)I

    move-result v14

    invoke-virtual {v12, v13, v14}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v12

    move-object v6, v12

    goto/16 :goto_0

    .line 421
    :cond_2
    sget-object v12, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    const-string v13, "%s %d %s"

    const/4 v14, 0x3

    new-array v14, v14, [Ljava/lang/Object;

    move-object/from16 v20, v14

    move-object/from16 v14, v20

    move-object/from16 v15, v20

    const/16 v16, 0x0

    move-object/from16 v17, v3

    aput-object v17, v15, v16

    move-object/from16 v20, v14

    move-object/from16 v14, v20

    move-object/from16 v15, v20

    const/16 v16, 0x1

    move/from16 v17, v10

    invoke-static/range {v17 .. v17}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v17

    aput-object v17, v15, v16

    move-object/from16 v20, v14

    move-object/from16 v14, v20

    move-object/from16 v15, v20

    const/16 v16, 0x2

    move-object/from16 v17, v5

    aput-object v17, v15, v16

    invoke-static {v13, v14}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v13

    check-cast v13, Ljava/lang/Object;

    invoke-virtual {v12, v13}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;)I

    move-result v12

    .line 423
    invoke-static {}, Lpmm/by/p2077kng/hacker/Loader;->Init()I

    move-result v12

    const/4 v13, 0x0

    if-le v12, v13, :cond_6

    .line 424
    invoke-static {}, Lpmm/by/p2077kng/hacker/Loader;->Init()I

    move-result v12

    const/4 v13, 0x1

    if-ne v12, v13, :cond_3

    .line 425
    move-object v12, v0

    const-string v13, "client Create Failed"

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 427
    :cond_3
    invoke-static {}, Lpmm/by/p2077kng/hacker/Loader;->Init()I

    move-result v12

    const/4 v13, 0x2

    if-ne v12, v13, :cond_4

    .line 428
    move-object v12, v0

    const-string v13, "client Connect Failed"

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 430
    :cond_4
    invoke-static {}, Lpmm/by/p2077kng/hacker/Loader;->Init()I

    move-result v12

    const/4 v13, 0x3

    if-ne v12, v13, :cond_5

    .line 431
    move-object v12, v0

    const-string v13, "init Server Failed"

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 433
    :cond_5
    move-object v12, v0

    const-string v13, "Fails to Start"

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 434
    sget-object v12, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    new-instance v13, Ljava/lang/StringBuffer;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    invoke-direct {v14}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v14, Ljava/lang/StringBuffer;

    move-object/from16 v20, v14

    move-object/from16 v14, v20

    move-object/from16 v15, v20

    invoke-direct {v15}, Ljava/lang/StringBuffer;-><init>()V

    const-string v15, "<font face=\'roboto\'>"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    const-string v15, "Inject Failled"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    invoke-virtual {v14}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    const-string v14, "</font>"

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v13

    invoke-virtual {v12, v13}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 435
    sget-object v12, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const/high16 v13, -0x10000

    invoke-virtual {v12, v13}, Landroid/widget/Button;->setTextColor(I)V

    .line 436
    sget-object v12, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    const/4 v13, 0x1

    new-array v13, v13, [Ljava/lang/String;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    const/4 v15, 0x0

    new-instance v16, Ljava/lang/StringBuffer;

    move-object/from16 v20, v16

    move-object/from16 v16, v20

    move-object/from16 v17, v20

    invoke-direct/range {v17 .. v17}, Ljava/lang/StringBuffer;-><init>()V

    const-string v17, "rm -rf "

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    move-object/from16 v17, v5

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    invoke-virtual/range {v16 .. v16}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v16

    aput-object v16, v14, v15

    invoke-virtual {v12, v13}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;)I

    move-result v12

    .line 447
    :goto_2
    goto/16 :goto_1

    .line 438
    :cond_6
    move-object v12, v0

    const-string v13, "Started successfully"

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 439
    sget-object v12, Lpmm/by/p2077kng/hacker/Loader;->logs:Landroid/widget/Button;

    const/4 v13, 0x0

    invoke-virtual {v12, v13}, Landroid/widget/Button;->setVisibility(I)V

    .line 440
    sget-object v12, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const/16 v13, 0x8

    invoke-virtual {v12, v13}, Landroid/widget/Button;->setVisibility(I)V

    .line 441
    sget-object v12, Leu/chainfire/libsuperuser/Shell$Pool;->SU:Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    const/4 v13, 0x1

    new-array v13, v13, [Ljava/lang/String;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    const/4 v15, 0x0

    new-instance v16, Ljava/lang/StringBuffer;

    move-object/from16 v20, v16

    move-object/from16 v16, v20

    move-object/from16 v17, v20

    invoke-direct/range {v17 .. v17}, Ljava/lang/StringBuffer;-><init>()V

    const-string v17, "rm -rf "

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    move-object/from16 v17, v5

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v16

    invoke-virtual/range {v16 .. v16}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v16

    aput-object v16, v14, v15

    invoke-virtual {v12, v13}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;)I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    move-result v12

    goto :goto_2

    :catch_0
    move-exception v12

    move-object v2, v12

    .line 445
    move-object v12, v0

    const-string v13, "Inject Failled"

    const/4 v14, 0x0

    invoke-static {v12, v13, v14}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v12

    invoke-virtual {v12}, Landroid/widget/Toast;->show()V

    .line 446
    sget-object v12, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    new-instance v13, Ljava/lang/StringBuffer;

    move-object/from16 v20, v13

    move-object/from16 v13, v20

    move-object/from16 v14, v20

    invoke-direct {v14}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v14, Ljava/lang/StringBuffer;

    move-object/from16 v20, v14

    move-object/from16 v14, v20

    move-object/from16 v15, v20

    invoke-direct {v15}, Ljava/lang/StringBuffer;-><init>()V

    const-string v15, "<font face=\'roboto\'>"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    const-string v15, "Failled"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    invoke-virtual {v14}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    const-string v14, "</font>"

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-static {v13}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v13

    invoke-virtual {v12, v13}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 447
    sget-object v12, Lpmm/by/p2077kng/hacker/Loader;->Bypass:Landroid/widget/Button;

    const/high16 v13, -0x10000

    invoke-virtual {v12, v13}, Landroid/widget/Button;->setTextColor(I)V

    goto/16 :goto_2
.end method

.method public addButton(Ljava/lang/String;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V
    .locals 22
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1107
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    new-instance v12, Landroid/widget/TextView;

    move-object/from16 v21, v12

    move-object/from16 v12, v21

    move-object/from16 v13, v21

    move-object v14, v1

    invoke-direct {v13, v14}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v5, v12

    .line 1108
    new-instance v12, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v21, v12

    move-object/from16 v12, v21

    move-object/from16 v13, v21

    const/4 v14, -0x1

    move-object v15, v1

    const/16 v16, 0x23

    invoke-direct/range {v15 .. v16}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v15

    invoke-direct {v13, v14, v15}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object v6, v12

    .line 1109
    move-object v12, v6

    move-object v13, v1

    const/4 v14, 0x5

    invoke-direct {v13, v14}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v13

    move-object v14, v1

    const/4 v15, 0x2

    invoke-direct {v14, v15}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v14

    move-object v15, v1

    const/16 v16, 0x5

    invoke-direct/range {v15 .. v16}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v15

    move-object/from16 v16, v1

    const/16 v17, 0x2

    invoke-direct/range {v16 .. v17}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v16

    invoke-virtual/range {v12 .. v16}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 1110
    move-object v12, v5

    move-object v13, v6

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 1111
    move-object v12, v5

    move-object v13, v1

    invoke-virtual {v13}, Lpmm/by/p2077kng/hacker/Loader;->dipToPixels()F

    move-result v13

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setTextSize(F)V

    .line 1112
    move-object v12, v5

    const/4 v13, -0x1

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1113
    move-object v12, v5

    const/16 v13, 0x11

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setGravity(I)V

    .line 1114
    new-instance v12, Landroid/graphics/drawable/GradientDrawable;

    move-object/from16 v21, v12

    move-object/from16 v12, v21

    move-object/from16 v13, v21

    invoke-direct {v13}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v7, v12

    .line 1115
    move-object v12, v7

    const-string v13, "#555555"

    invoke-static {v13}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v13

    invoke-virtual {v12, v13}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 1116
    move-object v12, v7

    const/4 v13, 0x4

    int-to-float v13, v13

    invoke-virtual {v12, v13}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 1117
    sget v12, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v13, 0x15

    if-lt v12, v13, :cond_0

    .line 1118
    move-object v12, v5

    const/high16 v13, 0x42b40000    # 90.0f

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setElevation(F)V

    .line 1120
    :cond_0
    move-object v12, v7

    move-object v8, v12

    .line 1121
    new-instance v12, Landroid/graphics/drawable/RippleDrawable;

    move-object/from16 v21, v12

    move-object/from16 v12, v21

    move-object/from16 v13, v21

    new-instance v14, Landroid/content/res/ColorStateList;

    move-object/from16 v21, v14

    move-object/from16 v14, v21

    move-object/from16 v15, v21

    const/16 v16, 0x1

    move/from16 v0, v16

    new-array v0, v0, [[I

    move-object/from16 v16, v0

    move-object/from16 v21, v16

    move-object/from16 v16, v21

    move-object/from16 v17, v21

    const/16 v18, 0x0

    const/16 v19, 0x0

    move/from16 v0, v19

    new-array v0, v0, [I

    move-object/from16 v19, v0

    aput-object v19, v17, v18

    const/16 v17, 0x1

    move/from16 v0, v17

    new-array v0, v0, [I

    move-object/from16 v17, v0

    move-object/from16 v21, v17

    move-object/from16 v17, v21

    move-object/from16 v18, v21

    const/16 v19, 0x0

    const-string v20, "#696969"

    invoke-static/range {v20 .. v20}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v20

    aput v20, v18, v19

    invoke-direct/range {v15 .. v17}, Landroid/content/res/ColorStateList;-><init>([[I[I)V

    move-object v15, v7

    const/16 v16, 0x0

    check-cast v16, Landroid/graphics/drawable/Drawable;

    invoke-direct/range {v13 .. v16}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v9, v12

    .line 1122
    move-object v12, v8

    sget-object v13, Landroid/graphics/drawable/GradientDrawable$Orientation;->TL_BR:Landroid/graphics/drawable/GradientDrawable$Orientation;

    invoke-virtual {v12, v13}, Landroid/graphics/drawable/GradientDrawable;->setOrientation(Landroid/graphics/drawable/GradientDrawable$Orientation;)V

    .line 1123
    move-object v12, v5

    move-object v13, v9

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1124
    move-object v12, v5

    const/4 v13, 0x0

    check-cast v13, Landroid/graphics/Typeface;

    const/4 v14, 0x1

    invoke-virtual {v12, v13, v14}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 1125
    move-object v12, v2

    const-string v13, ""

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v12

    if-eqz v12, :cond_1

    .line 1126
    move-object v12, v2

    const-string v13, ""

    const-string v14, ""

    invoke-virtual {v12, v13, v14}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v12

    move-object v2, v12

    .line 1127
    move-object v12, v5

    new-instance v13, Ljava/lang/StringBuffer;

    move-object/from16 v21, v13

    move-object/from16 v13, v21

    move-object/from16 v14, v21

    invoke-direct {v14}, Ljava/lang/StringBuffer;-><init>()V

    move-object v14, v2

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    const-string v14, ""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1128
    move-object v12, v2

    move-object v10, v12

    .line 1129
    move-object v12, v5

    new-instance v13, Lpmm/by/p2077kng/hacker/Loader$100000022;

    move-object/from16 v21, v13

    move-object/from16 v13, v21

    move-object/from16 v14, v21

    move-object v15, v1

    move-object/from16 v16, v3

    move-object/from16 v17, v5

    move-object/from16 v18, v10

    invoke-direct/range {v14 .. v18}, Lpmm/by/p2077kng/hacker/Loader$100000022;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;Landroid/widget/TextView;Ljava/lang/String;)V

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 1172
    :goto_0
    move-object v12, v1

    iget-object v12, v12, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object v13, v5

    invoke-virtual {v12, v13}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void

    .line 1161
    :cond_1
    move-object v12, v5

    move-object v13, v2

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1163
    move-object v12, v2

    move-object v10, v12

    .line 1164
    move-object v12, v5

    move-object v13, v8

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1165
    move-object v12, v5

    new-instance v13, Lpmm/by/p2077kng/hacker/Loader$100000023;

    move-object/from16 v21, v13

    move-object/from16 v13, v21

    move-object/from16 v14, v21

    move-object v15, v1

    move-object/from16 v16, v3

    invoke-direct/range {v14 .. v16}, Lpmm/by/p2077kng/hacker/Loader$100000023;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V

    invoke-virtual {v12, v13}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    goto :goto_0
.end method

.method public addButtonpmm(Ljava/lang/String;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1176
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    new-instance v9, Landroid/widget/TextView;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    move-object v11, v0

    invoke-direct {v10, v11}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v4, v9

    .line 1177
    new-instance v9, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v16, v9

    move-object/from16 v9, v16

    move-object/from16 v10, v16

    const/4 v11, -0x1

    move-object v12, v0

    const/16 v13, 0x23

    invoke-direct {v12, v13}, Lpmm/by/p2077kng/hacker/Loader;->dp(I)I

    move-result v12

    invoke-direct {v10, v11, v12}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    move-object v5, v9

    .line 1178
    move-object v9, v5

    const/4 v10, 0x2

    const/4 v11, 0x2

    const/4 v12, 0x2

    const/4 v13, 0x2

    invoke-virtual {v9, v10, v11, v12, v13}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 1179
    move-object v9, v4

    move-object v10, v5

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 1180
    const/16 v9, 0xc

    int-to-float v9, v9

    move v6, v9

    .line 1181
    move-object v9, v4

    const/4 v10, 0x2

    move v11, v6

    invoke-virtual {v9, v10, v11}, Landroid/widget/TextView;->setTextSize(IF)V

    .line 1182
    move-object v9, v4

    const/4 v10, -0x1

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setTextColor(I)V

    .line 1183
    move-object v9, v4

    const/16 v10, 0x11

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setGravity(I)V

    .line 1184
    move-object v9, v4

    const/4 v10, 0x1

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setAllCaps(Z)V

    .line 1185
    move-object v9, v4

    move-object v10, v0

    invoke-virtual {v10}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v10

    invoke-static {v10}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fuckass(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v10

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1186
    move-object v9, v4

    const/4 v10, 0x0

    check-cast v10, Landroid/graphics/Typeface;

    const/4 v11, 0x1

    invoke-virtual {v9, v10, v11}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 1187
    move-object v9, v1

    const-string v10, ""

    invoke-virtual {v9, v10}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v9

    if-eqz v9, :cond_0

    .line 1188
    move-object v9, v1

    const-string v10, ""

    const-string v11, ""

    invoke-virtual {v9, v10, v11}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v9

    move-object v1, v9

    .line 1189
    move-object v9, v4

    new-instance v10, Ljava/lang/StringBuffer;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    invoke-direct {v11}, Ljava/lang/StringBuffer;-><init>()V

    move-object v11, v1

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v11, " OFF"

    invoke-virtual {v10, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1190
    move-object v9, v1

    move-object v7, v9

    .line 1191
    move-object v9, v4

    new-instance v10, Lpmm/by/p2077kng/hacker/Loader$100000024;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v0

    move-object v13, v2

    move-object v14, v4

    move-object v15, v7

    invoke-direct {v11, v12, v13, v14, v15}, Lpmm/by/p2077kng/hacker/Loader$100000024;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;Landroid/widget/TextView;Ljava/lang/String;)V

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 1217
    :goto_0
    move-object v9, v0

    iget-object v9, v9, Lpmm/by/p2077kng/hacker/Loader;->patches:Landroid/widget/LinearLayout;

    move-object v10, v4

    invoke-virtual {v9, v10}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    return-void

    .line 1207
    :cond_0
    move-object v9, v4

    move-object v10, v1

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 1209
    move-object v9, v1

    move-object v7, v9

    .line 1210
    move-object v9, v4

    move-object v10, v0

    invoke-virtual {v10}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v10

    invoke-static {v10}, Lpmm/by/p2077kng/hacker/P2077KNG;->Fuckass(Landroid/content/Context;)Landroid/graphics/drawable/GradientDrawable;

    move-result-object v10

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 1211
    move-object v9, v4

    new-instance v10, Lpmm/by/p2077kng/hacker/Loader$100000025;

    move-object/from16 v16, v10

    move-object/from16 v10, v16

    move-object/from16 v11, v16

    move-object v12, v0

    move-object v13, v2

    invoke-direct {v11, v12, v13}, Lpmm/by/p2077kng/hacker/Loader$100000025;-><init>(Lpmm/by/p2077kng/hacker/Loader;Lpmm/by/p2077kng/hacker/Loader$InterfaceBtn;)V

    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    goto :goto_0
.end method

.method public dipToPixels()F
    .locals 5

    .prologue
    .line 1285
    move-object v0, p0

    const/4 v2, 0x1

    const/high16 v3, 0x40a00000    # 5.0f

    move-object v4, v0

    invoke-virtual {v4}, Lpmm/by/p2077kng/hacker/Loader;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    invoke-virtual {v4}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v4

    invoke-static {v2, v3, v4}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v2

    move v0, v2

    return v0
.end method

.method public isViewCollapsed()Z
    .locals 4

    .prologue
    .line 1253
    move-object v0, p0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Loader;->frameLayout:Landroid/widget/FrameLayout;

    if-eqz v2, :cond_0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Loader;->collapse_view:Landroid/widget/RelativeLayout;

    invoke-virtual {v2}, Landroid/widget/RelativeLayout;->getVisibility()I

    move-result v2

    const/4 v3, 0x0

    if-eq v2, v3, :cond_0

    const/4 v2, 0x0

    :goto_0
    move v0, v2

    return v0

    :cond_0
    const/4 v2, 0x1

    goto :goto_0
.end method

.method public lambda$initFloating$0$FloatingViewService(Landroid/view/View;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1225
    move-object v0, p0

    move-object v1, p1

    new-instance v6, Landroid/app/AlertDialog$Builder;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    move-object v8, v0

    invoke-virtual {v8}, Lpmm/by/p2077kng/hacker/Loader;->getBaseContext()Landroid/content/Context;

    move-result-object v8

    invoke-direct {v7, v8}, Landroid/app/AlertDialog$Builder;-><init>(Landroid/content/Context;)V

    const-string v7, "\n\tDo you want to close?"

    invoke-virtual {v6, v7}, Landroid/app/AlertDialog$Builder;->setMessage(Ljava/lang/CharSequence;)Landroid/app/AlertDialog$Builder;

    move-result-object v6

    const-string v7, "OK"

    new-instance v8, Lpmm/by/p2077kng/hacker/Loader$100000026;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    move-object v10, v0

    invoke-direct {v9, v10}, Lpmm/by/p2077kng/hacker/Loader$100000026;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    invoke-virtual {v6, v7, v8}, Landroid/app/AlertDialog$Builder;->setPositiveButton(Ljava/lang/CharSequence;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v6

    const-string v7, "Cancel"

    new-instance v8, Lpmm/by/p2077kng/hacker/Loader$100000027;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    move-object v10, v0

    invoke-direct {v9, v10}, Lpmm/by/p2077kng/hacker/Loader$100000027;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    invoke-virtual {v6, v7, v8}, Landroid/app/AlertDialog$Builder;->setNegativeButton(Ljava/lang/CharSequence;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/AlertDialog$Builder;

    move-result-object v6

    invoke-virtual {v6}, Landroid/app/AlertDialog$Builder;->create()Landroid/app/AlertDialog;

    move-result-object v6

    move-object v4, v6

    .line 1236
    sget v6, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v7, 0x19

    if-le v6, v7, :cond_0

    .line 1237
    const/16 v6, 0x7f6

    move v3, v6

    .line 1241
    :goto_0
    move-object v6, v4

    invoke-virtual {v6}, Landroid/app/AlertDialog;->getWindow()Landroid/view/Window;

    move-result-object v6

    move v7, v3

    invoke-virtual {v6, v7}, Landroid/view/Window;->setType(I)V

    .line 1242
    move-object v6, v4

    invoke-virtual {v6}, Landroid/app/AlertDialog;->show()V

    return-void

    .line 1239
    :cond_0
    const/16 v6, 0x7d2

    move v3, v6

    goto :goto_0
.end method

.method public lambda$initFloating$1$FloatingViewService(Landroid/view/View;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1247
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader;->collapse_view:Landroid/widget/RelativeLayout;

    const/4 v4, 0x0

    invoke-virtual {v3, v4}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 1248
    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Loader;->mExpanded:Landroid/widget/LinearLayout;

    const/16 v4, 0x8

    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    return-void
.end method

.method public onBind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 4

    .prologue
    .line 206
    move-object v0, p0

    move-object v1, p1

    const/4 v3, 0x0

    check-cast v3, Landroid/os/IBinder;

    move-object v0, v3

    return-object v0
.end method

.method public onCreate()V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    move-object v0, p0

    move-object v2, v0

    const-string v3, "com.aide.ui.crustacean"

    invoke-static {v2, v3}, Ladrt/ADRTLogCatReader;->onContext(Landroid/content/Context;Ljava/lang/String;)V

    .line 247
    move-object v2, v0

    invoke-super {v2}, Landroid/app/Service;->onCreate()V

    .line 248
    move-object v2, v0

    new-instance v3, Lpmm/by/p2077kng/hacker/ESPView;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v5, v0

    check-cast v5, Landroid/content/Context;

    invoke-direct {v4, v5}, Lpmm/by/p2077kng/hacker/ESPView;-><init>(Landroid/content/Context;)V

    iput-object v3, v2, Lpmm/by/p2077kng/hacker/Loader;->overlayView:Lpmm/by/p2077kng/hacker/ESPView;

    .line 249
    const-string v2, "P2077KNG"

    invoke-static {v2}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 250
    move-object v2, v0

    invoke-direct {v2}, Lpmm/by/p2077kng/hacker/Loader;->FlutuanteService()V

    .line 251
    move-object v2, v0

    invoke-direct {v2}, Lpmm/by/p2077kng/hacker/Loader;->ThreadStatus()V

    .line 252
    move-object v2, v0

    invoke-direct {v2}, Lpmm/by/p2077kng/hacker/Loader;->DrawCanvas()V

    .line 253
    move-object v2, v0

    invoke-direct {v2}, Lpmm/by/p2077kng/hacker/Loader;->Running()V

    .line 254
    move-object v2, v0

    invoke-direct {v2}, Lpmm/by/p2077kng/hacker/Loader;->CreateMenuList()V

    .line 255
    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Loader;->mViewImage3:Landroid/widget/ImageView;

    new-instance v3, Lpmm/by/p2077kng/hacker/Loader$100000002;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v5, v0

    invoke-direct {v4, v5}, Lpmm/by/p2077kng/hacker/Loader$100000002;-><init>(Lpmm/by/p2077kng/hacker/Loader;)V

    invoke-virtual {v2, v3}, Landroid/widget/ImageView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public onTaskRemoved(Landroid/content/Intent;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Intent;",
            ")V"
        }
    .end annotation

    .prologue
    .line 1302
    move-object v1, p0

    move-object v2, p1

    move-object v6, v1

    move-object v7, v2

    invoke-super {v6, v7}, Landroid/app/Service;->onTaskRemoved(Landroid/content/Intent;)V

    .line 1304
    const-wide/16 v6, 0x64

    :try_start_0
    invoke-static {v6, v7}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1308
    :goto_0
    move-object v6, v1

    invoke-virtual {v6}, Lpmm/by/p2077kng/hacker/Loader;->stopSelf()V

    return-void

    .line 1304
    :catch_0
    move-exception v6

    move-object v4, v6

    .line 1306
    move-object v6, v4

    invoke-virtual {v6}, Ljava/lang/InterruptedException;->printStackTrace()V

    goto :goto_0
.end method

.method public setParams()Landroid/view/WindowManager$LayoutParams;
    .locals 9

    .prologue
    .line 304
    move-object v0, p0

    new-instance v4, Landroid/view/WindowManager$LayoutParams;

    move-object v8, v4

    move-object v4, v8

    move-object v5, v8

    const/4 v6, -0x1

    const/4 v7, -0x1

    invoke-direct {v5, v6, v7}, Landroid/view/WindowManager$LayoutParams;-><init>(II)V

    move-object v2, v4

    .line 305
    move-object v4, v2

    const/16 v5, 0x10

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 306
    move-object v4, v2

    const/16 v5, 0x2000

    iput v5, v4, Landroid/view/WindowManager$LayoutParams;->flags:I

    .line 307
    move-object v4, v2

    move-object v0, v4

    return-object v0
.end method
