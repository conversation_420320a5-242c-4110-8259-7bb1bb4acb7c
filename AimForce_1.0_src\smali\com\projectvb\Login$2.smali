.class Lcom/projectvb/Login$2;
.super Ljava/lang/Object;
.source "Login.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/projectvb/Login;->Init()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/projectvb/Login;

.field final synthetic val$inputLicenseKey:Landroid/widget/EditText;

.field final synthetic val$loginButton:Landroid/widget/Button;


# direct methods
.method constructor <init>(Lcom/projectvb/Login;Landroid/widget/EditText;Landroid/widget/Button;)V
    .locals 0
    .param p1, "this$0"    # Lcom/projectvb/Login;

    .line 141
    iput-object p1, p0, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    iput-object p2, p0, Lcom/projectvb/Login$2;->val$inputLicenseKey:Landroid/widget/EditText;

    iput-object p3, p0, Lcom/projectvb/Login$2;->val$loginButton:Landroid/widget/Button;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 3
    .param p1, "v"    # Landroid/view/View;

    .line 144
    iget-object v0, p0, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v0}, Lcom/projectvb/Login;->access$200(Lcom/projectvb/Login;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 145
    iget-object v0, p0, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    const-string v1, "Session not initialized yet. Please wait..."

    invoke-static {v0, v1}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V

    .line 146
    return-void

    .line 148
    :cond_0
    iget-object v0, p0, Lcom/projectvb/Login$2;->val$inputLicenseKey:Landroid/widget/EditText;

    invoke-virtual {v0}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v0

    .line 150
    .local v0, "licenseKey":Ljava/lang/String;
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 151
    iget-object v1, p0, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    const-string v2, "Please enter a license key"

    invoke-static {v1, v2}, Lcom/projectvb/Login;->access$400(Lcom/projectvb/Login;Ljava/lang/String;)V

    .line 152
    return-void

    .line 155
    :cond_1
    iget-object v1, p0, Lcom/projectvb/Login$2;->this$0:Lcom/projectvb/Login;

    invoke-static {v1}, Lcom/projectvb/Login;->access$500(Lcom/projectvb/Login;)Landroid/widget/ProgressBar;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 156
    iget-object v1, p0, Lcom/projectvb/Login$2;->val$loginButton:Landroid/widget/Button;

    invoke-virtual {v1, v2}, Landroid/widget/Button;->setEnabled(Z)V

    .line 158
    new-instance v1, Ljava/lang/Thread;

    new-instance v2, Lcom/projectvb/Login$2$1;

    invoke-direct {v2, p0, v0}, Lcom/projectvb/Login$2$1;-><init>(Lcom/projectvb/Login$2;Ljava/lang/String;)V

    invoke-direct {v1, v2}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 208
    invoke-virtual {v1}, Ljava/lang/Thread;->start()V

    .line 209
    return-void
.end method
