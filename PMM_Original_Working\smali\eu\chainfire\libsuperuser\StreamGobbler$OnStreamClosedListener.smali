.class public interface abstract Leu/chainfire/libsuperuser/StreamGobbler$OnStreamClosedListener;
.super Ljava/lang/Object;
.source "StreamGobbler.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leu/chainfire/libsuperuser/StreamGobbler;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnStreamClosedListener"
.end annotation


# virtual methods
.method public abstract onStreamClosed()V
.end method
