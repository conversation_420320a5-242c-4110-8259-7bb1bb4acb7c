.class public Leu/chainfire/libsuperuser/Shell;
.super Ljava/lang/Object;
.source "Shell.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Leu/chainfire/libsuperuser/Shell$Pool;,
        Leu/chainfire/libsuperuser/Shell$PoolWrapper;,
        Leu/chainfire/libsuperuser/Shell$ThreadedAutoCloseable;,
        Leu/chainfire/libsuperuser/Shell$Threaded;,
        Leu/chainfire/libsuperuser/Shell$Interactive;,
        Leu/chainfire/libsuperuser/Shell$SyncCommands;,
        Leu/chainfire/libsuperuser/Shell$DeprecatedSyncCommands;,
        Leu/chainfire/libsuperuser/Shell$OnSyncCommandInputStreamListener;,
        Leu/chainfire/libsuperuser/Shell$OnSyncCommandLineListener;,
        Leu/chainfire/libsuperuser/Shell$Builder;,
        Leu/chainfire/libsuperuser/Shell$Command;,
        Leu/chainfire/libsuperuser/Shell$OnCommandInputStreamListener;,
        Leu/chainfire/libsuperuser/Shell$OnCommandInputStream;,
        Leu/chainfire/libsuperuser/Shell$OnCommandLineListener;,
        Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;,
        Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;,
        Leu/chainfire/libsuperuser/Shell$OnCommandResultListenerUnbuffered;,
        Leu/chainfire/libsuperuser/Shell$OnCommandResultListener2;,
        Leu/chainfire/libsuperuser/Shell$OnCommandResultListener;,
        Leu/chainfire/libsuperuser/Shell$OnShellOpenResultListener;,
        Leu/chainfire/libsuperuser/Shell$OnResult;,
        Leu/chainfire/libsuperuser/Shell$SU;,
        Leu/chainfire/libsuperuser/Shell$SH;,
        Leu/chainfire/libsuperuser/Shell$ShellDiedException;,
        Leu/chainfire/libsuperuser/Shell$ShellNotClosedException;,
        Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;
    }
.end annotation


# static fields
.field protected static final availableTestCommands:[Ljava/lang/String;

.field private static volatile redirectDeprecated:Z


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .prologue
    .line 97
    const/4 v0, 0x1

    sput-boolean v0, Leu/chainfire/libsuperuser/Shell;->redirectDeprecated:Z

    .line 289
    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/String;

    move-object v4, v0

    move-object v0, v4

    move-object v1, v4

    const/4 v2, 0x0

    const-string v3, "echo -BOC-"

    aput-object v3, v1, v2

    move-object v4, v0

    move-object v0, v4

    move-object v1, v4

    const/4 v2, 0x1

    const-string v3, "id"

    aput-object v3, v1, v2

    sput-object v0, Leu/chainfire/libsuperuser/Shell;->availableTestCommands:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .prologue
    .line 56
    move-object v0, p0

    move-object v1, v0

    invoke-direct {v1}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$000()Z
    .locals 1

    .prologue
    .line 56
    sget-boolean v0, Leu/chainfire/libsuperuser/Shell;->redirectDeprecated:Z

    return v0
.end method

.method public static isRedirectDeprecated()Z
    .locals 1

    .prologue
    .line 105
    sget-boolean v0, Leu/chainfire/libsuperuser/Shell;->redirectDeprecated:Z

    return v0
.end method

.method protected static parseAvailableResult(Ljava/util/List;Z)Z
    .locals 7
    .param p0    # Ljava/util/List;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;Z)Z"
        }
    .end annotation

    .prologue
    .line 302
    move-object v0, p0

    move v1, p1

    move-object v5, v0

    if-nez v5, :cond_0

    .line 303
    const/4 v5, 0x0

    move v0, v5

    .line 320
    :goto_0
    return v0

    .line 306
    :cond_0
    const/4 v5, 0x0

    move v2, v5

    .line 308
    move-object v5, v0

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    move-object v3, v5

    :goto_1
    move-object v5, v3

    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_5

    move-object v5, v3

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    move-object v4, v5

    .line 309
    move-object v5, v4

    const-string v6, "uid="

    invoke-virtual {v5, v6}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_3

    .line 311
    move v5, v1

    if-eqz v5, :cond_1

    move-object v5, v4

    const-string v6, "uid=0"

    invoke-virtual {v5, v6}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_2

    :cond_1
    const/4 v5, 0x1

    :goto_2
    move v0, v5

    goto :goto_0

    :cond_2
    const/4 v5, 0x0

    goto :goto_2

    .line 312
    :cond_3
    move-object v5, v4

    const-string v6, "-BOC-"

    invoke-virtual {v5, v6}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_4

    .line 316
    const/4 v5, 0x1

    move v2, v5

    .line 318
    :cond_4
    goto :goto_1

    .line 320
    :cond_5
    move v5, v2

    move v0, v5

    goto :goto_0
.end method

.method public static run(Ljava/lang/String;[Ljava/lang/String;Z)Ljava/util/List;
    .locals 7
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # [Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "[",
            "Ljava/lang/String;",
            "Z)",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 138
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, v0

    move-object v4, v1

    const/4 v5, 0x0

    move v6, v2

    invoke-static {v3, v4, v5, v6}, Leu/chainfire/libsuperuser/Shell;->run(Ljava/lang/String;[Ljava/lang/String;[Ljava/lang/String;Z)Ljava/util/List;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method public static run(Ljava/lang/String;[Ljava/lang/String;[Ljava/lang/String;Z)Ljava/util/List;
    .locals 23
    .param p0    # Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # [Ljava/lang/String;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # [Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation build Landroidx/annotation/WorkerThread;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "[",
            "Ljava/lang/String;",
            "[",
            "Ljava/lang/String;",
            "Z)",
            "Ljava/util/List",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .prologue
    .line 184
    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    move/from16 v4, p3

    move-object v15, v1

    sget-object v16, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual/range {v15 .. v16}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v15

    move-object v5, v15

    .line 186
    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->getSanityChecksEnabledEffective()Z

    move-result v15

    if-eqz v15, :cond_0

    invoke-static {}, Leu/chainfire/libsuperuser/Debug;->onMainThread()Z

    move-result v15

    if-eqz v15, :cond_0

    .line 191
    const-string v15, "Application attempted to run a shell command from the main thread"

    invoke-static {v15}, Leu/chainfire/libsuperuser/Debug;->log(Ljava/lang/String;)V

    .line 192
    new-instance v15, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;

    move-object/from16 v22, v15

    move-object/from16 v15, v22

    move-object/from16 v16, v22

    const-string v17, "Application attempted to run a shell command from the main thread"

    invoke-direct/range {v16 .. v17}, Leu/chainfire/libsuperuser/Shell$ShellOnMainThreadException;-><init>(Ljava/lang/String;)V

    throw v15

    .line 195
    :cond_0
    sget-boolean v15, Leu/chainfire/libsuperuser/Shell;->redirectDeprecated:Z

    if-eqz v15, :cond_1

    .line 197
    move-object v15, v1

    invoke-static {v15}, Leu/chainfire/libsuperuser/Shell$Pool;->getWrapper(Ljava/lang/String;)Leu/chainfire/libsuperuser/Shell$PoolWrapper;

    move-result-object v15

    move-object/from16 v16, v2

    move-object/from16 v17, v3

    move/from16 v18, v4

    invoke-virtual/range {v15 .. v18}, Leu/chainfire/libsuperuser/Shell$PoolWrapper;->run(Ljava/lang/Object;[Ljava/lang/String;Z)Ljava/util/List;

    move-result-object v15

    move-object v1, v15

    .line 286
    :goto_0
    return-object v1

    .line 200
    :cond_1
    sget-object v15, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v16, "[%s%%] START"

    const/16 v17, 0x1

    move/from16 v0, v17

    new-array v0, v0, [Ljava/lang/Object;

    move-object/from16 v17, v0

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    const/16 v19, 0x0

    move-object/from16 v20, v5

    aput-object v20, v18, v19

    invoke-static/range {v15 .. v17}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Leu/chainfire/libsuperuser/Debug;->logCommand(Ljava/lang/String;)V

    .line 202
    new-instance v15, Ljava/util/ArrayList;

    move-object/from16 v22, v15

    move-object/from16 v15, v22

    move-object/from16 v16, v22

    invoke-direct/range {v16 .. v16}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v15}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v15

    move-object v6, v15

    .line 206
    move-object v15, v3

    if-eqz v15, :cond_4

    .line 207
    :try_start_0
    new-instance v15, Ljava/util/HashMap;

    move-object/from16 v22, v15

    move-object/from16 v15, v22

    move-object/from16 v16, v22

    invoke-static {}, Ljava/lang/System;->getenv()Ljava/util/Map;

    move-result-object v17

    invoke-direct/range {v16 .. v17}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    move-object v7, v15

    .line 209
    move-object v15, v3

    move-object v9, v15

    move-object v15, v9

    array-length v15, v15

    move v10, v15

    const/4 v15, 0x0

    move v11, v15

    :goto_1
    move v15, v11

    move/from16 v16, v10

    move/from16 v0, v16

    if-ge v15, v0, :cond_3

    move-object v15, v9

    move/from16 v16, v11

    aget-object v15, v15, v16

    move-object v12, v15

    .line 210
    move-object v15, v12

    const-string v16, "="

    invoke-virtual/range {v15 .. v16}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v15

    move/from16 v22, v15

    move/from16 v15, v22

    move/from16 v16, v22

    move/from16 v8, v16

    if-ltz v15, :cond_2

    .line 211
    move-object v15, v7

    move-object/from16 v16, v12

    const/16 v17, 0x0

    move/from16 v18, v8

    invoke-virtual/range {v16 .. v18}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v16

    move-object/from16 v17, v12

    move/from16 v18, v8

    const/16 v19, 0x1

    add-int/lit8 v18, v18, 0x1

    invoke-virtual/range {v17 .. v18}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v17

    invoke-interface/range {v15 .. v17}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v15

    .line 209
    :cond_2
    add-int/lit8 v11, v11, 0x1

    goto :goto_1

    .line 214
    :cond_3
    const/4 v15, 0x0

    move v9, v15

    .line 215
    move-object v15, v7

    invoke-interface {v15}, Ljava/util/Map;->size()I

    move-result v15

    new-array v15, v15, [Ljava/lang/String;

    move-object v3, v15

    .line 216
    move-object v15, v7

    invoke-interface {v15}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v15

    invoke-interface {v15}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v15

    move-object v10, v15

    :goto_2
    move-object v15, v10

    invoke-interface {v15}, Ljava/util/Iterator;->hasNext()Z

    move-result v15

    if-eqz v15, :cond_4

    move-object v15, v10

    invoke-interface {v15}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v15

    check-cast v15, Ljava/util/Map$Entry;

    move-object v11, v15

    .line 217
    move-object v15, v3

    move/from16 v16, v9

    new-instance v17, Ljava/lang/StringBuilder;

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    invoke-direct/range {v18 .. v18}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v18, v11

    invoke-interface/range {v18 .. v18}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v18

    check-cast v18, Ljava/lang/String;

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    const-string v18, "="

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    move-object/from16 v18, v11

    invoke-interface/range {v18 .. v18}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v18

    check-cast v18, Ljava/lang/String;

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    invoke-virtual/range {v17 .. v17}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v17

    aput-object v17, v15, v16

    .line 218
    add-int/lit8 v9, v9, 0x1

    .line 219
    goto :goto_2

    .line 224
    :cond_4
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v15

    move-object/from16 v16, v1

    move-object/from16 v17, v3

    invoke-virtual/range {v15 .. v17}, Ljava/lang/Runtime;->exec(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/Process;

    move-result-object v15

    move-object v7, v15

    .line 225
    new-instance v15, Ljava/io/DataOutputStream;

    move-object/from16 v22, v15

    move-object/from16 v15, v22

    move-object/from16 v16, v22

    move-object/from16 v17, v7

    invoke-virtual/range {v17 .. v17}, Ljava/lang/Process;->getOutputStream()Ljava/io/OutputStream;

    move-result-object v17

    invoke-direct/range {v16 .. v17}, Ljava/io/DataOutputStream;-><init>(Ljava/io/OutputStream;)V

    move-object v8, v15

    .line 226
    new-instance v15, Leu/chainfire/libsuperuser/StreamGobbler;

    move-object/from16 v22, v15

    move-object/from16 v15, v22

    move-object/from16 v16, v22

    new-instance v17, Ljava/lang/StringBuilder;

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    invoke-direct/range {v18 .. v18}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v18, v5

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    const-string v18, "-"

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    invoke-virtual/range {v17 .. v17}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v17

    move-object/from16 v18, v7

    invoke-virtual/range {v18 .. v18}, Ljava/lang/Process;->getInputStream()Ljava/io/InputStream;

    move-result-object v18

    move-object/from16 v19, v6

    invoke-direct/range {v16 .. v19}, Leu/chainfire/libsuperuser/StreamGobbler;-><init>(Ljava/lang/String;Ljava/io/InputStream;Ljava/util/List;)V

    move-object v9, v15

    .line 228
    new-instance v15, Leu/chainfire/libsuperuser/StreamGobbler;

    move-object/from16 v22, v15

    move-object/from16 v15, v22

    move-object/from16 v16, v22

    new-instance v17, Ljava/lang/StringBuilder;

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    invoke-direct/range {v18 .. v18}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v18, v5

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    const-string v18, "*"

    invoke-virtual/range {v17 .. v18}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v17

    invoke-virtual/range {v17 .. v17}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v17

    move-object/from16 v18, v7

    invoke-virtual/range {v18 .. v18}, Ljava/lang/Process;->getErrorStream()Ljava/io/InputStream;

    move-result-object v18

    move/from16 v19, v4

    if-eqz v19, :cond_5

    move-object/from16 v19, v6

    :goto_3
    invoke-direct/range {v16 .. v19}, Leu/chainfire/libsuperuser/StreamGobbler;-><init>(Ljava/lang/String;Ljava/io/InputStream;Ljava/util/List;)V

    move-object v10, v15

    .line 232
    move-object v15, v9

    invoke-virtual {v15}, Leu/chainfire/libsuperuser/StreamGobbler;->start()V

    .line 233
    move-object v15, v10

    invoke-virtual {v15}, Leu/chainfire/libsuperuser/StreamGobbler;->start()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_3

    .line 235
    move-object v15, v2

    move-object v11, v15

    move-object v15, v11

    :try_start_1
    array-length v15, v15

    move v12, v15

    const/4 v15, 0x0

    move v13, v15

    :goto_4
    move v15, v13

    move/from16 v16, v12

    move/from16 v0, v16

    if-ge v15, v0, :cond_6

    move-object v15, v11

    move/from16 v16, v13

    aget-object v15, v15, v16

    move-object v14, v15

    .line 236
    sget-object v15, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v16, "[%s+] %s"

    const/16 v17, 0x2

    move/from16 v0, v17

    new-array v0, v0, [Ljava/lang/Object;

    move-object/from16 v17, v0

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    const/16 v19, 0x0

    move-object/from16 v20, v5

    aput-object v20, v18, v19

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    const/16 v19, 0x1

    move-object/from16 v20, v14

    aput-object v20, v18, v19

    invoke-static/range {v15 .. v17}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Leu/chainfire/libsuperuser/Debug;->logCommand(Ljava/lang/String;)V

    .line 237
    move-object v15, v8

    new-instance v16, Ljava/lang/StringBuilder;

    move-object/from16 v22, v16

    move-object/from16 v16, v22

    move-object/from16 v17, v22

    invoke-direct/range {v17 .. v17}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v17, v14

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v16

    const-string v17, "\n"

    invoke-virtual/range {v16 .. v17}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v16

    invoke-virtual/range {v16 .. v16}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v16

    const-string v17, "UTF-8"

    invoke-virtual/range {v16 .. v17}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v16

    invoke-virtual/range {v15 .. v16}, Ljava/io/DataOutputStream;->write([B)V

    .line 238
    move-object v15, v8

    invoke-virtual {v15}, Ljava/io/DataOutputStream;->flush()V

    .line 235
    add-int/lit8 v13, v13, 0x1

    goto :goto_4

    .line 228
    :cond_5
    const/16 v19, 0x0

    goto :goto_3

    .line 240
    :cond_6
    move-object v15, v8

    const-string v16, "exit\n"

    const-string v17, "UTF-8"

    invoke-virtual/range {v16 .. v17}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v16

    invoke-virtual/range {v15 .. v16}, Ljava/io/DataOutputStream;->write([B)V

    .line 241
    move-object v15, v8

    invoke-virtual {v15}, Ljava/io/DataOutputStream;->flush()V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_3

    .line 257
    :cond_7
    :goto_5
    move-object v15, v7

    :try_start_2
    invoke-virtual {v15}, Ljava/lang/Process;->waitFor()I
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_3

    move-result v15

    .line 265
    move-object v15, v8

    :try_start_3
    invoke-virtual {v15}, Ljava/io/DataOutputStream;->close()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_2
    .catch Ljava/lang/InterruptedException; {:try_start_3 .. :try_end_3} :catch_3

    .line 269
    :goto_6
    move-object v15, v9

    :try_start_4
    invoke-virtual {v15}, Leu/chainfire/libsuperuser/StreamGobbler;->join()V

    .line 270
    move-object v15, v10

    invoke-virtual {v15}, Leu/chainfire/libsuperuser/StreamGobbler;->join()V

    .line 271
    move-object v15, v7

    invoke-virtual {v15}, Ljava/lang/Process;->destroy()V

    .line 274
    move-object v15, v1

    invoke-static {v15}, Leu/chainfire/libsuperuser/Shell$SU;->isSU(Ljava/lang/String;)Z

    move-result v15

    if-eqz v15, :cond_8

    move-object v15, v7

    invoke-virtual {v15}, Ljava/lang/Process;->exitValue()I
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_4 .. :try_end_4} :catch_3

    move-result v15

    const/16 v16, 0xff

    move/from16 v0, v16

    if-ne v15, v0, :cond_8

    .line 275
    const/4 v15, 0x0

    move-object v6, v15

    .line 285
    :cond_8
    :goto_7
    sget-object v15, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const-string v16, "[%s%%] END"

    const/16 v17, 0x1

    move/from16 v0, v17

    new-array v0, v0, [Ljava/lang/Object;

    move-object/from16 v17, v0

    move-object/from16 v22, v17

    move-object/from16 v17, v22

    move-object/from16 v18, v22

    const/16 v19, 0x0

    move-object/from16 v20, v1

    sget-object v21, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual/range {v20 .. v21}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v20

    aput-object v20, v18, v19

    invoke-static/range {v15 .. v17}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v15

    invoke-static {v15}, Leu/chainfire/libsuperuser/Debug;->logCommand(Ljava/lang/String;)V

    .line 286
    move-object v15, v6

    move-object v1, v15

    goto/16 :goto_0

    .line 242
    :catch_0
    move-exception v15

    move-object v11, v15

    .line 243
    move-object v15, v11

    :try_start_5
    invoke-virtual {v15}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v15

    const-string v16, "EPIPE"

    invoke-virtual/range {v15 .. v16}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v15

    if-nez v15, :cond_7

    move-object v15, v11

    invoke-virtual {v15}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v15

    const-string v16, "Stream closed"

    invoke-virtual/range {v15 .. v16}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v15

    if-eqz v15, :cond_9

    goto :goto_5

    .line 251
    :cond_9
    move-object v15, v11

    throw v15
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_1
    .catch Ljava/lang/InterruptedException; {:try_start_5 .. :try_end_5} :catch_3

    .line 277
    :catch_1
    move-exception v15

    move-object v7, v15

    .line 279
    const/4 v15, 0x0

    move-object v6, v15

    .line 283
    goto :goto_7

    .line 266
    :catch_2
    move-exception v15

    move-object v11, v15

    goto :goto_6

    .line 280
    :catch_3
    move-exception v15

    move-object v7, v15

    .line 282
    const/4 v15, 0x0

    move-object v6, v15

    goto :goto_7
.end method

.method public static setRedirectDeprecated(Z)V
    .locals 2

    .prologue
    .line 116
    move v0, p0

    move v1, v0

    sput-boolean v1, Leu/chainfire/libsuperuser/Shell;->redirectDeprecated:Z

    .line 117
    return-void
.end method
