.class Leu/chainfire/libsuperuser/Shell$Threaded$1;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Threaded;->onClosed()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Threaded;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Threaded;)V
    .locals 4

    .prologue
    .line 2846
    move-object v0, p0

    move-object v1, p1

    move-object v2, v0

    move-object v3, v1

    iput-object v3, v2, Leu/chainfire/libsuperuser/Shell$Threaded$1;->this$0:Leu/chainfire/libsuperuser/Shell$Threaded;

    move-object v2, v0

    invoke-direct {v2}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 9

    .prologue
    .line 2850
    move-object v1, p0

    move-object v4, v1

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded$1;->this$0:Leu/chainfire/libsuperuser/Shell$Threaded;

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->callbackSync:Ljava/lang/Object;

    move-object v8, v4

    move-object v4, v8

    move-object v5, v8

    move-object v2, v5

    monitor-enter v4

    .line 2851
    move-object v4, v1

    :try_start_0
    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded$1;->this$0:Leu/chainfire/libsuperuser/Shell$Threaded;

    iget v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->callbacks:I

    if-lez v4, :cond_0

    .line 2853
    move-object v4, v1

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded$1;->this$0:Leu/chainfire/libsuperuser/Shell$Threaded;

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded;->handler:Landroid/os/Handler;

    move-object v5, v1

    const-wide/16 v6, 0x3e8

    invoke-virtual {v4, v5, v6, v7}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    move-result v4

    .line 2861
    :goto_0
    move-object v4, v2

    monitor-exit v4

    .line 2862
    return-void

    .line 2855
    :cond_0
    sget v4, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v5, 0x12

    if-lt v4, v5, :cond_1

    .line 2856
    move-object v4, v1

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded$1;->this$0:Leu/chainfire/libsuperuser/Shell$Threaded;

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$4800(Leu/chainfire/libsuperuser/Shell$Threaded;)Landroid/os/HandlerThread;

    move-result-object v4

    invoke-virtual {v4}, Landroid/os/HandlerThread;->quitSafely()Z

    move-result v4

    goto :goto_0

    .line 2858
    :cond_1
    move-object v4, v1

    iget-object v4, v4, Leu/chainfire/libsuperuser/Shell$Threaded$1;->this$0:Leu/chainfire/libsuperuser/Shell$Threaded;

    invoke-static {v4}, Leu/chainfire/libsuperuser/Shell$Threaded;->access$4800(Leu/chainfire/libsuperuser/Shell$Threaded;)Landroid/os/HandlerThread;

    move-result-object v4

    invoke-virtual {v4}, Landroid/os/HandlerThread;->quit()Z

    move-result v4

    goto :goto_0

    .line 2861
    :catchall_0
    move-exception v4

    move-object v3, v4

    move-object v4, v2

    monitor-exit v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    move-object v4, v3

    throw v4
.end method
