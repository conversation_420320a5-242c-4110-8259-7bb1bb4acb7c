.class Lcom/bad/modder/injector/InjectorService$100000007;
.super Ljava/lang/Object;
.source "InjectorService.java"

# interfaces
.implements Lcom/bad/modder/injector/InjectorService$InterfaceInt;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bad/modder/injector/InjectorService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000007"
.end annotation


# instance fields
.field private final this$0:Lcom/bad/modder/injector/InjectorService;

.field private final val$feature:I


# direct methods
.method constructor <init>(Lcom/bad/modder/injector/InjectorService;I)V
    .locals 6

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    move-object v4, v0

    move-object v5, v1

    iput-object v5, v4, Lcom/bad/modder/injector/InjectorService$100000007;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v4, v0

    move v5, v2

    iput v5, v4, Lcom/bad/modder/injector/InjectorService$100000007;->val$feature:I

    return-void
.end method

.method static access$0(Lcom/bad/modder/injector/InjectorService$100000007;)Lcom/bad/modder/injector/InjectorService;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000007;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public OnWrite(I)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .prologue
    .line 659
    move-object v0, p0

    move v1, p1

    move-object v3, v0

    iget-object v3, v3, Lcom/bad/modder/injector/InjectorService$100000007;->this$0:Lcom/bad/modder/injector/InjectorService;

    move-object v4, v0

    iget v4, v4, Lcom/bad/modder/injector/InjectorService$100000007;->val$feature:I

    move v5, v1

    invoke-virtual {v3, v4, v5}, Lcom/bad/modder/injector/InjectorService;->Changes(II)V

    return-void
.end method
