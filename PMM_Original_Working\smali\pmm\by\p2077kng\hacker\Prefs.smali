.class public Lpmm/by/p2077kng/hacker/Prefs;
.super Ljava/lang/Object;
.source "Prefs.java"


# static fields
.field private static final DEFAULT_BOOLEAN_VALUE:Z = false

.field private static final DEFAULT_DOUBLE_VALUE:D = -1.0

.field private static final DEFAULT_FLOAT_VALUE:F = -1.0f

.field private static final DEFAULT_INT_VALUE:I = -0x1

.field private static final DEFAULT_LONG_VALUE:J = -0x1L

.field private static final DEFAULT_STRING_VALUE:Ljava/lang/String; = ""

.field private static final LENGTH:Ljava/lang/String; = "_length"

.field private static prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;


# instance fields
.field private sharedPreferences:Landroid/content/SharedPreferences;


# direct methods
.method constructor <init>(Landroid/content/Context;)V
    .locals 8

    .prologue
    .line 20
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    invoke-direct {v3}, Ljava/lang/Object;-><init>()V

    .line 21
    move-object v3, v0

    move-object v4, v1

    invoke-virtual {v4}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v4

    new-instance v5, Ljava/lang/StringBuffer;

    move-object v7, v5

    move-object v5, v7

    move-object v6, v7

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    move-object v6, v1

    invoke-virtual {v6}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    const-string v6, "_preferences"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    const/4 v6, 0x0

    invoke-virtual {v4, v5, v6}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v4

    iput-object v4, v3, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    return-void
.end method

.method constructor <init>(Landroid/content/Context;Ljava/lang/String;)V
    .locals 8

    .prologue
    .line 27
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    invoke-direct {v4}, Ljava/lang/Object;-><init>()V

    .line 28
    move-object v4, v0

    move-object v5, v1

    invoke-virtual {v5}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v5

    move-object v6, v2

    const/4 v7, 0x0

    invoke-virtual {v5, v6, v7}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v5

    iput-object v5, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    return-void
.end method

.method public static with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;
    .locals 7

    .prologue
    .line 35
    move-object v0, p0

    sget-object v3, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    if-nez v3, :cond_0

    .line 36
    new-instance v3, Lpmm/by/p2077kng/hacker/Prefs;

    move-object v6, v3

    move-object v3, v6

    move-object v4, v6

    move-object v5, v0

    invoke-direct {v4, v5}, Lpmm/by/p2077kng/hacker/Prefs;-><init>(Landroid/content/Context;)V

    sput-object v3, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    .line 38
    :cond_0
    sget-object v3, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    move-object v0, v3

    return-object v0
.end method

.method public static with(Landroid/content/Context;Ljava/lang/String;)Lpmm/by/p2077kng/hacker/Prefs;
    .locals 9

    .prologue
    .line 49
    move-object v0, p0

    move-object v1, p1

    sget-object v4, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    if-nez v4, :cond_0

    .line 50
    new-instance v4, Lpmm/by/p2077kng/hacker/Prefs;

    move-object v8, v4

    move-object v4, v8

    move-object v5, v8

    move-object v6, v0

    move-object v7, v1

    invoke-direct {v5, v6, v7}, Lpmm/by/p2077kng/hacker/Prefs;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    sput-object v4, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    .line 52
    :cond_0
    sget-object v4, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    move-object v0, v4

    return-object v0
.end method

.method public static with(Landroid/content/Context;Ljava/lang/String;Z)Lpmm/by/p2077kng/hacker/Prefs;
    .locals 10

    .prologue
    .line 57
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move v5, v2

    if-eqz v5, :cond_0

    .line 58
    new-instance v5, Lpmm/by/p2077kng/hacker/Prefs;

    move-object v9, v5

    move-object v5, v9

    move-object v6, v9

    move-object v7, v0

    move-object v8, v1

    invoke-direct {v6, v7, v8}, Lpmm/by/p2077kng/hacker/Prefs;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    sput-object v5, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    .line 60
    :cond_0
    sget-object v5, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    move-object v0, v5

    return-object v0
.end method

.method public static with(Landroid/content/Context;Z)Lpmm/by/p2077kng/hacker/Prefs;
    .locals 8

    .prologue
    .line 42
    move-object v0, p0

    move v1, p1

    move v4, v1

    if-eqz v4, :cond_0

    .line 43
    new-instance v4, Lpmm/by/p2077kng/hacker/Prefs;

    move-object v7, v4

    move-object v4, v7

    move-object v5, v7

    move-object v6, v0

    invoke-direct {v5, v6}, Lpmm/by/p2077kng/hacker/Prefs;-><init>(Landroid/content/Context;)V

    sput-object v4, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    .line 45
    :cond_0
    sget-object v4, Lpmm/by/p2077kng/hacker/Prefs;->prefsInstance:Lpmm/by/p2077kng/hacker/Prefs;

    move-object v0, v4

    return-object v0
.end method


# virtual methods
.method public clear()V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .prologue
    .line 182
    move-object v0, p0

    move-object v2, v0

    iget-object v2, v2, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v2}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v2

    invoke-interface {v2}, Landroid/content/SharedPreferences$Editor;->clear()Landroid/content/SharedPreferences$Editor;

    move-result-object v2

    invoke-interface {v2}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method public contains(Ljava/lang/String;)Z
    .locals 5

    .prologue
    .line 178
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v4, v1

    invoke-interface {v3, v4}, Landroid/content/SharedPreferences;->contains(Ljava/lang/String;)Z

    move-result v3

    move v0, v3

    return v0
.end method

.method public getStringSet(Ljava/lang/String;Ljava/util/Set;)Ljava/util/Set;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Set",
            "<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/util/Set",
            "<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .prologue
    .line 158
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v5, v1

    move-object v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences;->getStringSet(Ljava/lang/String;Ljava/util/Set;)Ljava/util/Set;

    move-result-object v4

    move-object v0, v4

    return-object v0
.end method

.method public putStringSet(Ljava/lang/String;Ljava/util/Set;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Set",
            "<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .prologue
    .line 154
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v4}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    move-object v5, v1

    move-object v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences$Editor;->putStringSet(Ljava/lang/String;Ljava/util/Set;)Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    invoke-interface {v4}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method public read(Ljava/lang/String;)Ljava/lang/String;
    .locals 6

    .prologue
    .line 66
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v4, v1

    const-string v5, ""

    invoke-interface {v3, v4, v5}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    move-object v0, v3

    return-object v0
.end method

.method public read(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 7

    .prologue
    .line 70
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v5, v1

    move-object v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    move-object v0, v4

    return-object v0
.end method

.method public readBoolean(Ljava/lang/String;)Z
    .locals 6

    .prologue
    .line 140
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    move-object v4, v1

    const/4 v5, 0x0

    invoke-virtual {v3, v4, v5}, Lpmm/by/p2077kng/hacker/Prefs;->readBoolean(Ljava/lang/String;Z)Z

    move-result v3

    move v0, v3

    return v0
.end method

.method public readBoolean(Ljava/lang/String;Z)Z
    .locals 7

    .prologue
    .line 144
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v5, v1

    move v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences;->getBoolean(Ljava/lang/String;Z)Z

    move-result v4

    move v0, v4

    return v0
.end method

.method public readDouble(Ljava/lang/String;)D
    .locals 6

    .prologue
    .line 94
    move-object v1, p0

    move-object v2, p1

    move-object v4, v1

    move-object v5, v2

    invoke-virtual {v4, v5}, Lpmm/by/p2077kng/hacker/Prefs;->contains(Ljava/lang/String;)Z

    move-result v4

    if-nez v4, :cond_0

    .line 95
    const-wide/high16 v4, -0x4010000000000000L    # -1.0

    move-wide v1, v4

    .line 96
    :goto_0
    return-wide v1

    :cond_0
    move-object v4, v1

    move-object v5, v2

    invoke-virtual {v4, v5}, Lpmm/by/p2077kng/hacker/Prefs;->readLong(Ljava/lang/String;)J

    move-result-wide v4

    invoke-static {v4, v5}, Ljava/lang/Double;->longBitsToDouble(J)D

    move-result-wide v4

    move-wide v1, v4

    goto :goto_0
.end method

.method public readDouble(Ljava/lang/String;D)D
    .locals 8

    .prologue
    .line 100
    move-object v1, p0

    move-object v2, p1

    move-wide v3, p2

    move-object v6, v1

    move-object v7, v2

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/Prefs;->contains(Ljava/lang/String;)Z

    move-result v6

    if-nez v6, :cond_0

    .line 101
    move-wide v6, v3

    move-wide v1, v6

    .line 102
    :goto_0
    return-wide v1

    :cond_0
    move-object v6, v1

    move-object v7, v2

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/Prefs;->readLong(Ljava/lang/String;)J

    move-result-wide v6

    invoke-static {v6, v7}, Ljava/lang/Double;->longBitsToDouble(J)D

    move-result-wide v6

    move-wide v1, v6

    goto :goto_0
.end method

.method public readFloat(Ljava/lang/String;)F
    .locals 6

    .prologue
    .line 112
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v4, v1

    const/high16 v5, -0x40800000    # -1.0f

    invoke-interface {v3, v4, v5}, Landroid/content/SharedPreferences;->getFloat(Ljava/lang/String;F)F

    move-result v3

    move v0, v3

    return v0
.end method

.method public readFloat(Ljava/lang/String;F)F
    .locals 7

    .prologue
    .line 116
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v5, v1

    move v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences;->getFloat(Ljava/lang/String;F)F

    move-result v4

    move v0, v4

    return v0
.end method

.method public readInt(Ljava/lang/String;)I
    .locals 6

    .prologue
    .line 80
    move-object v0, p0

    move-object v1, p1

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v4, v1

    const/4 v5, -0x1

    invoke-interface {v3, v4, v5}, Landroid/content/SharedPreferences;->getInt(Ljava/lang/String;I)I

    move-result v3

    move v0, v3

    return v0
.end method

.method public readInt(Ljava/lang/String;I)I
    .locals 7

    .prologue
    .line 84
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v5, v1

    move v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences;->getInt(Ljava/lang/String;I)I

    move-result v4

    move v0, v4

    return v0
.end method

.method public readLong(Ljava/lang/String;)J
    .locals 8

    .prologue
    .line 126
    move-object v1, p0

    move-object v2, p1

    move-object v4, v1

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v5, v2

    const-wide/16 v6, -0x1

    invoke-interface {v4, v5, v6, v7}, Landroid/content/SharedPreferences;->getLong(Ljava/lang/String;J)J

    move-result-wide v4

    move-wide v1, v4

    return-wide v1
.end method

.method public readLong(Ljava/lang/String;J)J
    .locals 10

    .prologue
    .line 130
    move-object v0, p0

    move-object v1, p1

    move-wide v2, p2

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    move-object v6, v1

    move-wide v7, v2

    invoke-interface {v5, v6, v7, v8}, Landroid/content/SharedPreferences;->getLong(Ljava/lang/String;J)J

    move-result-wide v5

    move-wide v0, v5

    return-wide v0
.end method

.method public remove(Ljava/lang/String;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 164
    move-object v0, p0

    move-object v1, p1

    move-object v6, v0

    new-instance v7, Ljava/lang/StringBuffer;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    move-object v8, v1

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "_length"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/Prefs;->contains(Ljava/lang/String;)Z

    move-result v6

    if-eqz v6, :cond_0

    .line 166
    move-object v6, v0

    new-instance v7, Ljava/lang/StringBuffer;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    move-object v8, v1

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "_length"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/Prefs;->readInt(Ljava/lang/String;)I

    move-result v6

    move v3, v6

    .line 167
    move v6, v3

    const/4 v7, 0x0

    if-lt v6, v7, :cond_0

    .line 168
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v6}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v6

    new-instance v7, Ljava/lang/StringBuffer;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    move-object v8, v1

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "_length"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-interface {v6, v7}, Landroid/content/SharedPreferences$Editor;->remove(Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v6

    invoke-interface {v6}, Landroid/content/SharedPreferences$Editor;->apply()V

    .line 169
    const/4 v6, 0x0

    move v4, v6

    :goto_0
    move v6, v4

    move v7, v3

    if-lt v6, v7, :cond_1

    .line 174
    :cond_0
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v6}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v6

    move-object v7, v1

    invoke-interface {v6, v7}, Landroid/content/SharedPreferences$Editor;->remove(Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v6

    invoke-interface {v6}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void

    .line 170
    :cond_1
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v6}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v6

    new-instance v7, Ljava/lang/StringBuffer;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v8, Ljava/lang/StringBuffer;

    move-object v11, v8

    move-object v8, v11

    move-object v9, v11

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    new-instance v9, Ljava/lang/StringBuffer;

    move-object v11, v9

    move-object v9, v11

    move-object v10, v11

    invoke-direct {v10}, Ljava/lang/StringBuffer;-><init>()V

    move-object v10, v1

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    const-string v10, "["

    invoke-virtual {v9, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    move v9, v4

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "]"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-interface {v6, v7}, Landroid/content/SharedPreferences$Editor;->remove(Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v6

    invoke-interface {v6}, Landroid/content/SharedPreferences$Editor;->apply()V

    .line 169
    add-int/lit8 v4, v4, 0x1

    goto :goto_0
.end method

.method public write(Ljava/lang/String;Ljava/lang/String;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .prologue
    .line 74
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v4}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    move-object v5, v1

    move-object v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    invoke-interface {v4}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method public writeBoolean(Ljava/lang/String;Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Z)V"
        }
    .end annotation

    .prologue
    .line 148
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v4}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    move-object v5, v1

    move v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences$Editor;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    invoke-interface {v4}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method public writeDouble(Ljava/lang/String;D)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "D)V"
        }
    .end annotation

    .prologue
    .line 106
    move-object v1, p0

    move-object v2, p1

    move-wide v3, p2

    move-object v6, v1

    move-object v7, v2

    move-wide v8, v3

    invoke-static {v8, v9}, Ljava/lang/Double;->doubleToRawLongBits(D)J

    move-result-wide v8

    invoke-virtual {v6, v7, v8, v9}, Lpmm/by/p2077kng/hacker/Prefs;->writeLong(Ljava/lang/String;J)V

    return-void
.end method

.method public writeFloat(Ljava/lang/String;F)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "F)V"
        }
    .end annotation

    .prologue
    .line 120
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v4}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    move-object v5, v1

    move v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences$Editor;->putFloat(Ljava/lang/String;F)Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    invoke-interface {v4}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method public writeInt(Ljava/lang/String;I)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    .prologue
    .line 88
    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v4, v0

    iget-object v4, v4, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v4}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    move-object v5, v1

    move v6, v2

    invoke-interface {v4, v5, v6}, Landroid/content/SharedPreferences$Editor;->putInt(Ljava/lang/String;I)Landroid/content/SharedPreferences$Editor;

    move-result-object v4

    invoke-interface {v4}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method

.method public writeLong(Ljava/lang/String;J)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J)V"
        }
    .end annotation

    .prologue
    .line 134
    move-object v0, p0

    move-object v1, p1

    move-wide v2, p2

    move-object v5, v0

    iget-object v5, v5, Lpmm/by/p2077kng/hacker/Prefs;->sharedPreferences:Landroid/content/SharedPreferences;

    invoke-interface {v5}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v5

    move-object v6, v1

    move-wide v7, v2

    invoke-interface {v5, v6, v7, v8}, Landroid/content/SharedPreferences$Editor;->putLong(Ljava/lang/String;J)Landroid/content/SharedPreferences$Editor;

    move-result-object v5

    invoke-interface {v5}, Landroid/content/SharedPreferences$Editor;->apply()V

    return-void
.end method
