.class Leu/chainfire/libsuperuser/Shell$Interactive$4;
.super Ljava/lang/Object;
.source "Shell.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Leu/chainfire/libsuperuser/Shell$Interactive;->processLine(Ljava/lang/String;Ljava/lang/Object;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

.field final synthetic val$isSTDERR:Z

.field final synthetic val$line:Ljava/lang/String;

.field final synthetic val$listener:Ljava/lang/Object;


# direct methods
.method constructor <init>(Leu/chainfire/libsuperuser/Shell$Interactive;Ljava/lang/Object;Ljava/lang/String;Z)V
    .locals 7

    .prologue
    .line 1927
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v4, p4

    move-object v5, v0

    move-object v6, v1

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$4;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    move-object v5, v0

    move-object v6, v2

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    move-object v5, v0

    move-object v6, v3

    iput-object v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$line:Ljava/lang/String;

    move-object v5, v0

    move v6, v4

    iput-boolean v6, v5, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$isSTDERR:Z

    move-object v5, v0

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    .prologue
    .line 1931
    move-object v0, p0

    move-object v2, v0

    :try_start_0
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    instance-of v2, v2, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    if-eqz v2, :cond_1

    .line 1932
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    check-cast v2, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$line:Ljava/lang/String;

    invoke-interface {v2, v3}, Leu/chainfire/libsuperuser/StreamGobbler$OnLineListener;->onLine(Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1939
    :cond_0
    :goto_0
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->endCallback()V

    .line 1941
    return-void

    .line 1933
    :cond_1
    move-object v2, v0

    :try_start_1
    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    instance-of v2, v2, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;

    if-eqz v2, :cond_2

    move-object v2, v0

    iget-boolean v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$isSTDERR:Z

    if-nez v2, :cond_2

    .line 1934
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    check-cast v2, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$line:Ljava/lang/String;

    invoke-interface {v2, v3}, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDOUT;->onSTDOUT(Ljava/lang/String;)V

    goto :goto_0

    .line 1935
    :cond_2
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    instance-of v2, v2, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;

    if-eqz v2, :cond_0

    move-object v2, v0

    iget-boolean v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$isSTDERR:Z

    if-eqz v2, :cond_0

    .line 1936
    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$listener:Ljava/lang/Object;

    check-cast v2, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;

    move-object v3, v0

    iget-object v3, v3, Leu/chainfire/libsuperuser/Shell$Interactive$4;->val$line:Ljava/lang/String;

    invoke-interface {v2, v3}, Leu/chainfire/libsuperuser/Shell$OnCommandLineSTDERR;->onSTDERR(Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    .line 1939
    :catchall_0
    move-exception v2

    move-object v1, v2

    move-object v2, v0

    iget-object v2, v2, Leu/chainfire/libsuperuser/Shell$Interactive$4;->this$0:Leu/chainfire/libsuperuser/Shell$Interactive;

    invoke-virtual {v2}, Leu/chainfire/libsuperuser/Shell$Interactive;->endCallback()V

    .line 1940
    move-object v2, v1

    throw v2
.end method
