.class Lpmm/by/p2077kng/hacker/MainActivity$100000004;
.super Ljava/lang/Object;
.source "MainActivity.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lpmm/by/p2077kng/hacker/MainActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x20
    name = "100000004"
.end annotation


# instance fields
.field private final this$0:Lpmm/by/p2077kng/hacker/MainActivity;

.field private final val$editText:Landroid/widget/EditText;

.field private final val$editText2:Landroid/widget/EditText;


# direct methods
.method constructor <init>(Lpmm/by/p2077kng/hacker/MainActivity;Landroid/widget/EditText;Landroid/widget/EditText;)V
    .locals 7

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, v0

    invoke-direct {v5}, Ljava/lang/Object;-><init>()V

    move-object v5, v0

    move-object v6, v1

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    move-object v5, v0

    move-object v6, v2

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->val$editText:Landroid/widget/EditText;

    move-object v5, v0

    move-object v6, v3

    iput-object v6, v5, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->val$editText2:Landroid/widget/EditText;

    return-void
.end method

.method static access$0(Lpmm/by/p2077kng/hacker/MainActivity$100000004;)Lpmm/by/p2077kng/hacker/MainActivity;
    .locals 4

    move-object v0, p0

    move-object v3, v0

    iget-object v3, v3, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    move-object v0, v3

    return-object v0
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .prologue
    .line 294
    move-object v0, p0

    move-object v1, p1

    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->val$editText:Landroid/widget/EditText;

    invoke-virtual {v6}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object v6

    invoke-interface {v6}, Landroid/text/Editable;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v6

    move-object v3, v6

    .line 295
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->val$editText2:Landroid/widget/EditText;

    invoke-virtual {v6}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object v6

    invoke-interface {v6}, Landroid/text/Editable;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v6

    move-object v4, v6

    .line 296
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v6

    const-string v7, "USER_Sv"

    move-object v8, v3

    invoke-virtual {v6, v7, v8}, Lpmm/by/p2077kng/hacker/Prefs;->write(Ljava/lang/String;Ljava/lang/String;)V

    .line 297
    move-object v6, v0

    iget-object v6, v6, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-static {v6}, Lpmm/by/p2077kng/hacker/Prefs;->with(Landroid/content/Context;)Lpmm/by/p2077kng/hacker/Prefs;

    move-result-object v6

    const-string v7, "PASS_Sv"

    move-object v8, v4

    invoke-virtual {v6, v7, v8}, Lpmm/by/p2077kng/hacker/Prefs;->write(Ljava/lang/String;Ljava/lang/String;)V

    .line 299
    new-instance v6, Lpmm/by/p2077kng/hacker/Load;

    move-object v11, v6

    move-object v6, v11

    move-object v7, v11

    move-object v8, v0

    iget-object v8, v8, Lpmm/by/p2077kng/hacker/MainActivity$100000004;->this$0:Lpmm/by/p2077kng/hacker/MainActivity;

    invoke-direct {v7, v8}, Lpmm/by/p2077kng/hacker/Load;-><init>(Lpmm/by/p2077kng/hacker/MainActivity;)V

    const/4 v7, 0x2

    new-array v7, v7, [Ljava/lang/String;

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    const/4 v9, 0x0

    move-object v10, v3

    aput-object v10, v8, v9

    move-object v11, v7

    move-object v7, v11

    move-object v8, v11

    const/4 v9, 0x1

    move-object v10, v4

    aput-object v10, v8, v9

    invoke-virtual {v6, v7}, Lpmm/by/p2077kng/hacker/Load;->execute([Ljava/lang/Object;)Landroid/os/AsyncTask;

    move-result-object v6

    return-void
.end method
